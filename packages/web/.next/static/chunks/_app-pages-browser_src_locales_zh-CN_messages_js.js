"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_zh-CN_messages_js"],{

/***/ "(app-pages-browser)/./src/locales/zh-CN/messages.js":
/*!***************************************!*\
  !*** ./src/locales/zh-CN/messages.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*eslint-disable*/ \nmodule.exports = {\n    messages: JSON.parse(\"{\\\"app.title\\\":[\\\"OnlyRules - AI 提示词管理平台\\\"],\\\"app.description\\\":[\\\"为您喜爱的 IDE 创建、整理和分享 AI 提示词规则。通过社区驱动的模板提升您的编码效率。\\\"],\\\"nav.home\\\":[\\\"首页\\\"],\\\"nav.templates\\\":[\\\"模板\\\"],\\\"nav.dashboard\\\":[\\\"控制台\\\"],\\\"nav.tutorials\\\":[\\\"教程\\\"],\\\"hero.title\\\":[\\\"用 AI 提示词为您的 IDE 赋能\\\"],\\\"hero.subtitle\\\":[\\\"发现、创建和分享强大的 AI 提示词规则，适用于 Cursor、Augment Code、Windsurf、Claude、GitHub Copilot、Gemini 等。加入优化编码工作流程的开发者社区。\\\"],\\\"hero.getStarted\\\":[\\\"开始使用\\\"],\\\"hero.browseTemplates\\\":[\\\"浏览模板\\\"],\\\"features.title\\\":[\\\"AI 驱动编码所需的一切\\\"],\\\"features.subtitle\\\":[\\\"从提示词模板到版本控制，我们为您提供全方位支持\\\"],\\\"auth.signIn\\\":[\\\"登录\\\"],\\\"auth.signUp\\\":[\\\"注册\\\"],\\\"auth.signOut\\\":[\\\"退出登录\\\"]}\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9sb2NhbGVzL3poLUNOL21lc3NhZ2VzLmpzIiwibWFwcGluZ3MiOiJBQUFBLGdCQUFnQjtBQUFFQSxPQUFPQyxPQUFPLEdBQUM7SUFBQ0MsVUFBU0MsS0FBS0MsS0FBSyxDQUFDO0FBQWtuQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL3NyYy9sb2NhbGVzL3poLUNOL21lc3NhZ2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qZXNsaW50LWRpc2FibGUqL21vZHVsZS5leHBvcnRzPXttZXNzYWdlczpKU09OLnBhcnNlKFwie1xcXCJhcHAudGl0bGVcXFwiOltcXFwiT25seVJ1bGVzIC0gQUkg5o+Q56S66K+N566h55CG5bmz5Y+wXFxcIl0sXFxcImFwcC5kZXNjcmlwdGlvblxcXCI6W1xcXCLkuLrmgqjllpzniLHnmoQgSURFIOWIm+W7uuOAgeaVtOeQhuWSjOWIhuS6qyBBSSDmj5DnpLror43op4TliJnjgILpgJrov4fnpL7ljLrpqbHliqjnmoTmqKHmnb/mj5DljYfmgqjnmoTnvJbnoIHmlYjnjofjgIJcXFwiXSxcXFwibmF2LmhvbWVcXFwiOltcXFwi6aaW6aG1XFxcIl0sXFxcIm5hdi50ZW1wbGF0ZXNcXFwiOltcXFwi5qih5p2/XFxcIl0sXFxcIm5hdi5kYXNoYm9hcmRcXFwiOltcXFwi5o6n5Yi25Y+wXFxcIl0sXFxcIm5hdi50dXRvcmlhbHNcXFwiOltcXFwi5pWZ56iLXFxcIl0sXFxcImhlcm8udGl0bGVcXFwiOltcXFwi55SoIEFJIOaPkOekuuivjeS4uuaCqOeahCBJREUg6LWL6IO9XFxcIl0sXFxcImhlcm8uc3VidGl0bGVcXFwiOltcXFwi5Y+R546w44CB5Yib5bu65ZKM5YiG5Lqr5by65aSn55qEIEFJIOaPkOekuuivjeinhOWIme+8jOmAgueUqOS6jiBDdXJzb3LjgIFBdWdtZW50IENvZGXjgIFXaW5kc3VyZuOAgUNsYXVkZeOAgUdpdEh1YiBDb3BpbG9044CBR2VtaW5pIOetieOAguWKoOWFpeS8mOWMlue8lueggeW3peS9nOa1geeoi+eahOW8gOWPkeiAheekvuWMuuOAglxcXCJdLFxcXCJoZXJvLmdldFN0YXJ0ZWRcXFwiOltcXFwi5byA5aeL5L2/55SoXFxcIl0sXFxcImhlcm8uYnJvd3NlVGVtcGxhdGVzXFxcIjpbXFxcIua1j+iniOaooeadv1xcXCJdLFxcXCJmZWF0dXJlcy50aXRsZVxcXCI6W1xcXCJBSSDpqbHliqjnvJbnoIHmiYDpnIDnmoTkuIDliIdcXFwiXSxcXFwiZmVhdHVyZXMuc3VidGl0bGVcXFwiOltcXFwi5LuO5o+Q56S66K+N5qih5p2/5Yiw54mI5pys5o6n5Yi277yM5oiR5Lus5Li65oKo5o+Q5L6b5YWo5pa55L2N5pSv5oyBXFxcIl0sXFxcImF1dGguc2lnbkluXFxcIjpbXFxcIueZu+W9lVxcXCJdLFxcXCJhdXRoLnNpZ25VcFxcXCI6W1xcXCLms6jlhoxcXFwiXSxcXFwiYXV0aC5zaWduT3V0XFxcIjpbXFxcIumAgOWHuueZu+W9lVxcXCJdfVwiKX07Il0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJtZXNzYWdlcyIsIkpTT04iLCJwYXJzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/zh-CN/messages.js\n"));

/***/ })

}]);