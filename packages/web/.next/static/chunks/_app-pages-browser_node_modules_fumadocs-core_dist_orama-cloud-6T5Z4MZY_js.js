"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_orama-cloud-6T5Z4MZY_js"],{

/***/ "(app-pages-browser)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9yZW1vdmUtdW5kZWZpbmVkLnRzXG5mdW5jdGlvbiByZW1vdmVVbmRlZmluZWQodmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBjb25zdCBvYmogPSB2YWx1ZTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgIGlmIChvYmpba2V5XSA9PT0gdm9pZCAwKSBkZWxldGUgb2JqW2tleV07XG4gICAgaWYgKGRlZXAgJiYgdHlwZW9mIG9ialtrZXldID09PSBcIm9iamVjdFwiICYmIG9ialtrZXldICE9PSBudWxsKSB7XG4gICAgICByZW1vdmVVbmRlZmluZWQob2JqW2tleV0sIGRlZXApO1xuICAgIH0gZWxzZSBpZiAoZGVlcCAmJiBBcnJheS5pc0FycmF5KG9ialtrZXldKSkge1xuICAgICAgb2JqW2tleV0uZm9yRWFjaCgodikgPT4gcmVtb3ZlVW5kZWZpbmVkKHYsIGRlZXApKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQge1xuICByZW1vdmVVbmRlZmluZWRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(app-pages-browser)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(app-pages-browser)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js\n"));

/***/ })

}]);