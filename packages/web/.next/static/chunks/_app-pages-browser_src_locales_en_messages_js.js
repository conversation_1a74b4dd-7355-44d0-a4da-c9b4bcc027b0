"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_messages_js"],{

/***/ "(app-pages-browser)/./src/locales/en/messages.js":
/*!************************************!*\
  !*** ./src/locales/en/messages.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*eslint-disable*/ \nmodule.exports = {\n    messages: JSON.parse(\"{\\\"app.title\\\":[\\\"OnlyRules - AI Prompt Management Platform\\\"],\\\"app.description\\\":[\\\"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.\\\"],\\\"nav.home\\\":[\\\"Home\\\"],\\\"nav.templates\\\":[\\\"Templates\\\"],\\\"nav.dashboard\\\":[\\\"Dashboard\\\"],\\\"nav.tutorials\\\":[\\\"Tutorials\\\"],\\\"hero.title\\\":[\\\"Supercharge Your IDE with AI Prompts\\\"],\\\"hero.subtitle\\\":[\\\"Discover, create, and share powerful AI prompt rules for Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, and more. Join the community of developers optimizing their coding workflow.\\\"],\\\"hero.getStarted\\\":[\\\"Get Started\\\"],\\\"hero.browseTemplates\\\":[\\\"Browse Templates\\\"],\\\"features.title\\\":[\\\"Everything You Need for AI-Powered Coding\\\"],\\\"features.subtitle\\\":[\\\"From prompt templates to version control, we've got you covered\\\"],\\\"auth.signIn\\\":[\\\"Sign In\\\"],\\\"auth.signUp\\\":[\\\"Sign Up\\\"],\\\"auth.signOut\\\":[\\\"Sign Out\\\"]}\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/locales/en/messages.js\n"));

/***/ })

}]);