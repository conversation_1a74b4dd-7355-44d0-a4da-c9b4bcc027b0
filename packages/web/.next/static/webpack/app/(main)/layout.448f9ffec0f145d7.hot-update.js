"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RadixThemeWrapper(param) {\n    let { children } = param;\n    _s();\n    const { resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // Use resolvedTheme from next-themes which handles SSR properly\n    // This ensures the same theme is used on both server and client\n    const appearance = resolvedTheme || \"dark\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Theme, {\n        accentColor: \"blue\",\n        grayColor: \"slate\",\n        radius: \"medium\",\n        scaling: \"100%\",\n        appearance: appearance,\n        panelBackground: \"translucent\",\n        hasBackground: false,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(RadixThemeWrapper, \"ZFEt0jE4OUgXZiwzKU9YAbCBnGI=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = RadixThemeWrapper;\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        attribute: \"class\",\n        disableTransitionOnChange: false,\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RadixThemeWrapper, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ThemeProvider;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadixThemeWrapper\");\n$RefreshReg$(_c1, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers/theme-provider.tsx\n"));

/***/ })

});