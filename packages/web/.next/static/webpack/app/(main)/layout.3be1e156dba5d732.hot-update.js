"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/layout",{

/***/ "(app-pages-browser)/./components/providers/theme-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/theme-provider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/themes */ \"(app-pages-browser)/../../node_modules/@radix-ui/themes/dist/esm/components/theme.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction RadixThemeWrapper(param) {\n    let { children } = param;\n    _s();\n    const { theme, systemTheme, resolvedTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // Use resolvedTheme from next-themes which handles SSR properly\n    // This ensures the same theme is used on both server and client\n    const appearance = resolvedTheme || \"dark\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Theme, {\n        accentColor: \"blue\",\n        grayColor: \"slate\",\n        radius: \"medium\",\n        scaling: \"100%\",\n        appearance: appearance,\n        panelBackground: \"translucent\",\n        hasBackground: false,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(RadixThemeWrapper, \"Q1+SnROMB4Q1ofXZQpro5+oiWZ0=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = RadixThemeWrapper;\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        attribute: \"class\",\n        disableTransitionOnChange: false,\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RadixThemeWrapper, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/theme-provider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ThemeProvider;\nvar _c, _c1;\n$RefreshReg$(_c, \"RadixThemeWrapper\");\n$RefreshReg$(_c1, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers/theme-provider.tsx\n"));

/***/ })

});