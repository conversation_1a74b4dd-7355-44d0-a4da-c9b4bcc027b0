"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rou3";
exports.ids = ["vendor-chunks/rou3"];
exports.modules = {

/***/ "(rsc)/../../node_modules/rou3/dist/index.mjs":
/*!**********************************************!*\
  !*** ../../node_modules/rou3/dist/index.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addRoute: () => (/* binding */ addRoute),\n/* harmony export */   createRouter: () => (/* binding */ createRouter),\n/* harmony export */   findAllRoutes: () => (/* binding */ findAllRoutes),\n/* harmony export */   findRoute: () => (/* binding */ findRoute),\n/* harmony export */   removeRoute: () => (/* binding */ removeRoute)\n/* harmony export */ });\nconst EmptyObject = /* @__PURE__ */ (() => {\n  const C = function() {\n  };\n  C.prototype = /* @__PURE__ */ Object.create(null);\n  return C;\n})();\n\nfunction createRouter() {\n  const ctx = {\n    root: { key: \"\" },\n    static: new EmptyObject()\n  };\n  return ctx;\n}\n\nfunction splitPath(path) {\n  return path.split(\"/\").filter(Boolean);\n}\nfunction getMatchParams(segments, paramsMap) {\n  const params = new EmptyObject();\n  for (const [index, name] of paramsMap) {\n    const segment = index < 0 ? segments.slice(-1 * index).join(\"/\") : segments[index];\n    if (typeof name === \"string\") {\n      params[name] = segment;\n    } else {\n      const match = segment.match(name);\n      if (match) {\n        for (const key in match.groups) {\n          params[key] = match.groups[key];\n        }\n      }\n    }\n  }\n  return params;\n}\n\nfunction addRoute(ctx, method = \"\", path, data) {\n  const segments = splitPath(path);\n  let node = ctx.root;\n  let _unnamedParamIndex = 0;\n  const paramsMap = [];\n  for (let i = 0; i < segments.length; i++) {\n    const segment = segments[i];\n    if (segment.startsWith(\"**\")) {\n      if (!node.wildcard) {\n        node.wildcard = { key: \"**\" };\n      }\n      node = node.wildcard;\n      paramsMap.push([\n        -i,\n        segment.split(\":\")[1] || \"_\",\n        segment.length === 2\n      ]);\n      break;\n    }\n    if (segment === \"*\" || segment.includes(\":\")) {\n      if (!node.param) {\n        node.param = { key: \"*\" };\n      }\n      node = node.param;\n      const isOptional = segment === \"*\";\n      paramsMap.push([\n        i,\n        isOptional ? `_${_unnamedParamIndex++}` : _getParamMatcher(segment),\n        isOptional\n      ]);\n      continue;\n    }\n    const child = node.static?.[segment];\n    if (child) {\n      node = child;\n    } else {\n      const staticNode = { key: segment };\n      if (!node.static) {\n        node.static = new EmptyObject();\n      }\n      node.static[segment] = staticNode;\n      node = staticNode;\n    }\n  }\n  const hasParams = paramsMap.length > 0;\n  if (!node.methods) {\n    node.methods = new EmptyObject();\n  }\n  if (!node.methods[method]) {\n    node.methods[method] = [];\n  }\n  node.methods[method].push({\n    data: data || null,\n    paramsMap: hasParams ? paramsMap : void 0\n  });\n  if (!hasParams) {\n    ctx.static[path] = node;\n  }\n}\nfunction _getParamMatcher(segment) {\n  if (!segment.includes(\":\", 1)) {\n    return segment.slice(1);\n  }\n  const regex = segment.replace(/:(\\w+)/g, (_, id) => `(?<${id}>\\\\w+)`);\n  return new RegExp(`^${regex}$`);\n}\n\nfunction findRoute(ctx, method = \"\", path, opts) {\n  if (path[path.length - 1] === \"/\") {\n    path = path.slice(0, -1);\n  }\n  const staticNode = ctx.static[path];\n  if (staticNode && staticNode.methods) {\n    const staticMatch = staticNode.methods[method] || staticNode.methods[\"\"];\n    if (staticMatch !== void 0) {\n      return staticMatch[0];\n    }\n  }\n  const segments = splitPath(path);\n  const match = _lookupTree(ctx, ctx.root, method, segments, 0)?.[0];\n  if (match === void 0) {\n    return;\n  }\n  if (opts?.params === false) {\n    return match;\n  }\n  return {\n    data: match.data,\n    params: match.paramsMap ? getMatchParams(segments, match.paramsMap) : void 0\n  };\n}\nfunction _lookupTree(ctx, node, method, segments, index) {\n  if (index === segments.length) {\n    if (node.methods) {\n      const match = node.methods[method] || node.methods[\"\"];\n      if (match) {\n        return match;\n      }\n    }\n    if (node.param && node.param.methods) {\n      const match = node.param.methods[method] || node.param.methods[\"\"];\n      if (match) {\n        const pMap = match[0].paramsMap;\n        if (pMap?.[pMap?.length - 1]?.[2]) {\n          return match;\n        }\n      }\n    }\n    if (node.wildcard && node.wildcard.methods) {\n      const match = node.wildcard.methods[method] || node.wildcard.methods[\"\"];\n      if (match) {\n        const pMap = match[0].paramsMap;\n        if (pMap?.[pMap?.length - 1]?.[2]) {\n          return match;\n        }\n      }\n    }\n    return void 0;\n  }\n  const segment = segments[index];\n  if (node.static) {\n    const staticChild = node.static[segment];\n    if (staticChild) {\n      const match = _lookupTree(ctx, staticChild, method, segments, index + 1);\n      if (match) {\n        return match;\n      }\n    }\n  }\n  if (node.param) {\n    const match = _lookupTree(ctx, node.param, method, segments, index + 1);\n    if (match) {\n      return match;\n    }\n  }\n  if (node.wildcard && node.wildcard.methods) {\n    return node.wildcard.methods[method] || node.wildcard.methods[\"\"];\n  }\n  return;\n}\n\nfunction removeRoute(ctx, method, path) {\n  const segments = splitPath(path);\n  return _remove(ctx.root, method || \"\", segments, 0);\n}\nfunction _remove(node, method, segments, index) {\n  if (index === segments.length) {\n    if (node.methods && method in node.methods) {\n      delete node.methods[method];\n      if (Object.keys(node.methods).length === 0) {\n        node.methods = void 0;\n      }\n    }\n    return;\n  }\n  const segment = segments[index];\n  if (segment === \"*\") {\n    if (node.param) {\n      _remove(node.param, method, segments, index + 1);\n      if (_isEmptyNode(node.param)) {\n        node.param = void 0;\n      }\n    }\n    return;\n  }\n  if (segment === \"**\") {\n    if (node.wildcard) {\n      _remove(node.wildcard, method, segments, index + 1);\n      if (_isEmptyNode(node.wildcard)) {\n        node.wildcard = void 0;\n      }\n    }\n    return;\n  }\n  const childNode = node.static?.[segment];\n  if (childNode) {\n    _remove(childNode, method, segments, index + 1);\n    if (_isEmptyNode(childNode)) {\n      delete node.static[segment];\n      if (Object.keys(node.static).length === 0) {\n        node.static = void 0;\n      }\n    }\n  }\n}\nfunction _isEmptyNode(node) {\n  return node.methods === void 0 && node.static === void 0 && node.param === void 0 && node.wildcard === void 0;\n}\n\nfunction findAllRoutes(ctx, method = \"\", path, opts) {\n  if (path[path.length - 1] === \"/\") {\n    path = path.slice(0, -1);\n  }\n  const segments = splitPath(path);\n  const matches = _findAll(ctx, ctx.root, method, segments, 0);\n  if (opts?.params === false) {\n    return matches;\n  }\n  return matches.map((m) => {\n    return {\n      data: m.data,\n      params: m.paramsMap ? getMatchParams(segments, m.paramsMap) : void 0\n    };\n  });\n}\nfunction _findAll(ctx, node, method, segments, index, matches = []) {\n  const segment = segments[index];\n  if (node.wildcard && node.wildcard.methods) {\n    const match = node.wildcard.methods[method] || node.wildcard.methods[\"\"];\n    if (match) {\n      matches.push(...match);\n    }\n  }\n  if (node.param) {\n    _findAll(ctx, node.param, method, segments, index + 1, matches);\n    if (index === segments.length && node.param.methods) {\n      const match = node.param.methods[method] || node.param.methods[\"\"];\n      if (match) {\n        matches.push(...match);\n      }\n    }\n  }\n  const staticChild = node.static?.[segment];\n  if (staticChild) {\n    _findAll(ctx, staticChild, method, segments, index + 1, matches);\n  }\n  if (index === segments.length && node.methods) {\n    const match = node.methods[method] || node.methods[\"\"];\n    if (match) {\n      matches.push(...match);\n    }\n  }\n  return matches;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL3JvdTMvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHFCQUFxQjtBQUN2QztBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHFCQUFxQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTiwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTRELEdBQUc7QUFDL0Qsd0JBQXdCLE1BQU07QUFDOUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9yb3UzL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEVtcHR5T2JqZWN0ID0gLyogQF9fUFVSRV9fICovICgoKSA9PiB7XG4gIGNvbnN0IEMgPSBmdW5jdGlvbigpIHtcbiAgfTtcbiAgQy5wcm90b3R5cGUgPSAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgcmV0dXJuIEM7XG59KSgpO1xuXG5mdW5jdGlvbiBjcmVhdGVSb3V0ZXIoKSB7XG4gIGNvbnN0IGN0eCA9IHtcbiAgICByb290OiB7IGtleTogXCJcIiB9LFxuICAgIHN0YXRpYzogbmV3IEVtcHR5T2JqZWN0KClcbiAgfTtcbiAgcmV0dXJuIGN0eDtcbn1cblxuZnVuY3Rpb24gc3BsaXRQYXRoKHBhdGgpIHtcbiAgcmV0dXJuIHBhdGguc3BsaXQoXCIvXCIpLmZpbHRlcihCb29sZWFuKTtcbn1cbmZ1bmN0aW9uIGdldE1hdGNoUGFyYW1zKHNlZ21lbnRzLCBwYXJhbXNNYXApIHtcbiAgY29uc3QgcGFyYW1zID0gbmV3IEVtcHR5T2JqZWN0KCk7XG4gIGZvciAoY29uc3QgW2luZGV4LCBuYW1lXSBvZiBwYXJhbXNNYXApIHtcbiAgICBjb25zdCBzZWdtZW50ID0gaW5kZXggPCAwID8gc2VnbWVudHMuc2xpY2UoLTEgKiBpbmRleCkuam9pbihcIi9cIikgOiBzZWdtZW50c1tpbmRleF07XG4gICAgaWYgKHR5cGVvZiBuYW1lID09PSBcInN0cmluZ1wiKSB7XG4gICAgICBwYXJhbXNbbmFtZV0gPSBzZWdtZW50O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBtYXRjaCA9IHNlZ21lbnQubWF0Y2gobmFtZSk7XG4gICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gbWF0Y2guZ3JvdXBzKSB7XG4gICAgICAgICAgcGFyYW1zW2tleV0gPSBtYXRjaC5ncm91cHNba2V5XTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gcGFyYW1zO1xufVxuXG5mdW5jdGlvbiBhZGRSb3V0ZShjdHgsIG1ldGhvZCA9IFwiXCIsIHBhdGgsIGRhdGEpIHtcbiAgY29uc3Qgc2VnbWVudHMgPSBzcGxpdFBhdGgocGF0aCk7XG4gIGxldCBub2RlID0gY3R4LnJvb3Q7XG4gIGxldCBfdW5uYW1lZFBhcmFtSW5kZXggPSAwO1xuICBjb25zdCBwYXJhbXNNYXAgPSBbXTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBzZWdtZW50cy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IHNlZ21lbnQgPSBzZWdtZW50c1tpXTtcbiAgICBpZiAoc2VnbWVudC5zdGFydHNXaXRoKFwiKipcIikpIHtcbiAgICAgIGlmICghbm9kZS53aWxkY2FyZCkge1xuICAgICAgICBub2RlLndpbGRjYXJkID0geyBrZXk6IFwiKipcIiB9O1xuICAgICAgfVxuICAgICAgbm9kZSA9IG5vZGUud2lsZGNhcmQ7XG4gICAgICBwYXJhbXNNYXAucHVzaChbXG4gICAgICAgIC1pLFxuICAgICAgICBzZWdtZW50LnNwbGl0KFwiOlwiKVsxXSB8fCBcIl9cIixcbiAgICAgICAgc2VnbWVudC5sZW5ndGggPT09IDJcbiAgICAgIF0pO1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGlmIChzZWdtZW50ID09PSBcIipcIiB8fCBzZWdtZW50LmluY2x1ZGVzKFwiOlwiKSkge1xuICAgICAgaWYgKCFub2RlLnBhcmFtKSB7XG4gICAgICAgIG5vZGUucGFyYW0gPSB7IGtleTogXCIqXCIgfTtcbiAgICAgIH1cbiAgICAgIG5vZGUgPSBub2RlLnBhcmFtO1xuICAgICAgY29uc3QgaXNPcHRpb25hbCA9IHNlZ21lbnQgPT09IFwiKlwiO1xuICAgICAgcGFyYW1zTWFwLnB1c2goW1xuICAgICAgICBpLFxuICAgICAgICBpc09wdGlvbmFsID8gYF8ke191bm5hbWVkUGFyYW1JbmRleCsrfWAgOiBfZ2V0UGFyYW1NYXRjaGVyKHNlZ21lbnQpLFxuICAgICAgICBpc09wdGlvbmFsXG4gICAgICBdKTtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBjb25zdCBjaGlsZCA9IG5vZGUuc3RhdGljPy5bc2VnbWVudF07XG4gICAgaWYgKGNoaWxkKSB7XG4gICAgICBub2RlID0gY2hpbGQ7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHN0YXRpY05vZGUgPSB7IGtleTogc2VnbWVudCB9O1xuICAgICAgaWYgKCFub2RlLnN0YXRpYykge1xuICAgICAgICBub2RlLnN0YXRpYyA9IG5ldyBFbXB0eU9iamVjdCgpO1xuICAgICAgfVxuICAgICAgbm9kZS5zdGF0aWNbc2VnbWVudF0gPSBzdGF0aWNOb2RlO1xuICAgICAgbm9kZSA9IHN0YXRpY05vZGU7XG4gICAgfVxuICB9XG4gIGNvbnN0IGhhc1BhcmFtcyA9IHBhcmFtc01hcC5sZW5ndGggPiAwO1xuICBpZiAoIW5vZGUubWV0aG9kcykge1xuICAgIG5vZGUubWV0aG9kcyA9IG5ldyBFbXB0eU9iamVjdCgpO1xuICB9XG4gIGlmICghbm9kZS5tZXRob2RzW21ldGhvZF0pIHtcbiAgICBub2RlLm1ldGhvZHNbbWV0aG9kXSA9IFtdO1xuICB9XG4gIG5vZGUubWV0aG9kc1ttZXRob2RdLnB1c2goe1xuICAgIGRhdGE6IGRhdGEgfHwgbnVsbCxcbiAgICBwYXJhbXNNYXA6IGhhc1BhcmFtcyA/IHBhcmFtc01hcCA6IHZvaWQgMFxuICB9KTtcbiAgaWYgKCFoYXNQYXJhbXMpIHtcbiAgICBjdHguc3RhdGljW3BhdGhdID0gbm9kZTtcbiAgfVxufVxuZnVuY3Rpb24gX2dldFBhcmFtTWF0Y2hlcihzZWdtZW50KSB7XG4gIGlmICghc2VnbWVudC5pbmNsdWRlcyhcIjpcIiwgMSkpIHtcbiAgICByZXR1cm4gc2VnbWVudC5zbGljZSgxKTtcbiAgfVxuICBjb25zdCByZWdleCA9IHNlZ21lbnQucmVwbGFjZSgvOihcXHcrKS9nLCAoXywgaWQpID0+IGAoPzwke2lkfT5cXFxcdyspYCk7XG4gIHJldHVybiBuZXcgUmVnRXhwKGBeJHtyZWdleH0kYCk7XG59XG5cbmZ1bmN0aW9uIGZpbmRSb3V0ZShjdHgsIG1ldGhvZCA9IFwiXCIsIHBhdGgsIG9wdHMpIHtcbiAgaWYgKHBhdGhbcGF0aC5sZW5ndGggLSAxXSA9PT0gXCIvXCIpIHtcbiAgICBwYXRoID0gcGF0aC5zbGljZSgwLCAtMSk7XG4gIH1cbiAgY29uc3Qgc3RhdGljTm9kZSA9IGN0eC5zdGF0aWNbcGF0aF07XG4gIGlmIChzdGF0aWNOb2RlICYmIHN0YXRpY05vZGUubWV0aG9kcykge1xuICAgIGNvbnN0IHN0YXRpY01hdGNoID0gc3RhdGljTm9kZS5tZXRob2RzW21ldGhvZF0gfHwgc3RhdGljTm9kZS5tZXRob2RzW1wiXCJdO1xuICAgIGlmIChzdGF0aWNNYXRjaCAhPT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm4gc3RhdGljTWF0Y2hbMF07XG4gICAgfVxuICB9XG4gIGNvbnN0IHNlZ21lbnRzID0gc3BsaXRQYXRoKHBhdGgpO1xuICBjb25zdCBtYXRjaCA9IF9sb29rdXBUcmVlKGN0eCwgY3R4LnJvb3QsIG1ldGhvZCwgc2VnbWVudHMsIDApPy5bMF07XG4gIGlmIChtYXRjaCA9PT0gdm9pZCAwKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGlmIChvcHRzPy5wYXJhbXMgPT09IGZhbHNlKSB7XG4gICAgcmV0dXJuIG1hdGNoO1xuICB9XG4gIHJldHVybiB7XG4gICAgZGF0YTogbWF0Y2guZGF0YSxcbiAgICBwYXJhbXM6IG1hdGNoLnBhcmFtc01hcCA/IGdldE1hdGNoUGFyYW1zKHNlZ21lbnRzLCBtYXRjaC5wYXJhbXNNYXApIDogdm9pZCAwXG4gIH07XG59XG5mdW5jdGlvbiBfbG9va3VwVHJlZShjdHgsIG5vZGUsIG1ldGhvZCwgc2VnbWVudHMsIGluZGV4KSB7XG4gIGlmIChpbmRleCA9PT0gc2VnbWVudHMubGVuZ3RoKSB7XG4gICAgaWYgKG5vZGUubWV0aG9kcykge1xuICAgICAgY29uc3QgbWF0Y2ggPSBub2RlLm1ldGhvZHNbbWV0aG9kXSB8fCBub2RlLm1ldGhvZHNbXCJcIl07XG4gICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgcmV0dXJuIG1hdGNoO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAobm9kZS5wYXJhbSAmJiBub2RlLnBhcmFtLm1ldGhvZHMpIHtcbiAgICAgIGNvbnN0IG1hdGNoID0gbm9kZS5wYXJhbS5tZXRob2RzW21ldGhvZF0gfHwgbm9kZS5wYXJhbS5tZXRob2RzW1wiXCJdO1xuICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgIGNvbnN0IHBNYXAgPSBtYXRjaFswXS5wYXJhbXNNYXA7XG4gICAgICAgIGlmIChwTWFwPy5bcE1hcD8ubGVuZ3RoIC0gMV0/LlsyXSkge1xuICAgICAgICAgIHJldHVybiBtYXRjaDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBpZiAobm9kZS53aWxkY2FyZCAmJiBub2RlLndpbGRjYXJkLm1ldGhvZHMpIHtcbiAgICAgIGNvbnN0IG1hdGNoID0gbm9kZS53aWxkY2FyZC5tZXRob2RzW21ldGhvZF0gfHwgbm9kZS53aWxkY2FyZC5tZXRob2RzW1wiXCJdO1xuICAgICAgaWYgKG1hdGNoKSB7XG4gICAgICAgIGNvbnN0IHBNYXAgPSBtYXRjaFswXS5wYXJhbXNNYXA7XG4gICAgICAgIGlmIChwTWFwPy5bcE1hcD8ubGVuZ3RoIC0gMV0/LlsyXSkge1xuICAgICAgICAgIHJldHVybiBtYXRjaDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdm9pZCAwO1xuICB9XG4gIGNvbnN0IHNlZ21lbnQgPSBzZWdtZW50c1tpbmRleF07XG4gIGlmIChub2RlLnN0YXRpYykge1xuICAgIGNvbnN0IHN0YXRpY0NoaWxkID0gbm9kZS5zdGF0aWNbc2VnbWVudF07XG4gICAgaWYgKHN0YXRpY0NoaWxkKSB7XG4gICAgICBjb25zdCBtYXRjaCA9IF9sb29rdXBUcmVlKGN0eCwgc3RhdGljQ2hpbGQsIG1ldGhvZCwgc2VnbWVudHMsIGluZGV4ICsgMSk7XG4gICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgcmV0dXJuIG1hdGNoO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBpZiAobm9kZS5wYXJhbSkge1xuICAgIGNvbnN0IG1hdGNoID0gX2xvb2t1cFRyZWUoY3R4LCBub2RlLnBhcmFtLCBtZXRob2QsIHNlZ21lbnRzLCBpbmRleCArIDEpO1xuICAgIGlmIChtYXRjaCkge1xuICAgICAgcmV0dXJuIG1hdGNoO1xuICAgIH1cbiAgfVxuICBpZiAobm9kZS53aWxkY2FyZCAmJiBub2RlLndpbGRjYXJkLm1ldGhvZHMpIHtcbiAgICByZXR1cm4gbm9kZS53aWxkY2FyZC5tZXRob2RzW21ldGhvZF0gfHwgbm9kZS53aWxkY2FyZC5tZXRob2RzW1wiXCJdO1xuICB9XG4gIHJldHVybjtcbn1cblxuZnVuY3Rpb24gcmVtb3ZlUm91dGUoY3R4LCBtZXRob2QsIHBhdGgpIHtcbiAgY29uc3Qgc2VnbWVudHMgPSBzcGxpdFBhdGgocGF0aCk7XG4gIHJldHVybiBfcmVtb3ZlKGN0eC5yb290LCBtZXRob2QgfHwgXCJcIiwgc2VnbWVudHMsIDApO1xufVxuZnVuY3Rpb24gX3JlbW92ZShub2RlLCBtZXRob2QsIHNlZ21lbnRzLCBpbmRleCkge1xuICBpZiAoaW5kZXggPT09IHNlZ21lbnRzLmxlbmd0aCkge1xuICAgIGlmIChub2RlLm1ldGhvZHMgJiYgbWV0aG9kIGluIG5vZGUubWV0aG9kcykge1xuICAgICAgZGVsZXRlIG5vZGUubWV0aG9kc1ttZXRob2RdO1xuICAgICAgaWYgKE9iamVjdC5rZXlzKG5vZGUubWV0aG9kcykubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIG5vZGUubWV0aG9kcyA9IHZvaWQgMDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IHNlZ21lbnQgPSBzZWdtZW50c1tpbmRleF07XG4gIGlmIChzZWdtZW50ID09PSBcIipcIikge1xuICAgIGlmIChub2RlLnBhcmFtKSB7XG4gICAgICBfcmVtb3ZlKG5vZGUucGFyYW0sIG1ldGhvZCwgc2VnbWVudHMsIGluZGV4ICsgMSk7XG4gICAgICBpZiAoX2lzRW1wdHlOb2RlKG5vZGUucGFyYW0pKSB7XG4gICAgICAgIG5vZGUucGFyYW0gPSB2b2lkIDA7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybjtcbiAgfVxuICBpZiAoc2VnbWVudCA9PT0gXCIqKlwiKSB7XG4gICAgaWYgKG5vZGUud2lsZGNhcmQpIHtcbiAgICAgIF9yZW1vdmUobm9kZS53aWxkY2FyZCwgbWV0aG9kLCBzZWdtZW50cywgaW5kZXggKyAxKTtcbiAgICAgIGlmIChfaXNFbXB0eU5vZGUobm9kZS53aWxkY2FyZCkpIHtcbiAgICAgICAgbm9kZS53aWxkY2FyZCA9IHZvaWQgMDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IGNoaWxkTm9kZSA9IG5vZGUuc3RhdGljPy5bc2VnbWVudF07XG4gIGlmIChjaGlsZE5vZGUpIHtcbiAgICBfcmVtb3ZlKGNoaWxkTm9kZSwgbWV0aG9kLCBzZWdtZW50cywgaW5kZXggKyAxKTtcbiAgICBpZiAoX2lzRW1wdHlOb2RlKGNoaWxkTm9kZSkpIHtcbiAgICAgIGRlbGV0ZSBub2RlLnN0YXRpY1tzZWdtZW50XTtcbiAgICAgIGlmIChPYmplY3Qua2V5cyhub2RlLnN0YXRpYykubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIG5vZGUuc3RhdGljID0gdm9pZCAwO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gX2lzRW1wdHlOb2RlKG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUubWV0aG9kcyA9PT0gdm9pZCAwICYmIG5vZGUuc3RhdGljID09PSB2b2lkIDAgJiYgbm9kZS5wYXJhbSA9PT0gdm9pZCAwICYmIG5vZGUud2lsZGNhcmQgPT09IHZvaWQgMDtcbn1cblxuZnVuY3Rpb24gZmluZEFsbFJvdXRlcyhjdHgsIG1ldGhvZCA9IFwiXCIsIHBhdGgsIG9wdHMpIHtcbiAgaWYgKHBhdGhbcGF0aC5sZW5ndGggLSAxXSA9PT0gXCIvXCIpIHtcbiAgICBwYXRoID0gcGF0aC5zbGljZSgwLCAtMSk7XG4gIH1cbiAgY29uc3Qgc2VnbWVudHMgPSBzcGxpdFBhdGgocGF0aCk7XG4gIGNvbnN0IG1hdGNoZXMgPSBfZmluZEFsbChjdHgsIGN0eC5yb290LCBtZXRob2QsIHNlZ21lbnRzLCAwKTtcbiAgaWYgKG9wdHM/LnBhcmFtcyA9PT0gZmFsc2UpIHtcbiAgICByZXR1cm4gbWF0Y2hlcztcbiAgfVxuICByZXR1cm4gbWF0Y2hlcy5tYXAoKG0pID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgZGF0YTogbS5kYXRhLFxuICAgICAgcGFyYW1zOiBtLnBhcmFtc01hcCA/IGdldE1hdGNoUGFyYW1zKHNlZ21lbnRzLCBtLnBhcmFtc01hcCkgOiB2b2lkIDBcbiAgICB9O1xuICB9KTtcbn1cbmZ1bmN0aW9uIF9maW5kQWxsKGN0eCwgbm9kZSwgbWV0aG9kLCBzZWdtZW50cywgaW5kZXgsIG1hdGNoZXMgPSBbXSkge1xuICBjb25zdCBzZWdtZW50ID0gc2VnbWVudHNbaW5kZXhdO1xuICBpZiAobm9kZS53aWxkY2FyZCAmJiBub2RlLndpbGRjYXJkLm1ldGhvZHMpIHtcbiAgICBjb25zdCBtYXRjaCA9IG5vZGUud2lsZGNhcmQubWV0aG9kc1ttZXRob2RdIHx8IG5vZGUud2lsZGNhcmQubWV0aG9kc1tcIlwiXTtcbiAgICBpZiAobWF0Y2gpIHtcbiAgICAgIG1hdGNoZXMucHVzaCguLi5tYXRjaCk7XG4gICAgfVxuICB9XG4gIGlmIChub2RlLnBhcmFtKSB7XG4gICAgX2ZpbmRBbGwoY3R4LCBub2RlLnBhcmFtLCBtZXRob2QsIHNlZ21lbnRzLCBpbmRleCArIDEsIG1hdGNoZXMpO1xuICAgIGlmIChpbmRleCA9PT0gc2VnbWVudHMubGVuZ3RoICYmIG5vZGUucGFyYW0ubWV0aG9kcykge1xuICAgICAgY29uc3QgbWF0Y2ggPSBub2RlLnBhcmFtLm1ldGhvZHNbbWV0aG9kXSB8fCBub2RlLnBhcmFtLm1ldGhvZHNbXCJcIl07XG4gICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgbWF0Y2hlcy5wdXNoKC4uLm1hdGNoKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgY29uc3Qgc3RhdGljQ2hpbGQgPSBub2RlLnN0YXRpYz8uW3NlZ21lbnRdO1xuICBpZiAoc3RhdGljQ2hpbGQpIHtcbiAgICBfZmluZEFsbChjdHgsIHN0YXRpY0NoaWxkLCBtZXRob2QsIHNlZ21lbnRzLCBpbmRleCArIDEsIG1hdGNoZXMpO1xuICB9XG4gIGlmIChpbmRleCA9PT0gc2VnbWVudHMubGVuZ3RoICYmIG5vZGUubWV0aG9kcykge1xuICAgIGNvbnN0IG1hdGNoID0gbm9kZS5tZXRob2RzW21ldGhvZF0gfHwgbm9kZS5tZXRob2RzW1wiXCJdO1xuICAgIGlmIChtYXRjaCkge1xuICAgICAgbWF0Y2hlcy5wdXNoKC4uLm1hdGNoKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG1hdGNoZXM7XG59XG5cbmV4cG9ydCB7IGFkZFJvdXRlLCBjcmVhdGVSb3V0ZXIsIGZpbmRBbGxSb3V0ZXMsIGZpbmRSb3V0ZSwgcmVtb3ZlUm91dGUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/rou3/dist/index.mjs\n");

/***/ })

};
;