"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@messageformat";
exports.ids = ["vendor-chunks/@messageformat"];
exports.modules = {

/***/ "(rsc)/../../node_modules/@messageformat/parser/lib/lexer.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@messageformat/parser/lib/lexer.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.lexer = exports.states = void 0;\nconst moo_1 = __importDefault(__webpack_require__(/*! moo */ \"(rsc)/../../node_modules/moo/moo.js\"));\nexports.states = {\n    body: {\n        doubleapos: { match: \"''\", value: () => \"'\" },\n        quoted: {\n            lineBreaks: true,\n            match: /'[{}#](?:[^']|'')*'(?!')/u,\n            value: src => src.slice(1, -1).replace(/''/g, \"'\")\n        },\n        argument: {\n            lineBreaks: true,\n            match: /\\{\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            push: 'arg',\n            value: src => src.substring(1).trim()\n        },\n        octothorpe: '#',\n        end: { match: '}', pop: 1 },\n        content: { lineBreaks: true, match: /[^][^{}#']*/u }\n    },\n    arg: {\n        select: {\n            lineBreaks: true,\n            match: /,\\s*(?:plural|select|selectordinal)\\s*,\\s*/u,\n            next: 'select',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-args': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*,/u,\n            next: 'body',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-simple': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            value: src => src.substring(1).trim()\n        },\n        end: { match: '}', pop: 1 }\n    },\n    select: {\n        offset: {\n            lineBreaks: true,\n            match: /\\s*offset\\s*:\\s*\\d+\\s*/u,\n            value: src => src.split(':')[1].trim()\n        },\n        case: {\n            lineBreaks: true,\n            match: /\\s*(?:=\\d+|[^\\p{Pat_Syn}\\p{Pat_WS}]+)\\s*\\{/u,\n            push: 'body',\n            value: src => src.substring(0, src.indexOf('{')).trim()\n        },\n        end: { match: /\\s*\\}/u, pop: 1 }\n    }\n};\nexports.lexer = moo_1.default.states(exports.states);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@messageformat/parser/lib/lexer.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@messageformat/parser/lib/parser.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@messageformat/parser/lib/parser.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * An AST parser for ICU MessageFormat strings\n *\n * @packageDocumentation\n * @example\n * ```\n * import { parse } from '@messageformat/parser\n *\n * parse('So {wow}.')\n * [ { type: 'content', value: 'So ' },\n *   { type: 'argument', arg: 'wow' },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('Such { thing }. { count, selectordinal, one {First} two {Second}' +\n *       '                  few {Third} other {#th} } word.')\n * [ { type: 'content', value: 'Such ' },\n *   { type: 'argument', arg: 'thing' },\n *   { type: 'content', value: '. ' },\n *   { type: 'selectordinal',\n *     arg: 'count',\n *     cases: [\n *       { key: 'one', tokens: [ { type: 'content', value: 'First' } ] },\n *       { key: 'two', tokens: [ { type: 'content', value: 'Second' } ] },\n *       { key: 'few', tokens: [ { type: 'content', value: 'Third' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: 'th' } ] }\n *     ] },\n *   { type: 'content', value: ' word.' } ]\n *\n *\n * parse('Many{type,select,plural{ numbers}selectordinal{ counting}' +\n *                          'select{ choices}other{ some {type}}}.')\n * [ { type: 'content', value: 'Many' },\n *   { type: 'select',\n *     arg: 'type',\n *     cases: [\n *       { key: 'plural', tokens: [ { type: 'content', value: 'numbers' } ] },\n *       { key: 'selectordinal', tokens: [ { type: 'content', value: 'counting' } ] },\n *       { key: 'select', tokens: [ { type: 'content', value: 'choices' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'content', value: 'some ' }, { type: 'argument', arg: 'type' } ] }\n *     ] },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('{Such compliance')\n * // ParseError: invalid syntax at line 1 col 7:\n * //\n * //  {Such compliance\n * //        ^\n *\n *\n * const msg = '{words, plural, zero{No words} one{One word} other{# words}}'\n * parse(msg)\n * [ { type: 'plural',\n *     arg: 'words',\n *     cases: [\n *       { key: 'zero', tokens: [ { type: 'content', value: 'No words' } ] },\n *       { key: 'one', tokens: [ { type: 'content', value: 'One word' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: ' words' } ] }\n *     ] } ]\n *\n *\n * parse(msg, { cardinal: [ 'one', 'other' ], ordinal: [ 'one', 'two', 'few', 'other' ] })\n * // ParseError: The plural case zero is not valid in this locale at line 1 col 17:\n * //\n * //   {words, plural, zero{\n * //                   ^\n * ```\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ParseError = void 0;\nexports.parse = parse;\nconst lexer_js_1 = __webpack_require__(/*! ./lexer.js */ \"(rsc)/../../node_modules/@messageformat/parser/lib/lexer.js\");\nconst getContext = (lt) => ({\n    offset: lt.offset,\n    line: lt.line,\n    col: lt.col,\n    text: lt.text,\n    lineBreaks: lt.lineBreaks\n});\nconst isSelectType = (type) => type === 'plural' || type === 'select' || type === 'selectordinal';\nfunction strictArgStyleParam(lt, param) {\n    let value = '';\n    let text = '';\n    for (const p of param) {\n        const pText = p.ctx.text;\n        text += pText;\n        switch (p.type) {\n            case 'content':\n                value += p.value;\n                break;\n            case 'argument':\n            case 'function':\n            case 'octothorpe':\n                value += pText;\n                break;\n            default:\n                throw new ParseError(lt, `Unsupported part in strict mode function arg style: ${pText}`);\n        }\n    }\n    const c = {\n        type: 'content',\n        value: value.trim(),\n        ctx: Object.assign({}, param[0].ctx, { text })\n    };\n    return [c];\n}\nconst strictArgTypes = [\n    'number',\n    'date',\n    'time',\n    'spellout',\n    'ordinal',\n    'duration'\n];\nconst defaultPluralKeys = ['zero', 'one', 'two', 'few', 'many', 'other'];\n/**\n * Thrown by {@link parse} on error\n *\n * @public\n */\nclass ParseError extends Error {\n    /** @internal */\n    constructor(lt, msg) {\n        super(lexer_js_1.lexer.formatError(lt, msg));\n    }\n}\nexports.ParseError = ParseError;\nclass Parser {\n    constructor(src, opt) {\n        var _a, _b, _c, _d;\n        this.lexer = lexer_js_1.lexer.reset(src);\n        this.cardinalKeys = (_a = opt === null || opt === void 0 ? void 0 : opt.cardinal) !== null && _a !== void 0 ? _a : defaultPluralKeys;\n        this.ordinalKeys = (_b = opt === null || opt === void 0 ? void 0 : opt.ordinal) !== null && _b !== void 0 ? _b : defaultPluralKeys;\n        this.strict = (_c = opt === null || opt === void 0 ? void 0 : opt.strict) !== null && _c !== void 0 ? _c : false;\n        this.strictPluralKeys = (_d = opt === null || opt === void 0 ? void 0 : opt.strictPluralKeys) !== null && _d !== void 0 ? _d : true;\n    }\n    parse() {\n        return this.parseBody(false, true);\n    }\n    checkSelectKey(lt, type, key) {\n        if (key[0] === '=') {\n            if (type === 'select') {\n                throw new ParseError(lt, `The case ${key} is not valid with select`);\n            }\n        }\n        else if (type !== 'select') {\n            const keys = type === 'plural' ? this.cardinalKeys : this.ordinalKeys;\n            if (this.strictPluralKeys && keys.length > 0 && !keys.includes(key)) {\n                const msg = `The ${type} case ${key} is not valid in this locale`;\n                throw new ParseError(lt, msg);\n            }\n        }\n    }\n    parseSelect({ value: arg }, inPlural, ctx, type) {\n        const sel = { type, arg, cases: [], ctx };\n        if (type === 'plural' || type === 'selectordinal')\n            inPlural = true;\n        else if (this.strict)\n            inPlural = false;\n        for (const lt of this.lexer) {\n            switch (lt.type) {\n                case 'offset':\n                    if (type === 'select') {\n                        throw new ParseError(lt, 'Unexpected plural offset for select');\n                    }\n                    if (sel.cases.length > 0) {\n                        throw new ParseError(lt, 'Plural offset must be set before cases');\n                    }\n                    sel.pluralOffset = Number(lt.value);\n                    ctx.text += lt.text;\n                    ctx.lineBreaks += lt.lineBreaks;\n                    break;\n                case 'case': {\n                    this.checkSelectKey(lt, type, lt.value);\n                    sel.cases.push({\n                        key: lt.value,\n                        tokens: this.parseBody(inPlural),\n                        ctx: getContext(lt)\n                    });\n                    break;\n                }\n                case 'end':\n                    return sel;\n                /* istanbul ignore next: never happens */\n                default:\n                    throw new ParseError(lt, `Unexpected lexer token: ${lt.type}`);\n            }\n        }\n        throw new ParseError(null, 'Unexpected message end');\n    }\n    parseArgToken(lt, inPlural) {\n        const ctx = getContext(lt);\n        const argType = this.lexer.next();\n        if (!argType)\n            throw new ParseError(null, 'Unexpected message end');\n        ctx.text += argType.text;\n        ctx.lineBreaks += argType.lineBreaks;\n        if (this.strict &&\n            (argType.type === 'func-simple' || argType.type === 'func-args') &&\n            !strictArgTypes.includes(argType.value)) {\n            const msg = `Invalid strict mode function arg type: ${argType.value}`;\n            throw new ParseError(lt, msg);\n        }\n        switch (argType.type) {\n            case 'end':\n                return { type: 'argument', arg: lt.value, ctx };\n            case 'func-simple': {\n                const end = this.lexer.next();\n                if (!end)\n                    throw new ParseError(null, 'Unexpected message end');\n                /* istanbul ignore if: never happens */\n                if (end.type !== 'end') {\n                    throw new ParseError(end, `Unexpected lexer token: ${end.type}`);\n                }\n                ctx.text += end.text;\n                if (isSelectType(argType.value.toLowerCase())) {\n                    throw new ParseError(argType, `Invalid type identifier: ${argType.value}`);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    ctx\n                };\n            }\n            case 'func-args': {\n                if (isSelectType(argType.value.toLowerCase())) {\n                    const msg = `Invalid type identifier: ${argType.value}`;\n                    throw new ParseError(argType, msg);\n                }\n                let param = this.parseBody(this.strict ? false : inPlural);\n                if (this.strict && param.length > 0) {\n                    param = strictArgStyleParam(lt, param);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    param,\n                    ctx\n                };\n            }\n            case 'select':\n                /* istanbul ignore else: never happens */\n                if (isSelectType(argType.value)) {\n                    return this.parseSelect(lt, inPlural, ctx, argType.value);\n                }\n                else {\n                    throw new ParseError(argType, `Unexpected select type ${argType.value}`);\n                }\n            /* istanbul ignore next: never happens */\n            default:\n                throw new ParseError(argType, `Unexpected lexer token: ${argType.type}`);\n        }\n    }\n    parseBody(inPlural, atRoot) {\n        const tokens = [];\n        let content = null;\n        for (const lt of this.lexer) {\n            if (lt.type === 'argument') {\n                if (content)\n                    content = null;\n                tokens.push(this.parseArgToken(lt, inPlural));\n            }\n            else if (lt.type === 'octothorpe' && inPlural) {\n                if (content)\n                    content = null;\n                tokens.push({ type: 'octothorpe', ctx: getContext(lt) });\n            }\n            else if (lt.type === 'end' && !atRoot) {\n                return tokens;\n            }\n            else {\n                let value = lt.value;\n                if (!inPlural && lt.type === 'quoted' && value[0] === '#') {\n                    if (value.includes('{')) {\n                        const errMsg = `Unsupported escape pattern: ${value}`;\n                        throw new ParseError(lt, errMsg);\n                    }\n                    value = lt.text;\n                }\n                if (content) {\n                    content.value += value;\n                    content.ctx.text += lt.text;\n                    content.ctx.lineBreaks += lt.lineBreaks;\n                }\n                else {\n                    content = { type: 'content', value, ctx: getContext(lt) };\n                    tokens.push(content);\n                }\n            }\n        }\n        if (atRoot)\n            return tokens;\n        throw new ParseError(null, 'Unexpected message end');\n    }\n}\n/**\n * Parse an input string into an array of tokens\n *\n * @public\n * @remarks\n * The parser only supports the default `DOUBLE_OPTIONAL`\n * {@link http://www.icu-project.org/apiref/icu4c/messagepattern_8h.html#af6e0757e0eb81c980b01ee5d68a9978b | apostrophe mode}.\n */\nfunction parse(src, options = {}) {\n    const parser = new Parser(src, options);\n    return parser.parse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@messageformat/parser/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@messageformat/parser/lib/lexer.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@messageformat/parser/lib/lexer.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.lexer = exports.states = void 0;\nconst moo_1 = __importDefault(__webpack_require__(/*! moo */ \"(ssr)/../../node_modules/moo/moo.js\"));\nexports.states = {\n    body: {\n        doubleapos: { match: \"''\", value: () => \"'\" },\n        quoted: {\n            lineBreaks: true,\n            match: /'[{}#](?:[^']|'')*'(?!')/u,\n            value: src => src.slice(1, -1).replace(/''/g, \"'\")\n        },\n        argument: {\n            lineBreaks: true,\n            match: /\\{\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            push: 'arg',\n            value: src => src.substring(1).trim()\n        },\n        octothorpe: '#',\n        end: { match: '}', pop: 1 },\n        content: { lineBreaks: true, match: /[^][^{}#']*/u }\n    },\n    arg: {\n        select: {\n            lineBreaks: true,\n            match: /,\\s*(?:plural|select|selectordinal)\\s*,\\s*/u,\n            next: 'select',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-args': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*,/u,\n            next: 'body',\n            value: src => src.split(',')[1].trim()\n        },\n        'func-simple': {\n            lineBreaks: true,\n            match: /,\\s*[^\\p{Pat_Syn}\\p{Pat_WS}]+\\s*/u,\n            value: src => src.substring(1).trim()\n        },\n        end: { match: '}', pop: 1 }\n    },\n    select: {\n        offset: {\n            lineBreaks: true,\n            match: /\\s*offset\\s*:\\s*\\d+\\s*/u,\n            value: src => src.split(':')[1].trim()\n        },\n        case: {\n            lineBreaks: true,\n            match: /\\s*(?:=\\d+|[^\\p{Pat_Syn}\\p{Pat_WS}]+)\\s*\\{/u,\n            push: 'body',\n            value: src => src.substring(0, src.indexOf('{')).trim()\n        },\n        end: { match: /\\s*\\}/u, pop: 1 }\n    }\n};\nexports.lexer = moo_1.default.states(exports.states);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@messageformat/parser/lib/lexer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@messageformat/parser/lib/parser.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@messageformat/parser/lib/parser.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * An AST parser for ICU MessageFormat strings\n *\n * @packageDocumentation\n * @example\n * ```\n * import { parse } from '@messageformat/parser\n *\n * parse('So {wow}.')\n * [ { type: 'content', value: 'So ' },\n *   { type: 'argument', arg: 'wow' },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('Such { thing }. { count, selectordinal, one {First} two {Second}' +\n *       '                  few {Third} other {#th} } word.')\n * [ { type: 'content', value: 'Such ' },\n *   { type: 'argument', arg: 'thing' },\n *   { type: 'content', value: '. ' },\n *   { type: 'selectordinal',\n *     arg: 'count',\n *     cases: [\n *       { key: 'one', tokens: [ { type: 'content', value: 'First' } ] },\n *       { key: 'two', tokens: [ { type: 'content', value: 'Second' } ] },\n *       { key: 'few', tokens: [ { type: 'content', value: 'Third' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: 'th' } ] }\n *     ] },\n *   { type: 'content', value: ' word.' } ]\n *\n *\n * parse('Many{type,select,plural{ numbers}selectordinal{ counting}' +\n *                          'select{ choices}other{ some {type}}}.')\n * [ { type: 'content', value: 'Many' },\n *   { type: 'select',\n *     arg: 'type',\n *     cases: [\n *       { key: 'plural', tokens: [ { type: 'content', value: 'numbers' } ] },\n *       { key: 'selectordinal', tokens: [ { type: 'content', value: 'counting' } ] },\n *       { key: 'select', tokens: [ { type: 'content', value: 'choices' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'content', value: 'some ' }, { type: 'argument', arg: 'type' } ] }\n *     ] },\n *   { type: 'content', value: '.' } ]\n *\n *\n * parse('{Such compliance')\n * // ParseError: invalid syntax at line 1 col 7:\n * //\n * //  {Such compliance\n * //        ^\n *\n *\n * const msg = '{words, plural, zero{No words} one{One word} other{# words}}'\n * parse(msg)\n * [ { type: 'plural',\n *     arg: 'words',\n *     cases: [\n *       { key: 'zero', tokens: [ { type: 'content', value: 'No words' } ] },\n *       { key: 'one', tokens: [ { type: 'content', value: 'One word' } ] },\n *       { key: 'other',\n *         tokens: [ { type: 'octothorpe' }, { type: 'content', value: ' words' } ] }\n *     ] } ]\n *\n *\n * parse(msg, { cardinal: [ 'one', 'other' ], ordinal: [ 'one', 'two', 'few', 'other' ] })\n * // ParseError: The plural case zero is not valid in this locale at line 1 col 17:\n * //\n * //   {words, plural, zero{\n * //                   ^\n * ```\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ParseError = void 0;\nexports.parse = parse;\nconst lexer_js_1 = __webpack_require__(/*! ./lexer.js */ \"(ssr)/../../node_modules/@messageformat/parser/lib/lexer.js\");\nconst getContext = (lt) => ({\n    offset: lt.offset,\n    line: lt.line,\n    col: lt.col,\n    text: lt.text,\n    lineBreaks: lt.lineBreaks\n});\nconst isSelectType = (type) => type === 'plural' || type === 'select' || type === 'selectordinal';\nfunction strictArgStyleParam(lt, param) {\n    let value = '';\n    let text = '';\n    for (const p of param) {\n        const pText = p.ctx.text;\n        text += pText;\n        switch (p.type) {\n            case 'content':\n                value += p.value;\n                break;\n            case 'argument':\n            case 'function':\n            case 'octothorpe':\n                value += pText;\n                break;\n            default:\n                throw new ParseError(lt, `Unsupported part in strict mode function arg style: ${pText}`);\n        }\n    }\n    const c = {\n        type: 'content',\n        value: value.trim(),\n        ctx: Object.assign({}, param[0].ctx, { text })\n    };\n    return [c];\n}\nconst strictArgTypes = [\n    'number',\n    'date',\n    'time',\n    'spellout',\n    'ordinal',\n    'duration'\n];\nconst defaultPluralKeys = ['zero', 'one', 'two', 'few', 'many', 'other'];\n/**\n * Thrown by {@link parse} on error\n *\n * @public\n */\nclass ParseError extends Error {\n    /** @internal */\n    constructor(lt, msg) {\n        super(lexer_js_1.lexer.formatError(lt, msg));\n    }\n}\nexports.ParseError = ParseError;\nclass Parser {\n    constructor(src, opt) {\n        var _a, _b, _c, _d;\n        this.lexer = lexer_js_1.lexer.reset(src);\n        this.cardinalKeys = (_a = opt === null || opt === void 0 ? void 0 : opt.cardinal) !== null && _a !== void 0 ? _a : defaultPluralKeys;\n        this.ordinalKeys = (_b = opt === null || opt === void 0 ? void 0 : opt.ordinal) !== null && _b !== void 0 ? _b : defaultPluralKeys;\n        this.strict = (_c = opt === null || opt === void 0 ? void 0 : opt.strict) !== null && _c !== void 0 ? _c : false;\n        this.strictPluralKeys = (_d = opt === null || opt === void 0 ? void 0 : opt.strictPluralKeys) !== null && _d !== void 0 ? _d : true;\n    }\n    parse() {\n        return this.parseBody(false, true);\n    }\n    checkSelectKey(lt, type, key) {\n        if (key[0] === '=') {\n            if (type === 'select') {\n                throw new ParseError(lt, `The case ${key} is not valid with select`);\n            }\n        }\n        else if (type !== 'select') {\n            const keys = type === 'plural' ? this.cardinalKeys : this.ordinalKeys;\n            if (this.strictPluralKeys && keys.length > 0 && !keys.includes(key)) {\n                const msg = `The ${type} case ${key} is not valid in this locale`;\n                throw new ParseError(lt, msg);\n            }\n        }\n    }\n    parseSelect({ value: arg }, inPlural, ctx, type) {\n        const sel = { type, arg, cases: [], ctx };\n        if (type === 'plural' || type === 'selectordinal')\n            inPlural = true;\n        else if (this.strict)\n            inPlural = false;\n        for (const lt of this.lexer) {\n            switch (lt.type) {\n                case 'offset':\n                    if (type === 'select') {\n                        throw new ParseError(lt, 'Unexpected plural offset for select');\n                    }\n                    if (sel.cases.length > 0) {\n                        throw new ParseError(lt, 'Plural offset must be set before cases');\n                    }\n                    sel.pluralOffset = Number(lt.value);\n                    ctx.text += lt.text;\n                    ctx.lineBreaks += lt.lineBreaks;\n                    break;\n                case 'case': {\n                    this.checkSelectKey(lt, type, lt.value);\n                    sel.cases.push({\n                        key: lt.value,\n                        tokens: this.parseBody(inPlural),\n                        ctx: getContext(lt)\n                    });\n                    break;\n                }\n                case 'end':\n                    return sel;\n                /* istanbul ignore next: never happens */\n                default:\n                    throw new ParseError(lt, `Unexpected lexer token: ${lt.type}`);\n            }\n        }\n        throw new ParseError(null, 'Unexpected message end');\n    }\n    parseArgToken(lt, inPlural) {\n        const ctx = getContext(lt);\n        const argType = this.lexer.next();\n        if (!argType)\n            throw new ParseError(null, 'Unexpected message end');\n        ctx.text += argType.text;\n        ctx.lineBreaks += argType.lineBreaks;\n        if (this.strict &&\n            (argType.type === 'func-simple' || argType.type === 'func-args') &&\n            !strictArgTypes.includes(argType.value)) {\n            const msg = `Invalid strict mode function arg type: ${argType.value}`;\n            throw new ParseError(lt, msg);\n        }\n        switch (argType.type) {\n            case 'end':\n                return { type: 'argument', arg: lt.value, ctx };\n            case 'func-simple': {\n                const end = this.lexer.next();\n                if (!end)\n                    throw new ParseError(null, 'Unexpected message end');\n                /* istanbul ignore if: never happens */\n                if (end.type !== 'end') {\n                    throw new ParseError(end, `Unexpected lexer token: ${end.type}`);\n                }\n                ctx.text += end.text;\n                if (isSelectType(argType.value.toLowerCase())) {\n                    throw new ParseError(argType, `Invalid type identifier: ${argType.value}`);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    ctx\n                };\n            }\n            case 'func-args': {\n                if (isSelectType(argType.value.toLowerCase())) {\n                    const msg = `Invalid type identifier: ${argType.value}`;\n                    throw new ParseError(argType, msg);\n                }\n                let param = this.parseBody(this.strict ? false : inPlural);\n                if (this.strict && param.length > 0) {\n                    param = strictArgStyleParam(lt, param);\n                }\n                return {\n                    type: 'function',\n                    arg: lt.value,\n                    key: argType.value,\n                    param,\n                    ctx\n                };\n            }\n            case 'select':\n                /* istanbul ignore else: never happens */\n                if (isSelectType(argType.value)) {\n                    return this.parseSelect(lt, inPlural, ctx, argType.value);\n                }\n                else {\n                    throw new ParseError(argType, `Unexpected select type ${argType.value}`);\n                }\n            /* istanbul ignore next: never happens */\n            default:\n                throw new ParseError(argType, `Unexpected lexer token: ${argType.type}`);\n        }\n    }\n    parseBody(inPlural, atRoot) {\n        const tokens = [];\n        let content = null;\n        for (const lt of this.lexer) {\n            if (lt.type === 'argument') {\n                if (content)\n                    content = null;\n                tokens.push(this.parseArgToken(lt, inPlural));\n            }\n            else if (lt.type === 'octothorpe' && inPlural) {\n                if (content)\n                    content = null;\n                tokens.push({ type: 'octothorpe', ctx: getContext(lt) });\n            }\n            else if (lt.type === 'end' && !atRoot) {\n                return tokens;\n            }\n            else {\n                let value = lt.value;\n                if (!inPlural && lt.type === 'quoted' && value[0] === '#') {\n                    if (value.includes('{')) {\n                        const errMsg = `Unsupported escape pattern: ${value}`;\n                        throw new ParseError(lt, errMsg);\n                    }\n                    value = lt.text;\n                }\n                if (content) {\n                    content.value += value;\n                    content.ctx.text += lt.text;\n                    content.ctx.lineBreaks += lt.lineBreaks;\n                }\n                else {\n                    content = { type: 'content', value, ctx: getContext(lt) };\n                    tokens.push(content);\n                }\n            }\n        }\n        if (atRoot)\n            return tokens;\n        throw new ParseError(null, 'Unexpected message end');\n    }\n}\n/**\n * Parse an input string into an array of tokens\n *\n * @public\n * @remarks\n * The parser only supports the default `DOUBLE_OPTIONAL`\n * {@link http://www.icu-project.org/apiref/icu4c/messagepattern_8h.html#af6e0757e0eb81c980b01ee5d68a9978b | apostrophe mode}.\n */\nfunction parse(src, options = {}) {\n    const parser = new Parser(src, options);\n    return parser.parse();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@messageformat/parser/lib/parser.js\n");

/***/ })

};
;