"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark";
exports.ids = ["vendor-chunks/remark"];
exports.modules = {

/***/ "(rsc)/../../node_modules/remark/index.js":
/*!******************************************!*\
  !*** ../../node_modules/remark/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   remark: () => (/* binding */ remark)\n/* harmony export */ });\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remark-parse */ \"(rsc)/../../node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_stringify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-stringify */ \"(rsc)/../../node_modules/remark-stringify/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unified */ \"(rsc)/../../node_modules/unified/lib/index.js\");\n// Note: types exposed from `index.d.ts`\n\n\n\n\n/**\n * Create a new unified processor that already uses `remark-parse` and\n * `remark-stringify`.\n */\nconst remark = (0,unified__WEBPACK_IMPORTED_MODULE_0__.unified)().use(remark_parse__WEBPACK_IMPORTED_MODULE_1__[\"default\"]).use(remark_stringify__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).freeze()\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL3JlbWFyay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDc0M7QUFDUTtBQUNmOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNPLGVBQWUsZ0RBQU8sT0FBTyxvREFBVyxNQUFNLHdEQUFlIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvcmVtYXJrL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE5vdGU6IHR5cGVzIGV4cG9zZWQgZnJvbSBgaW5kZXguZC50c2BcbmltcG9ydCByZW1hcmtQYXJzZSBmcm9tICdyZW1hcmstcGFyc2UnXG5pbXBvcnQgcmVtYXJrU3RyaW5naWZ5IGZyb20gJ3JlbWFyay1zdHJpbmdpZnknXG5pbXBvcnQge3VuaWZpZWR9IGZyb20gJ3VuaWZpZWQnXG5cbi8qKlxuICogQ3JlYXRlIGEgbmV3IHVuaWZpZWQgcHJvY2Vzc29yIHRoYXQgYWxyZWFkeSB1c2VzIGByZW1hcmstcGFyc2VgIGFuZFxuICogYHJlbWFyay1zdHJpbmdpZnlgLlxuICovXG5leHBvcnQgY29uc3QgcmVtYXJrID0gdW5pZmllZCgpLnVzZShyZW1hcmtQYXJzZSkudXNlKHJlbWFya1N0cmluZ2lmeSkuZnJlZXplKClcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/remark/index.js\n");

/***/ })

};
;