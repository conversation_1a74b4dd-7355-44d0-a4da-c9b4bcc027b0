"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/_arx.js":
/*!*****************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/_arx.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCipher: () => (/* binding */ createCipher),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   sigma: () => (/* binding */ sigma)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/utils.js\");\n// Basic utils for ARX (add-rotate-xor) salsa and chacha ciphers.\n\n\n/*\nRFC8439 requires multi-step cipher stream, where\nauthKey starts with counter: 0, actual msg with counter: 1.\n\nFor this, we need a way to re-use nonce / counter:\n\n    const counter = new Uint8Array(4);\n    chacha(..., counter, ...); // counter is now 1\n    chacha(..., counter, ...); // counter is now 2\n\nThis is complicated:\n\n- 32-bit counters are enough, no need for 64-bit: max ArrayBuffer size in JS is 4GB\n- Original papers don't allow mutating counters\n- Counter overflow is undefined [^1]\n- Idea A: allow providing (nonce | counter) instead of just nonce, re-use it\n- Caveat: Cannot be re-used through all cases:\n- * chacha has (counter | nonce)\n- * xchacha has (nonce16 | counter | nonce16)\n- Idea B: separate nonce / counter and provide separate API for counter re-use\n- Caveat: there are different counter sizes depending on an algorithm.\n- salsa & chacha also differ in structures of key & sigma:\n  salsa20:      s[0] | k(4) | s[1] | nonce(2) | ctr(2) | s[2] | k(4) | s[3]\n  chacha:       s(4) | k(8) | ctr(1) | nonce(3)\n  chacha20orig: s(4) | k(8) | ctr(2) | nonce(2)\n- Idea C: helper method such as `setSalsaState(key, nonce, sigma, data)`\n- Caveat: we can't re-use counter array\n\nxchacha [^2] uses the subkey and remaining 8 byte nonce with ChaCha20 as normal\n(prefixed by 4 NUL bytes, since [RFC8439] specifies a 12-byte nonce).\n\n[^1]: https://mailarchive.ietf.org/arch/msg/cfrg/gsOnTJzcbgG6OqD8Sc0GO5aR_tU/\n[^2]: https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha#appendix-A.2\n*/\n// We can't make top-level var depend on utils.utf8ToBytes\n// because it's not present in all envs. Creating a similar fn here\nconst _utf8ToBytes = (str) => Uint8Array.from(str.split('').map((c) => c.charCodeAt(0)));\nconst sigma16 = _utf8ToBytes('expand 16-byte k');\nconst sigma32 = _utf8ToBytes('expand 32-byte k');\nconst sigma16_32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(sigma16);\nconst sigma32_32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(sigma32);\nconst sigma = sigma32_32.slice();\nfunction rotl(a, b) {\n    return (a << b) | (a >>> (32 - b));\n}\n// Is byte array aligned to 4 byte offset (u32)?\nfunction isAligned32(b) {\n    return b.byteOffset % 4 === 0;\n}\n// Salsa and Chacha block length is always 512-bit\nconst BLOCK_LEN = 64;\nconst BLOCK_LEN32 = 16;\n// new Uint32Array([2**32])   // => Uint32Array(1) [ 0 ]\n// new Uint32Array([2**32-1]) // => Uint32Array(1) [ 4294967295 ]\nconst MAX_COUNTER = 2 ** 32 - 1;\nconst U32_EMPTY = new Uint32Array();\nfunction runCipher(core, sigma, key, nonce, data, output, counter, rounds) {\n    const len = data.length;\n    const block = new Uint8Array(BLOCK_LEN);\n    const b32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(block);\n    // Make sure that buffers aligned to 4 bytes\n    const isAligned = isAligned32(data) && isAligned32(output);\n    const d32 = isAligned ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(data) : U32_EMPTY;\n    const o32 = isAligned ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(output) : U32_EMPTY;\n    for (let pos = 0; pos < len; counter++) {\n        core(sigma, key, nonce, b32, counter, rounds);\n        if (counter >= MAX_COUNTER)\n            throw new Error('arx: counter overflow');\n        const take = Math.min(BLOCK_LEN, len - pos);\n        // aligned to 4 bytes\n        if (isAligned && take === BLOCK_LEN) {\n            const pos32 = pos / 4;\n            if (pos % 4 !== 0)\n                throw new Error('arx: invalid block position');\n            for (let j = 0, posj; j < BLOCK_LEN32; j++) {\n                posj = pos32 + j;\n                o32[posj] = d32[posj] ^ b32[j];\n            }\n            pos += BLOCK_LEN;\n            continue;\n        }\n        for (let j = 0, posj; j < take; j++) {\n            posj = pos + j;\n            output[posj] = data[posj] ^ block[j];\n        }\n        pos += take;\n    }\n}\nfunction createCipher(core, opts) {\n    const { allowShortKeys, extendNonceFn, counterLength, counterRight, rounds } = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.checkOpts)({ allowShortKeys: false, counterLength: 8, counterRight: false, rounds: 20 }, opts);\n    if (typeof core !== 'function')\n        throw new Error('core must be a function');\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.number)(counterLength);\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.number)(rounds);\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bool)(counterRight);\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bool)(allowShortKeys);\n    return (key, nonce, data, output, counter = 0) => {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(key);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(nonce);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(data);\n        const len = data.length;\n        if (output === undefined)\n            output = new Uint8Array(len);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(output);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.number)(counter);\n        if (counter < 0 || counter >= MAX_COUNTER)\n            throw new Error('arx: counter overflow');\n        if (output.length < len)\n            throw new Error(`arx: output (${output.length}) is shorter than data (${len})`);\n        const toClean = [];\n        // Key & sigma\n        // key=16 -> sigma16, k=key|key\n        // key=32 -> sigma32, k=key\n        let l = key.length, k, sigma;\n        if (l === 32) {\n            toClean.push((k = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.copyBytes)(key)));\n            sigma = sigma32_32;\n        }\n        else if (l === 16 && allowShortKeys) {\n            k = new Uint8Array(32);\n            k.set(key);\n            k.set(key, 16);\n            sigma = sigma16_32;\n            toClean.push(k);\n        }\n        else {\n            throw new Error(`arx: invalid 32-byte key, got length=${l}`);\n        }\n        // Nonce\n        // salsa20:      8   (8-byte counter)\n        // chacha20orig: 8   (8-byte counter)\n        // chacha20:     12  (4-byte counter)\n        // xsalsa20:     24  (16 -> hsalsa,  8 -> old nonce)\n        // xchacha20:    24  (16 -> hchacha, 8 -> old nonce)\n        // Align nonce to 4 bytes\n        if (!isAligned32(nonce))\n            toClean.push((nonce = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.copyBytes)(nonce)));\n        const k32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(k);\n        // hsalsa & hchacha: handle extended nonce\n        if (extendNonceFn) {\n            if (nonce.length !== 24)\n                throw new Error(`arx: extended nonce must be 24 bytes`);\n            extendNonceFn(sigma, k32, (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(nonce.subarray(0, 16)), k32);\n            nonce = nonce.subarray(16);\n        }\n        // Handle nonce counter\n        const nonceNcLen = 16 - counterLength;\n        if (nonceNcLen !== nonce.length)\n            throw new Error(`arx: nonce must be ${nonceNcLen} or 16 bytes`);\n        // Pad counter when nonce is 64 bit\n        if (nonceNcLen !== 12) {\n            const nc = new Uint8Array(12);\n            nc.set(nonce, counterRight ? 0 : 12 - nonce.length);\n            nonce = nc;\n            toClean.push(nonce);\n        }\n        const n32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(nonce);\n        runCipher(core, sigma, k32, n32, data, output, counter, rounds);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(...toClean);\n        return output;\n    };\n}\n//# sourceMappingURL=_arx.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0Bub2JsZS9jaXBoZXJzL2VzbS9fYXJ4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDaUY7QUFDbkI7QUFDOUQ7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsK0JBQStCO0FBQy9CLCtCQUErQjs7QUFFL0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4Q0FBRztBQUN0QixtQkFBbUIsOENBQUc7QUFDZjtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQUc7QUFDbkI7QUFDQTtBQUNBLDRCQUE0Qiw4Q0FBRztBQUMvQiw0QkFBNEIsOENBQUc7QUFDL0Isc0JBQXNCLFdBQVc7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGlCQUFpQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsVUFBVTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFlBQVkscUVBQXFFLEVBQUUsb0RBQVMsR0FBRywwRUFBMEU7QUFDeks7QUFDQTtBQUNBLElBQUksa0RBQU87QUFDWCxJQUFJLGtEQUFPO0FBQ1gsSUFBSSxnREFBSztBQUNULElBQUksZ0RBQUs7QUFDVDtBQUNBLFFBQVEsaURBQU07QUFDZCxRQUFRLGlEQUFNO0FBQ2QsUUFBUSxpREFBTTtBQUNkO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaURBQU07QUFDZCxRQUFRLGtEQUFPO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLGNBQWMsMEJBQTBCLElBQUk7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLG9EQUFTO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0VBQW9FLEVBQUU7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLG9EQUFTO0FBQzNDLG9CQUFvQiw4Q0FBRztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw4Q0FBRztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELFlBQVk7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsOENBQUc7QUFDdkI7QUFDQSxRQUFRLGdEQUFLO0FBQ2I7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9Abm9ibGUvY2lwaGVycy9lc20vX2FyeC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYXNpYyB1dGlscyBmb3IgQVJYIChhZGQtcm90YXRlLXhvcikgc2Fsc2EgYW5kIGNoYWNoYSBjaXBoZXJzLlxuaW1wb3J0IHsgYm9vbCBhcyBhYm9vbCwgYnl0ZXMgYXMgYWJ5dGVzLCBudW1iZXIgYXMgYW51bWJlciB9IGZyb20gJy4vX2Fzc2VydC5qcyc7XG5pbXBvcnQgeyBjaGVja09wdHMsIGNsZWFuLCBjb3B5Qnl0ZXMsIHUzMiB9IGZyb20gJy4vdXRpbHMuanMnO1xuLypcblJGQzg0MzkgcmVxdWlyZXMgbXVsdGktc3RlcCBjaXBoZXIgc3RyZWFtLCB3aGVyZVxuYXV0aEtleSBzdGFydHMgd2l0aCBjb3VudGVyOiAwLCBhY3R1YWwgbXNnIHdpdGggY291bnRlcjogMS5cblxuRm9yIHRoaXMsIHdlIG5lZWQgYSB3YXkgdG8gcmUtdXNlIG5vbmNlIC8gY291bnRlcjpcblxuICAgIGNvbnN0IGNvdW50ZXIgPSBuZXcgVWludDhBcnJheSg0KTtcbiAgICBjaGFjaGEoLi4uLCBjb3VudGVyLCAuLi4pOyAvLyBjb3VudGVyIGlzIG5vdyAxXG4gICAgY2hhY2hhKC4uLiwgY291bnRlciwgLi4uKTsgLy8gY291bnRlciBpcyBub3cgMlxuXG5UaGlzIGlzIGNvbXBsaWNhdGVkOlxuXG4tIDMyLWJpdCBjb3VudGVycyBhcmUgZW5vdWdoLCBubyBuZWVkIGZvciA2NC1iaXQ6IG1heCBBcnJheUJ1ZmZlciBzaXplIGluIEpTIGlzIDRHQlxuLSBPcmlnaW5hbCBwYXBlcnMgZG9uJ3QgYWxsb3cgbXV0YXRpbmcgY291bnRlcnNcbi0gQ291bnRlciBvdmVyZmxvdyBpcyB1bmRlZmluZWQgW14xXVxuLSBJZGVhIEE6IGFsbG93IHByb3ZpZGluZyAobm9uY2UgfCBjb3VudGVyKSBpbnN0ZWFkIG9mIGp1c3Qgbm9uY2UsIHJlLXVzZSBpdFxuLSBDYXZlYXQ6IENhbm5vdCBiZSByZS11c2VkIHRocm91Z2ggYWxsIGNhc2VzOlxuLSAqIGNoYWNoYSBoYXMgKGNvdW50ZXIgfCBub25jZSlcbi0gKiB4Y2hhY2hhIGhhcyAobm9uY2UxNiB8IGNvdW50ZXIgfCBub25jZTE2KVxuLSBJZGVhIEI6IHNlcGFyYXRlIG5vbmNlIC8gY291bnRlciBhbmQgcHJvdmlkZSBzZXBhcmF0ZSBBUEkgZm9yIGNvdW50ZXIgcmUtdXNlXG4tIENhdmVhdDogdGhlcmUgYXJlIGRpZmZlcmVudCBjb3VudGVyIHNpemVzIGRlcGVuZGluZyBvbiBhbiBhbGdvcml0aG0uXG4tIHNhbHNhICYgY2hhY2hhIGFsc28gZGlmZmVyIGluIHN0cnVjdHVyZXMgb2Yga2V5ICYgc2lnbWE6XG4gIHNhbHNhMjA6ICAgICAgc1swXSB8IGsoNCkgfCBzWzFdIHwgbm9uY2UoMikgfCBjdHIoMikgfCBzWzJdIHwgayg0KSB8IHNbM11cbiAgY2hhY2hhOiAgICAgICBzKDQpIHwgayg4KSB8IGN0cigxKSB8IG5vbmNlKDMpXG4gIGNoYWNoYTIwb3JpZzogcyg0KSB8IGsoOCkgfCBjdHIoMikgfCBub25jZSgyKVxuLSBJZGVhIEM6IGhlbHBlciBtZXRob2Qgc3VjaCBhcyBgc2V0U2Fsc2FTdGF0ZShrZXksIG5vbmNlLCBzaWdtYSwgZGF0YSlgXG4tIENhdmVhdDogd2UgY2FuJ3QgcmUtdXNlIGNvdW50ZXIgYXJyYXlcblxueGNoYWNoYSBbXjJdIHVzZXMgdGhlIHN1YmtleSBhbmQgcmVtYWluaW5nIDggYnl0ZSBub25jZSB3aXRoIENoYUNoYTIwIGFzIG5vcm1hbFxuKHByZWZpeGVkIGJ5IDQgTlVMIGJ5dGVzLCBzaW5jZSBbUkZDODQzOV0gc3BlY2lmaWVzIGEgMTItYnl0ZSBub25jZSkuXG5cblteMV06IGh0dHBzOi8vbWFpbGFyY2hpdmUuaWV0Zi5vcmcvYXJjaC9tc2cvY2ZyZy9nc09uVEp6Y2JnRzZPcUQ4U2MwR081YVJfdFUvXG5bXjJdOiBodHRwczovL2RhdGF0cmFja2VyLmlldGYub3JnL2RvYy9odG1sL2RyYWZ0LWlydGYtY2ZyZy14Y2hhY2hhI2FwcGVuZGl4LUEuMlxuKi9cbi8vIFdlIGNhbid0IG1ha2UgdG9wLWxldmVsIHZhciBkZXBlbmQgb24gdXRpbHMudXRmOFRvQnl0ZXNcbi8vIGJlY2F1c2UgaXQncyBub3QgcHJlc2VudCBpbiBhbGwgZW52cy4gQ3JlYXRpbmcgYSBzaW1pbGFyIGZuIGhlcmVcbmNvbnN0IF91dGY4VG9CeXRlcyA9IChzdHIpID0+IFVpbnQ4QXJyYXkuZnJvbShzdHIuc3BsaXQoJycpLm1hcCgoYykgPT4gYy5jaGFyQ29kZUF0KDApKSk7XG5jb25zdCBzaWdtYTE2ID0gX3V0ZjhUb0J5dGVzKCdleHBhbmQgMTYtYnl0ZSBrJyk7XG5jb25zdCBzaWdtYTMyID0gX3V0ZjhUb0J5dGVzKCdleHBhbmQgMzItYnl0ZSBrJyk7XG5jb25zdCBzaWdtYTE2XzMyID0gdTMyKHNpZ21hMTYpO1xuY29uc3Qgc2lnbWEzMl8zMiA9IHUzMihzaWdtYTMyKTtcbmV4cG9ydCBjb25zdCBzaWdtYSA9IHNpZ21hMzJfMzIuc2xpY2UoKTtcbmV4cG9ydCBmdW5jdGlvbiByb3RsKGEsIGIpIHtcbiAgICByZXR1cm4gKGEgPDwgYikgfCAoYSA+Pj4gKDMyIC0gYikpO1xufVxuLy8gSXMgYnl0ZSBhcnJheSBhbGlnbmVkIHRvIDQgYnl0ZSBvZmZzZXQgKHUzMik/XG5mdW5jdGlvbiBpc0FsaWduZWQzMihiKSB7XG4gICAgcmV0dXJuIGIuYnl0ZU9mZnNldCAlIDQgPT09IDA7XG59XG4vLyBTYWxzYSBhbmQgQ2hhY2hhIGJsb2NrIGxlbmd0aCBpcyBhbHdheXMgNTEyLWJpdFxuY29uc3QgQkxPQ0tfTEVOID0gNjQ7XG5jb25zdCBCTE9DS19MRU4zMiA9IDE2O1xuLy8gbmV3IFVpbnQzMkFycmF5KFsyKiozMl0pICAgLy8gPT4gVWludDMyQXJyYXkoMSkgWyAwIF1cbi8vIG5ldyBVaW50MzJBcnJheShbMioqMzItMV0pIC8vID0+IFVpbnQzMkFycmF5KDEpIFsgNDI5NDk2NzI5NSBdXG5jb25zdCBNQVhfQ09VTlRFUiA9IDIgKiogMzIgLSAxO1xuY29uc3QgVTMyX0VNUFRZID0gbmV3IFVpbnQzMkFycmF5KCk7XG5mdW5jdGlvbiBydW5DaXBoZXIoY29yZSwgc2lnbWEsIGtleSwgbm9uY2UsIGRhdGEsIG91dHB1dCwgY291bnRlciwgcm91bmRzKSB7XG4gICAgY29uc3QgbGVuID0gZGF0YS5sZW5ndGg7XG4gICAgY29uc3QgYmxvY2sgPSBuZXcgVWludDhBcnJheShCTE9DS19MRU4pO1xuICAgIGNvbnN0IGIzMiA9IHUzMihibG9jayk7XG4gICAgLy8gTWFrZSBzdXJlIHRoYXQgYnVmZmVycyBhbGlnbmVkIHRvIDQgYnl0ZXNcbiAgICBjb25zdCBpc0FsaWduZWQgPSBpc0FsaWduZWQzMihkYXRhKSAmJiBpc0FsaWduZWQzMihvdXRwdXQpO1xuICAgIGNvbnN0IGQzMiA9IGlzQWxpZ25lZCA/IHUzMihkYXRhKSA6IFUzMl9FTVBUWTtcbiAgICBjb25zdCBvMzIgPSBpc0FsaWduZWQgPyB1MzIob3V0cHV0KSA6IFUzMl9FTVBUWTtcbiAgICBmb3IgKGxldCBwb3MgPSAwOyBwb3MgPCBsZW47IGNvdW50ZXIrKykge1xuICAgICAgICBjb3JlKHNpZ21hLCBrZXksIG5vbmNlLCBiMzIsIGNvdW50ZXIsIHJvdW5kcyk7XG4gICAgICAgIGlmIChjb3VudGVyID49IE1BWF9DT1VOVEVSKVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdhcng6IGNvdW50ZXIgb3ZlcmZsb3cnKTtcbiAgICAgICAgY29uc3QgdGFrZSA9IE1hdGgubWluKEJMT0NLX0xFTiwgbGVuIC0gcG9zKTtcbiAgICAgICAgLy8gYWxpZ25lZCB0byA0IGJ5dGVzXG4gICAgICAgIGlmIChpc0FsaWduZWQgJiYgdGFrZSA9PT0gQkxPQ0tfTEVOKSB7XG4gICAgICAgICAgICBjb25zdCBwb3MzMiA9IHBvcyAvIDQ7XG4gICAgICAgICAgICBpZiAocG9zICUgNCAhPT0gMClcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2FyeDogaW52YWxpZCBibG9jayBwb3NpdGlvbicpO1xuICAgICAgICAgICAgZm9yIChsZXQgaiA9IDAsIHBvc2o7IGogPCBCTE9DS19MRU4zMjsgaisrKSB7XG4gICAgICAgICAgICAgICAgcG9zaiA9IHBvczMyICsgajtcbiAgICAgICAgICAgICAgICBvMzJbcG9zal0gPSBkMzJbcG9zal0gXiBiMzJbal07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBwb3MgKz0gQkxPQ0tfTEVOO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQgaiA9IDAsIHBvc2o7IGogPCB0YWtlOyBqKyspIHtcbiAgICAgICAgICAgIHBvc2ogPSBwb3MgKyBqO1xuICAgICAgICAgICAgb3V0cHV0W3Bvc2pdID0gZGF0YVtwb3NqXSBeIGJsb2NrW2pdO1xuICAgICAgICB9XG4gICAgICAgIHBvcyArPSB0YWtlO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDaXBoZXIoY29yZSwgb3B0cykge1xuICAgIGNvbnN0IHsgYWxsb3dTaG9ydEtleXMsIGV4dGVuZE5vbmNlRm4sIGNvdW50ZXJMZW5ndGgsIGNvdW50ZXJSaWdodCwgcm91bmRzIH0gPSBjaGVja09wdHMoeyBhbGxvd1Nob3J0S2V5czogZmFsc2UsIGNvdW50ZXJMZW5ndGg6IDgsIGNvdW50ZXJSaWdodDogZmFsc2UsIHJvdW5kczogMjAgfSwgb3B0cyk7XG4gICAgaWYgKHR5cGVvZiBjb3JlICE9PSAnZnVuY3Rpb24nKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2NvcmUgbXVzdCBiZSBhIGZ1bmN0aW9uJyk7XG4gICAgYW51bWJlcihjb3VudGVyTGVuZ3RoKTtcbiAgICBhbnVtYmVyKHJvdW5kcyk7XG4gICAgYWJvb2woY291bnRlclJpZ2h0KTtcbiAgICBhYm9vbChhbGxvd1Nob3J0S2V5cyk7XG4gICAgcmV0dXJuIChrZXksIG5vbmNlLCBkYXRhLCBvdXRwdXQsIGNvdW50ZXIgPSAwKSA9PiB7XG4gICAgICAgIGFieXRlcyhrZXkpO1xuICAgICAgICBhYnl0ZXMobm9uY2UpO1xuICAgICAgICBhYnl0ZXMoZGF0YSk7XG4gICAgICAgIGNvbnN0IGxlbiA9IGRhdGEubGVuZ3RoO1xuICAgICAgICBpZiAob3V0cHV0ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICBvdXRwdXQgPSBuZXcgVWludDhBcnJheShsZW4pO1xuICAgICAgICBhYnl0ZXMob3V0cHV0KTtcbiAgICAgICAgYW51bWJlcihjb3VudGVyKTtcbiAgICAgICAgaWYgKGNvdW50ZXIgPCAwIHx8IGNvdW50ZXIgPj0gTUFYX0NPVU5URVIpXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2FyeDogY291bnRlciBvdmVyZmxvdycpO1xuICAgICAgICBpZiAob3V0cHV0Lmxlbmd0aCA8IGxlbilcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgYXJ4OiBvdXRwdXQgKCR7b3V0cHV0Lmxlbmd0aH0pIGlzIHNob3J0ZXIgdGhhbiBkYXRhICgke2xlbn0pYCk7XG4gICAgICAgIGNvbnN0IHRvQ2xlYW4gPSBbXTtcbiAgICAgICAgLy8gS2V5ICYgc2lnbWFcbiAgICAgICAgLy8ga2V5PTE2IC0+IHNpZ21hMTYsIGs9a2V5fGtleVxuICAgICAgICAvLyBrZXk9MzIgLT4gc2lnbWEzMiwgaz1rZXlcbiAgICAgICAgbGV0IGwgPSBrZXkubGVuZ3RoLCBrLCBzaWdtYTtcbiAgICAgICAgaWYgKGwgPT09IDMyKSB7XG4gICAgICAgICAgICB0b0NsZWFuLnB1c2goKGsgPSBjb3B5Qnl0ZXMoa2V5KSkpO1xuICAgICAgICAgICAgc2lnbWEgPSBzaWdtYTMyXzMyO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGwgPT09IDE2ICYmIGFsbG93U2hvcnRLZXlzKSB7XG4gICAgICAgICAgICBrID0gbmV3IFVpbnQ4QXJyYXkoMzIpO1xuICAgICAgICAgICAgay5zZXQoa2V5KTtcbiAgICAgICAgICAgIGsuc2V0KGtleSwgMTYpO1xuICAgICAgICAgICAgc2lnbWEgPSBzaWdtYTE2XzMyO1xuICAgICAgICAgICAgdG9DbGVhbi5wdXNoKGspO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBhcng6IGludmFsaWQgMzItYnl0ZSBrZXksIGdvdCBsZW5ndGg9JHtsfWApO1xuICAgICAgICB9XG4gICAgICAgIC8vIE5vbmNlXG4gICAgICAgIC8vIHNhbHNhMjA6ICAgICAgOCAgICg4LWJ5dGUgY291bnRlcilcbiAgICAgICAgLy8gY2hhY2hhMjBvcmlnOiA4ICAgKDgtYnl0ZSBjb3VudGVyKVxuICAgICAgICAvLyBjaGFjaGEyMDogICAgIDEyICAoNC1ieXRlIGNvdW50ZXIpXG4gICAgICAgIC8vIHhzYWxzYTIwOiAgICAgMjQgICgxNiAtPiBoc2Fsc2EsICA4IC0+IG9sZCBub25jZSlcbiAgICAgICAgLy8geGNoYWNoYTIwOiAgICAyNCAgKDE2IC0+IGhjaGFjaGEsIDggLT4gb2xkIG5vbmNlKVxuICAgICAgICAvLyBBbGlnbiBub25jZSB0byA0IGJ5dGVzXG4gICAgICAgIGlmICghaXNBbGlnbmVkMzIobm9uY2UpKVxuICAgICAgICAgICAgdG9DbGVhbi5wdXNoKChub25jZSA9IGNvcHlCeXRlcyhub25jZSkpKTtcbiAgICAgICAgY29uc3QgazMyID0gdTMyKGspO1xuICAgICAgICAvLyBoc2Fsc2EgJiBoY2hhY2hhOiBoYW5kbGUgZXh0ZW5kZWQgbm9uY2VcbiAgICAgICAgaWYgKGV4dGVuZE5vbmNlRm4pIHtcbiAgICAgICAgICAgIGlmIChub25jZS5sZW5ndGggIT09IDI0KVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgYXJ4OiBleHRlbmRlZCBub25jZSBtdXN0IGJlIDI0IGJ5dGVzYCk7XG4gICAgICAgICAgICBleHRlbmROb25jZUZuKHNpZ21hLCBrMzIsIHUzMihub25jZS5zdWJhcnJheSgwLCAxNikpLCBrMzIpO1xuICAgICAgICAgICAgbm9uY2UgPSBub25jZS5zdWJhcnJheSgxNik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSGFuZGxlIG5vbmNlIGNvdW50ZXJcbiAgICAgICAgY29uc3Qgbm9uY2VOY0xlbiA9IDE2IC0gY291bnRlckxlbmd0aDtcbiAgICAgICAgaWYgKG5vbmNlTmNMZW4gIT09IG5vbmNlLmxlbmd0aClcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgYXJ4OiBub25jZSBtdXN0IGJlICR7bm9uY2VOY0xlbn0gb3IgMTYgYnl0ZXNgKTtcbiAgICAgICAgLy8gUGFkIGNvdW50ZXIgd2hlbiBub25jZSBpcyA2NCBiaXRcbiAgICAgICAgaWYgKG5vbmNlTmNMZW4gIT09IDEyKSB7XG4gICAgICAgICAgICBjb25zdCBuYyA9IG5ldyBVaW50OEFycmF5KDEyKTtcbiAgICAgICAgICAgIG5jLnNldChub25jZSwgY291bnRlclJpZ2h0ID8gMCA6IDEyIC0gbm9uY2UubGVuZ3RoKTtcbiAgICAgICAgICAgIG5vbmNlID0gbmM7XG4gICAgICAgICAgICB0b0NsZWFuLnB1c2gobm9uY2UpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG4zMiA9IHUzMihub25jZSk7XG4gICAgICAgIHJ1bkNpcGhlcihjb3JlLCBzaWdtYSwgazMyLCBuMzIsIGRhdGEsIG91dHB1dCwgY291bnRlciwgcm91bmRzKTtcbiAgICAgICAgY2xlYW4oLi4udG9DbGVhbik7XG4gICAgICAgIHJldHVybiBvdXRwdXQ7XG4gICAgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV9hcnguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/_arx.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js":
/*!********************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/_assert.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bool: () => (/* binding */ bool),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   output: () => (/* binding */ output)\n/* harmony export */ });\nfunction number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`positive integer expected, not ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`boolean expected, not ${b}`);\n}\nfunction isBytes(a) {\n    return (a instanceof Uint8Array ||\n        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));\n}\nfunction bytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Uint8Array expected of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('hash must be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\n\nconst assert = { number, bool, bytes, hash, exists, output };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (assert);\n//# sourceMappingURL=_assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/_poly1305.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/_poly1305.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   poly1305: () => (/* binding */ poly1305),\n/* harmony export */   wrapConstructorWithKey: () => (/* binding */ wrapConstructorWithKey)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/utils.js\");\n\n\n// Poly1305 is a fast and parallel secret-key message-authentication code.\n// https://cr.yp.to/mac.html, https://cr.yp.to/mac/poly1305-20050329.pdf\n// https://datatracker.ietf.org/doc/html/rfc8439\n// Based on Public Domain poly1305-donna https://github.com/floodyberry/poly1305-donna\nconst u8to16 = (a, i) => (a[i++] & 0xff) | ((a[i++] & 0xff) << 8);\nclass Poly1305 {\n    constructor(key) {\n        this.blockLen = 16;\n        this.outputLen = 16;\n        this.buffer = new Uint8Array(16);\n        this.r = new Uint16Array(10);\n        this.h = new Uint16Array(10);\n        this.pad = new Uint16Array(8);\n        this.pos = 0;\n        this.finished = false;\n        key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(key);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(key, 32);\n        const t0 = u8to16(key, 0);\n        const t1 = u8to16(key, 2);\n        const t2 = u8to16(key, 4);\n        const t3 = u8to16(key, 6);\n        const t4 = u8to16(key, 8);\n        const t5 = u8to16(key, 10);\n        const t6 = u8to16(key, 12);\n        const t7 = u8to16(key, 14);\n        // https://github.com/floodyberry/poly1305-donna/blob/e6ad6e091d30d7f4ec2d4f978be1fcfcbce72781/poly1305-donna-16.h#L47\n        this.r[0] = t0 & 0x1fff;\n        this.r[1] = ((t0 >>> 13) | (t1 << 3)) & 0x1fff;\n        this.r[2] = ((t1 >>> 10) | (t2 << 6)) & 0x1f03;\n        this.r[3] = ((t2 >>> 7) | (t3 << 9)) & 0x1fff;\n        this.r[4] = ((t3 >>> 4) | (t4 << 12)) & 0x00ff;\n        this.r[5] = (t4 >>> 1) & 0x1ffe;\n        this.r[6] = ((t4 >>> 14) | (t5 << 2)) & 0x1fff;\n        this.r[7] = ((t5 >>> 11) | (t6 << 5)) & 0x1f81;\n        this.r[8] = ((t6 >>> 8) | (t7 << 8)) & 0x1fff;\n        this.r[9] = (t7 >>> 5) & 0x007f;\n        for (let i = 0; i < 8; i++)\n            this.pad[i] = u8to16(key, 16 + 2 * i);\n    }\n    process(data, offset, isLast = false) {\n        const hibit = isLast ? 0 : 1 << 11;\n        const { h, r } = this;\n        const r0 = r[0];\n        const r1 = r[1];\n        const r2 = r[2];\n        const r3 = r[3];\n        const r4 = r[4];\n        const r5 = r[5];\n        const r6 = r[6];\n        const r7 = r[7];\n        const r8 = r[8];\n        const r9 = r[9];\n        const t0 = u8to16(data, offset + 0);\n        const t1 = u8to16(data, offset + 2);\n        const t2 = u8to16(data, offset + 4);\n        const t3 = u8to16(data, offset + 6);\n        const t4 = u8to16(data, offset + 8);\n        const t5 = u8to16(data, offset + 10);\n        const t6 = u8to16(data, offset + 12);\n        const t7 = u8to16(data, offset + 14);\n        let h0 = h[0] + (t0 & 0x1fff);\n        let h1 = h[1] + (((t0 >>> 13) | (t1 << 3)) & 0x1fff);\n        let h2 = h[2] + (((t1 >>> 10) | (t2 << 6)) & 0x1fff);\n        let h3 = h[3] + (((t2 >>> 7) | (t3 << 9)) & 0x1fff);\n        let h4 = h[4] + (((t3 >>> 4) | (t4 << 12)) & 0x1fff);\n        let h5 = h[5] + ((t4 >>> 1) & 0x1fff);\n        let h6 = h[6] + (((t4 >>> 14) | (t5 << 2)) & 0x1fff);\n        let h7 = h[7] + (((t5 >>> 11) | (t6 << 5)) & 0x1fff);\n        let h8 = h[8] + (((t6 >>> 8) | (t7 << 8)) & 0x1fff);\n        let h9 = h[9] + ((t7 >>> 5) | hibit);\n        let c = 0;\n        let d0 = c + h0 * r0 + h1 * (5 * r9) + h2 * (5 * r8) + h3 * (5 * r7) + h4 * (5 * r6);\n        c = d0 >>> 13;\n        d0 &= 0x1fff;\n        d0 += h5 * (5 * r5) + h6 * (5 * r4) + h7 * (5 * r3) + h8 * (5 * r2) + h9 * (5 * r1);\n        c += d0 >>> 13;\n        d0 &= 0x1fff;\n        let d1 = c + h0 * r1 + h1 * r0 + h2 * (5 * r9) + h3 * (5 * r8) + h4 * (5 * r7);\n        c = d1 >>> 13;\n        d1 &= 0x1fff;\n        d1 += h5 * (5 * r6) + h6 * (5 * r5) + h7 * (5 * r4) + h8 * (5 * r3) + h9 * (5 * r2);\n        c += d1 >>> 13;\n        d1 &= 0x1fff;\n        let d2 = c + h0 * r2 + h1 * r1 + h2 * r0 + h3 * (5 * r9) + h4 * (5 * r8);\n        c = d2 >>> 13;\n        d2 &= 0x1fff;\n        d2 += h5 * (5 * r7) + h6 * (5 * r6) + h7 * (5 * r5) + h8 * (5 * r4) + h9 * (5 * r3);\n        c += d2 >>> 13;\n        d2 &= 0x1fff;\n        let d3 = c + h0 * r3 + h1 * r2 + h2 * r1 + h3 * r0 + h4 * (5 * r9);\n        c = d3 >>> 13;\n        d3 &= 0x1fff;\n        d3 += h5 * (5 * r8) + h6 * (5 * r7) + h7 * (5 * r6) + h8 * (5 * r5) + h9 * (5 * r4);\n        c += d3 >>> 13;\n        d3 &= 0x1fff;\n        let d4 = c + h0 * r4 + h1 * r3 + h2 * r2 + h3 * r1 + h4 * r0;\n        c = d4 >>> 13;\n        d4 &= 0x1fff;\n        d4 += h5 * (5 * r9) + h6 * (5 * r8) + h7 * (5 * r7) + h8 * (5 * r6) + h9 * (5 * r5);\n        c += d4 >>> 13;\n        d4 &= 0x1fff;\n        let d5 = c + h0 * r5 + h1 * r4 + h2 * r3 + h3 * r2 + h4 * r1;\n        c = d5 >>> 13;\n        d5 &= 0x1fff;\n        d5 += h5 * r0 + h6 * (5 * r9) + h7 * (5 * r8) + h8 * (5 * r7) + h9 * (5 * r6);\n        c += d5 >>> 13;\n        d5 &= 0x1fff;\n        let d6 = c + h0 * r6 + h1 * r5 + h2 * r4 + h3 * r3 + h4 * r2;\n        c = d6 >>> 13;\n        d6 &= 0x1fff;\n        d6 += h5 * r1 + h6 * r0 + h7 * (5 * r9) + h8 * (5 * r8) + h9 * (5 * r7);\n        c += d6 >>> 13;\n        d6 &= 0x1fff;\n        let d7 = c + h0 * r7 + h1 * r6 + h2 * r5 + h3 * r4 + h4 * r3;\n        c = d7 >>> 13;\n        d7 &= 0x1fff;\n        d7 += h5 * r2 + h6 * r1 + h7 * r0 + h8 * (5 * r9) + h9 * (5 * r8);\n        c += d7 >>> 13;\n        d7 &= 0x1fff;\n        let d8 = c + h0 * r8 + h1 * r7 + h2 * r6 + h3 * r5 + h4 * r4;\n        c = d8 >>> 13;\n        d8 &= 0x1fff;\n        d8 += h5 * r3 + h6 * r2 + h7 * r1 + h8 * r0 + h9 * (5 * r9);\n        c += d8 >>> 13;\n        d8 &= 0x1fff;\n        let d9 = c + h0 * r9 + h1 * r8 + h2 * r7 + h3 * r6 + h4 * r5;\n        c = d9 >>> 13;\n        d9 &= 0x1fff;\n        d9 += h5 * r4 + h6 * r3 + h7 * r2 + h8 * r1 + h9 * r0;\n        c += d9 >>> 13;\n        d9 &= 0x1fff;\n        c = ((c << 2) + c) | 0;\n        c = (c + d0) | 0;\n        d0 = c & 0x1fff;\n        c = c >>> 13;\n        d1 += c;\n        h[0] = d0;\n        h[1] = d1;\n        h[2] = d2;\n        h[3] = d3;\n        h[4] = d4;\n        h[5] = d5;\n        h[6] = d6;\n        h[7] = d7;\n        h[8] = d8;\n        h[9] = d9;\n    }\n    finalize() {\n        const { h, pad } = this;\n        const g = new Uint16Array(10);\n        let c = h[1] >>> 13;\n        h[1] &= 0x1fff;\n        for (let i = 2; i < 10; i++) {\n            h[i] += c;\n            c = h[i] >>> 13;\n            h[i] &= 0x1fff;\n        }\n        h[0] += c * 5;\n        c = h[0] >>> 13;\n        h[0] &= 0x1fff;\n        h[1] += c;\n        c = h[1] >>> 13;\n        h[1] &= 0x1fff;\n        h[2] += c;\n        g[0] = h[0] + 5;\n        c = g[0] >>> 13;\n        g[0] &= 0x1fff;\n        for (let i = 1; i < 10; i++) {\n            g[i] = h[i] + c;\n            c = g[i] >>> 13;\n            g[i] &= 0x1fff;\n        }\n        g[9] -= 1 << 13;\n        let mask = (c ^ 1) - 1;\n        for (let i = 0; i < 10; i++)\n            g[i] &= mask;\n        mask = ~mask;\n        for (let i = 0; i < 10; i++)\n            h[i] = (h[i] & mask) | g[i];\n        h[0] = (h[0] | (h[1] << 13)) & 0xffff;\n        h[1] = ((h[1] >>> 3) | (h[2] << 10)) & 0xffff;\n        h[2] = ((h[2] >>> 6) | (h[3] << 7)) & 0xffff;\n        h[3] = ((h[3] >>> 9) | (h[4] << 4)) & 0xffff;\n        h[4] = ((h[4] >>> 12) | (h[5] << 1) | (h[6] << 14)) & 0xffff;\n        h[5] = ((h[6] >>> 2) | (h[7] << 11)) & 0xffff;\n        h[6] = ((h[7] >>> 5) | (h[8] << 8)) & 0xffff;\n        h[7] = ((h[8] >>> 8) | (h[9] << 5)) & 0xffff;\n        let f = h[0] + pad[0];\n        h[0] = f & 0xffff;\n        for (let i = 1; i < 8; i++) {\n            f = (((h[i] + pad[i]) | 0) + (f >>> 16)) | 0;\n            h[i] = f & 0xffff;\n        }\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(g);\n    }\n    update(data) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        const { buffer, blockLen } = this;\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input\n            if (take === blockLen) {\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(data, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(buffer, 0, false);\n                this.pos = 0;\n            }\n        }\n        return this;\n    }\n    destroy() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(this.h, this.r, this.buffer, this.pad);\n    }\n    digestInto(out) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.exists)(this);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.output)(out, this);\n        this.finished = true;\n        const { buffer, h } = this;\n        let { pos } = this;\n        if (pos) {\n            buffer[pos++] = 1;\n            for (; pos < 16; pos++)\n                buffer[pos] = 0;\n            this.process(buffer, 0, true);\n        }\n        this.finalize();\n        let opos = 0;\n        for (let i = 0; i < 8; i++) {\n            out[opos++] = h[i] >>> 0;\n            out[opos++] = h[i] >>> 8;\n        }\n        return out;\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n}\nfunction wrapConstructorWithKey(hashCons) {\n    const hashC = (msg, key) => hashCons(key).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(msg)).digest();\n    const tmp = hashCons(new Uint8Array(32));\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (key) => hashCons(key);\n    return hashC;\n}\nconst poly1305 = wrapConstructorWithKey((key) => new Poly1305(key));\n//# sourceMappingURL=_poly1305.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/_poly1305.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/chacha.js":
/*!*******************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/chacha.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _poly1305_aead: () => (/* binding */ _poly1305_aead),\n/* harmony export */   chacha12: () => (/* binding */ chacha12),\n/* harmony export */   chacha20: () => (/* binding */ chacha20),\n/* harmony export */   chacha20orig: () => (/* binding */ chacha20orig),\n/* harmony export */   chacha20poly1305: () => (/* binding */ chacha20poly1305),\n/* harmony export */   chacha8: () => (/* binding */ chacha8),\n/* harmony export */   hchacha: () => (/* binding */ hchacha),\n/* harmony export */   xchacha20: () => (/* binding */ xchacha20),\n/* harmony export */   xchacha20poly1305: () => (/* binding */ xchacha20poly1305)\n/* harmony export */ });\n/* harmony import */ var _arx_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_arx.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_arx.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./_assert.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\");\n/* harmony import */ var _poly1305_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_poly1305.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_poly1305.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/utils.js\");\n// prettier-ignore\n\n\n\n\n// ChaCha20 stream cipher was released in 2008. ChaCha aims to increase\n// the diffusion per round, but had slightly less cryptanalysis.\n// https://cr.yp.to/chacha.html, http://cr.yp.to/chacha/chacha-20080128.pdf\n/**\n * ChaCha core function.\n */\n// prettier-ignore\nfunction chachaCore(s, k, n, out, cnt, rounds = 20) {\n    let y00 = s[0], y01 = s[1], y02 = s[2], y03 = s[3], // \"expa\"   \"nd 3\"  \"2-by\"  \"te k\"\n    y04 = k[0], y05 = k[1], y06 = k[2], y07 = k[3], // Key      Key     Key     Key\n    y08 = k[4], y09 = k[5], y10 = k[6], y11 = k[7], // Key      Key     Key     Key\n    y12 = cnt, y13 = n[0], y14 = n[1], y15 = n[2]; // Counter  Counter\tNonce   Nonce\n    // Save state to temporary variables\n    let x00 = y00, x01 = y01, x02 = y02, x03 = y03, x04 = y04, x05 = y05, x06 = y06, x07 = y07, x08 = y08, x09 = y09, x10 = y10, x11 = y11, x12 = y12, x13 = y13, x14 = y14, x15 = y15;\n    for (let r = 0; r < rounds; r += 2) {\n        x00 = (x00 + x04) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x00, 16);\n        x08 = (x08 + x12) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x08, 12);\n        x00 = (x00 + x04) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x00, 8);\n        x08 = (x08 + x12) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x08, 7);\n        x01 = (x01 + x05) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x01, 16);\n        x09 = (x09 + x13) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x09, 12);\n        x01 = (x01 + x05) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x01, 8);\n        x09 = (x09 + x13) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x09, 7);\n        x02 = (x02 + x06) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x02, 16);\n        x10 = (x10 + x14) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x10, 12);\n        x02 = (x02 + x06) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x02, 8);\n        x10 = (x10 + x14) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x10, 7);\n        x03 = (x03 + x07) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x03, 16);\n        x11 = (x11 + x15) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x11, 12);\n        x03 = (x03 + x07) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x03, 8);\n        x11 = (x11 + x15) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x11, 7);\n        x00 = (x00 + x05) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x00, 16);\n        x10 = (x10 + x15) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x10, 12);\n        x00 = (x00 + x05) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x00, 8);\n        x10 = (x10 + x15) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x10, 7);\n        x01 = (x01 + x06) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x01, 16);\n        x11 = (x11 + x12) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x11, 12);\n        x01 = (x01 + x06) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x01, 8);\n        x11 = (x11 + x12) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x11, 7);\n        x02 = (x02 + x07) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x02, 16);\n        x08 = (x08 + x13) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x08, 12);\n        x02 = (x02 + x07) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x02, 8);\n        x08 = (x08 + x13) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x08, 7);\n        x03 = (x03 + x04) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x03, 16);\n        x09 = (x09 + x14) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x09, 12);\n        x03 = (x03 + x04) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x03, 8);\n        x09 = (x09 + x14) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x09, 7);\n    }\n    // Write output\n    let oi = 0;\n    out[oi++] = (y00 + x00) | 0;\n    out[oi++] = (y01 + x01) | 0;\n    out[oi++] = (y02 + x02) | 0;\n    out[oi++] = (y03 + x03) | 0;\n    out[oi++] = (y04 + x04) | 0;\n    out[oi++] = (y05 + x05) | 0;\n    out[oi++] = (y06 + x06) | 0;\n    out[oi++] = (y07 + x07) | 0;\n    out[oi++] = (y08 + x08) | 0;\n    out[oi++] = (y09 + x09) | 0;\n    out[oi++] = (y10 + x10) | 0;\n    out[oi++] = (y11 + x11) | 0;\n    out[oi++] = (y12 + x12) | 0;\n    out[oi++] = (y13 + x13) | 0;\n    out[oi++] = (y14 + x14) | 0;\n    out[oi++] = (y15 + x15) | 0;\n}\n/**\n * hchacha helper method, used primarily in xchacha, to hash\n * key and nonce into key' and nonce'.\n * Same as chachaCore, but there doesn't seem to be a way to move the block\n * out without 25% performance hit.\n */\n// prettier-ignore\nfunction hchacha(s, k, i, o32) {\n    let x00 = s[0], x01 = s[1], x02 = s[2], x03 = s[3], x04 = k[0], x05 = k[1], x06 = k[2], x07 = k[3], x08 = k[4], x09 = k[5], x10 = k[6], x11 = k[7], x12 = i[0], x13 = i[1], x14 = i[2], x15 = i[3];\n    for (let r = 0; r < 20; r += 2) {\n        x00 = (x00 + x04) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x00, 16);\n        x08 = (x08 + x12) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x08, 12);\n        x00 = (x00 + x04) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x00, 8);\n        x08 = (x08 + x12) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x08, 7);\n        x01 = (x01 + x05) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x01, 16);\n        x09 = (x09 + x13) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x09, 12);\n        x01 = (x01 + x05) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x01, 8);\n        x09 = (x09 + x13) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x09, 7);\n        x02 = (x02 + x06) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x02, 16);\n        x10 = (x10 + x14) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x10, 12);\n        x02 = (x02 + x06) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x02, 8);\n        x10 = (x10 + x14) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x10, 7);\n        x03 = (x03 + x07) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x03, 16);\n        x11 = (x11 + x15) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x11, 12);\n        x03 = (x03 + x07) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x03, 8);\n        x11 = (x11 + x15) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x11, 7);\n        x00 = (x00 + x05) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x00, 16);\n        x10 = (x10 + x15) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x10, 12);\n        x00 = (x00 + x05) | 0;\n        x15 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 ^ x00, 8);\n        x10 = (x10 + x15) | 0;\n        x05 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 ^ x10, 7);\n        x01 = (x01 + x06) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x01, 16);\n        x11 = (x11 + x12) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x11, 12);\n        x01 = (x01 + x06) | 0;\n        x12 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 ^ x01, 8);\n        x11 = (x11 + x12) | 0;\n        x06 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 ^ x11, 7);\n        x02 = (x02 + x07) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x02, 16);\n        x08 = (x08 + x13) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x08, 12);\n        x02 = (x02 + x07) | 0;\n        x13 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 ^ x02, 8);\n        x08 = (x08 + x13) | 0;\n        x07 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 ^ x08, 7);\n        x03 = (x03 + x04) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x03, 16);\n        x09 = (x09 + x14) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x09, 12);\n        x03 = (x03 + x04) | 0;\n        x14 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 ^ x03, 8);\n        x09 = (x09 + x14) | 0;\n        x04 = (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 ^ x09, 7);\n    }\n    let oi = 0;\n    o32[oi++] = x00;\n    o32[oi++] = x01;\n    o32[oi++] = x02;\n    o32[oi++] = x03;\n    o32[oi++] = x12;\n    o32[oi++] = x13;\n    o32[oi++] = x14;\n    o32[oi++] = x15;\n}\n/**\n * Original, non-RFC chacha20 from DJB. 8-byte nonce, 8-byte counter.\n */\nconst chacha20orig = /* @__PURE__ */ (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.createCipher)(chachaCore, {\n    counterRight: false,\n    counterLength: 8,\n    allowShortKeys: true,\n});\n/**\n * ChaCha stream cipher. Conforms to RFC 8439 (IETF, TLS). 12-byte nonce, 4-byte counter.\n * With 12-byte nonce, it's not safe to use fill it with random (CSPRNG), due to collision chance.\n */\nconst chacha20 = /* @__PURE__ */ (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.createCipher)(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    allowShortKeys: false,\n});\n/**\n * XChaCha eXtended-nonce ChaCha. 24-byte nonce.\n * With 24-byte nonce, it's safe to use fill it with random (CSPRNG).\n * https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha\n */\nconst xchacha20 = /* @__PURE__ */ (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.createCipher)(chachaCore, {\n    counterRight: false,\n    counterLength: 8,\n    extendNonceFn: hchacha,\n    allowShortKeys: false,\n});\n/**\n * Reduced 8-round chacha, described in original paper.\n */\nconst chacha8 = /* @__PURE__ */ (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.createCipher)(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    rounds: 8,\n});\n/**\n * Reduced 12-round chacha, described in original paper.\n */\nconst chacha12 = /* @__PURE__ */ (0,_arx_js__WEBPACK_IMPORTED_MODULE_0__.createCipher)(chachaCore, {\n    counterRight: false,\n    counterLength: 4,\n    rounds: 12,\n});\nconst ZEROS16 = /* @__PURE__ */ new Uint8Array(16);\n// Pad to digest size with zeros\nconst updatePadded = (h, msg) => {\n    h.update(msg);\n    const left = msg.length % 16;\n    if (left)\n        h.update(ZEROS16.subarray(left));\n};\nconst ZEROS32 = /* @__PURE__ */ new Uint8Array(32);\nfunction computeTag(fn, key, nonce, data, AAD) {\n    const authKey = fn(key, nonce, ZEROS32);\n    const h = _poly1305_js__WEBPACK_IMPORTED_MODULE_1__.poly1305.create(authKey);\n    if (AAD)\n        updatePadded(h, AAD);\n    updatePadded(h, data);\n    const num = new Uint8Array(16);\n    const view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.createView)(num);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.setBigUint64)(view, 0, BigInt(AAD ? AAD.length : 0), true);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.setBigUint64)(view, 8, BigInt(data.length), true);\n    h.update(num);\n    const res = h.digest();\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.clean)(authKey, num);\n    return res;\n}\n/**\n * AEAD algorithm from RFC 8439.\n * Salsa20 and chacha (RFC 8439) use poly1305 differently.\n * We could have composed them similar to:\n * https://github.com/paulmillr/scure-base/blob/b266c73dde977b1dd7ef40ef7a23cc15aab526b3/index.ts#L250\n * But it's hard because of authKey:\n * In salsa20, authKey changes position in salsa stream.\n * In chacha, authKey can't be computed inside computeTag, it modifies the counter.\n */\nconst _poly1305_aead = (xorStream) => (key, nonce, AAD) => {\n    const tagLength = 16;\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_3__.bytes)(key, 32);\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_3__.bytes)(nonce);\n    return {\n        encrypt(plaintext, output) {\n            const plength = plaintext.length;\n            const clength = plength + tagLength;\n            if (output) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_3__.bytes)(output, clength);\n            }\n            else {\n                output = new Uint8Array(clength);\n            }\n            xorStream(key, nonce, plaintext, output, 1);\n            const tag = computeTag(xorStream, key, nonce, output.subarray(0, -tagLength), AAD);\n            output.set(tag, plength); // append tag\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.clean)(tag);\n            return output;\n        },\n        decrypt(ciphertext, output) {\n            const clength = ciphertext.length;\n            const plength = clength - tagLength;\n            if (clength < tagLength)\n                throw new Error(`encrypted data must be at least ${tagLength} bytes`);\n            if (output) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_3__.bytes)(output, plength);\n            }\n            else {\n                output = new Uint8Array(plength);\n            }\n            const data = ciphertext.subarray(0, -tagLength);\n            const passedTag = ciphertext.subarray(-tagLength);\n            const tag = computeTag(xorStream, key, nonce, data, AAD);\n            if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.equalBytes)(passedTag, tag))\n                throw new Error('invalid tag');\n            xorStream(key, nonce, data, output, 1);\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.clean)(tag);\n            return output;\n        },\n    };\n};\n/**\n * ChaCha20-Poly1305 from RFC 8439.\n * With 12-byte nonce, it's not safe to use fill it with random (CSPRNG), due to collision chance.\n */\nconst chacha20poly1305 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.wrapCipher)({ blockSize: 64, nonceLength: 12, tagLength: 16 }, _poly1305_aead(chacha20));\n/**\n * XChaCha20-Poly1305 extended-nonce chacha.\n * https://datatracker.ietf.org/doc/html/draft-irtf-cfrg-xchacha\n * With 24-byte nonce, it's safe to use fill it with random (CSPRNG).\n */\nconst xchacha20poly1305 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.wrapCipher)({ blockSize: 64, nonceLength: 24, tagLength: 16 }, _poly1305_aead(xchacha20));\n//# sourceMappingURL=chacha.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/chacha.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/cryptoNode.js":
/*!***********************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/cryptoNode.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// See utils.ts for details.\n// The file will throw on node.js 14 and earlier.\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0Bub2JsZS9jaXBoZXJzL2VzbS9jcnlwdG9Ob2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ2tDO0FBQzNCLGVBQWUsMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsME5BQWlCLEdBQUcsa0RBQVk7QUFDdEYiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9Abm9ibGUvY2lwaGVycy9lc20vY3J5cHRvTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBXZSB1c2UgV2ViQ3J5cHRvIGFrYSBnbG9iYWxUaGlzLmNyeXB0bywgd2hpY2ggZXhpc3RzIGluIGJyb3dzZXJzIGFuZCBub2RlLmpzIDE2Ky5cbi8vIFNlZSB1dGlscy50cyBmb3IgZGV0YWlscy5cbi8vIFRoZSBmaWxlIHdpbGwgdGhyb3cgb24gbm9kZS5qcyAxNCBhbmQgZWFybGllci5cbi8vIEB0cy1pZ25vcmVcbmltcG9ydCAqIGFzIG5jIGZyb20gJ25vZGU6Y3J5cHRvJztcbmV4cG9ydCBjb25zdCBjcnlwdG8gPSBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICd3ZWJjcnlwdG8nIGluIG5jID8gbmMud2ViY3J5cHRvIDogdW5kZWZpbmVkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3J5cHRvTm9kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/cryptoNode.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/utils.js":
/*!******************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/utils.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToNumberBE: () => (/* binding */ bytesToNumberBE),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   copyBytes: () => (/* binding */ copyBytes),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   equalBytes: () => (/* binding */ equalBytes),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   hexToNumber: () => (/* binding */ hexToNumber),\n/* harmony export */   isAligned32: () => (/* binding */ isAligned32),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   numberToBytesBE: () => (/* binding */ numberToBytesBE),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u16: () => (/* binding */ u16),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u64Lengths: () => (/* binding */ u64Lengths),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapCipher: () => (/* binding */ wrapCipher)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_assert.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\");\n/*! noble-ciphers - MIT License (c) 2023 Paul Miller (paulmillr.com) */\n\n// Cast array to different type\nconst u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nconst u16 = (arr) => new Uint16Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 2));\nconst u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nconst createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// big-endian hardware is rare. Just in case someone still decides to run ciphers:\n// early-throw an error because we don't support BE yet.\nconst isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.bytes)(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 };\nfunction asciiToBase16(char) {\n    if (char >= asciis._0 && char <= asciis._9)\n        return char - asciis._0;\n    if (char >= asciis._A && char <= asciis._F)\n        return char - (asciis._A - 10);\n    if (char >= asciis._a && char <= asciis._f)\n        return char - (asciis._a - 10);\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2;\n    }\n    return array;\n}\nfunction hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // Big Endian\n    return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n// BE: Big Endian, LE: Little Endian\nfunction bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nfunction numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nconst nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`string expected, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * @example bytesToUtf8(new Uint8Array([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    else if ((0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(data))\n        data = copyBytes(data);\n    else\n        throw new Error(`Uint8Array expected, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.bytes)(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts == null || typeof opts !== 'object')\n        throw new Error('options must be defined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n// Compares 2 u8a-s in kinda constant time\nfunction equalBytes(a, b) {\n    if (a.length !== b.length)\n        return false;\n    let diff = 0;\n    for (let i = 0; i < a.length; i++)\n        diff |= a[i] ^ b[i];\n    return diff === 0;\n}\n// For runtime check if class implements interface\nclass Hash {\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nconst wrapCipher = (params, c) => {\n    Object.assign(c, params);\n    return c;\n};\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\nfunction u64Lengths(ciphertext, AAD) {\n    const num = new Uint8Array(16);\n    const view = createView(num);\n    setBigUint64(view, 0, BigInt(AAD ? AAD.length : 0), true);\n    setBigUint64(view, 8, BigInt(ciphertext.length), true);\n    return num;\n}\n// Is byte array aligned to 4 byte offset (u32)?\nfunction isAligned32(bytes) {\n    return bytes.byteOffset % 4 === 0;\n}\n// copy bytes to new u8a (aligned). Because Buffer.slice is broken.\nfunction copyBytes(bytes) {\n    return Uint8Array.from(bytes);\n}\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/ciphers/esm/webcrypto.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@noble/ciphers/esm/webcrypto.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cbc: () => (/* binding */ cbc),\n/* harmony export */   ctr: () => (/* binding */ ctr),\n/* harmony export */   gcm: () => (/* binding */ gcm),\n/* harmony export */   getWebcryptoSubtle: () => (/* binding */ getWebcryptoSubtle),\n/* harmony export */   managedNonce: () => (/* binding */ managedNonce),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/* harmony import */ var _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/ciphers/crypto */ \"(rsc)/../../node_modules/@noble/ciphers/esm/cryptoNode.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_assert.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/_assert.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/ciphers/esm/utils.js\");\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.js on#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\n//\n// Use full path so that Node.js can rewrite it to `cryptoNode.js`.\n\n\n\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function')\n        return _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    throw new Error('crypto.getRandomValues must be defined');\n}\nfunction getWebcryptoSubtle() {\n    if (_noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.subtle === 'object' && _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.subtle != null)\n        return _noble_ciphers_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.subtle;\n    throw new Error('crypto.subtle must be defined');\n}\n// Uses CSPRG for nonce, nonce injected in ciphertext\nfunction managedNonce(fn) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.number)(fn.nonceLength);\n    return ((key, ...args) => ({\n        encrypt(plaintext, ...argsEnc) {\n            const { nonceLength } = fn;\n            const nonce = randomBytes(nonceLength);\n            const ciphertext = fn(key, nonce, ...args).encrypt(plaintext, ...argsEnc);\n            const out = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.concatBytes)(nonce, ciphertext);\n            ciphertext.fill(0);\n            return out;\n        },\n        decrypt(ciphertext, ...argsDec) {\n            const { nonceLength } = fn;\n            const nonce = ciphertext.subarray(0, nonceLength);\n            const data = ciphertext.subarray(nonceLength);\n            return fn(key, nonce, ...args).decrypt(data, ...argsDec);\n        },\n    }));\n}\n// Overridable\nconst utils = {\n    async encrypt(key, keyParams, cryptParams, plaintext) {\n        const cr = getWebcryptoSubtle();\n        const iKey = await cr.importKey('raw', key, keyParams, true, ['encrypt']);\n        const ciphertext = await cr.encrypt(cryptParams, iKey, plaintext);\n        return new Uint8Array(ciphertext);\n    },\n    async decrypt(key, keyParams, cryptParams, ciphertext) {\n        const cr = getWebcryptoSubtle();\n        const iKey = await cr.importKey('raw', key, keyParams, true, ['decrypt']);\n        const plaintext = await cr.decrypt(cryptParams, iKey, ciphertext);\n        return new Uint8Array(plaintext);\n    },\n};\nconst mode = {\n    CBC: 'AES-CBC',\n    CTR: 'AES-CTR',\n    GCM: 'AES-GCM',\n};\nfunction getCryptParams(algo, nonce, AAD) {\n    if (algo === mode.CBC)\n        return { name: mode.CBC, iv: nonce };\n    if (algo === mode.CTR)\n        return { name: mode.CTR, counter: nonce, length: 64 };\n    if (algo === mode.GCM) {\n        if (AAD)\n            return { name: mode.GCM, iv: nonce, additionalData: AAD };\n        else\n            return { name: mode.GCM, iv: nonce };\n    }\n    throw new Error('unknown aes block mode');\n}\nfunction generate(algo) {\n    return (key, nonce, AAD) => {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(key);\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(nonce);\n        const keyParams = { name: algo, length: key.length * 8 };\n        const cryptParams = getCryptParams(algo, nonce, AAD);\n        return {\n            // keyLength,\n            encrypt(plaintext) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(plaintext);\n                return utils.encrypt(key, keyParams, cryptParams, plaintext);\n            },\n            decrypt(ciphertext) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.bytes)(ciphertext);\n                return utils.decrypt(key, keyParams, cryptParams, ciphertext);\n            },\n        };\n    };\n}\nconst cbc = generate(mode.CBC);\nconst ctr = generate(mode.CTR);\nconst gcm = generate(mode.GCM);\n// // Type tests\n// import { siv, gcm, ctr, ecb, cbc } from '../aes.js';\n// import { xsalsa20poly1305 } from '../salsa.js';\n// import { chacha20poly1305, xchacha20poly1305 } from '../chacha.js';\n// const wsiv = managedNonce(siv);\n// const wgcm = managedNonce(gcm);\n// const wctr = managedNonce(ctr);\n// const wcbc = managedNonce(cbc);\n// const wsalsapoly = managedNonce(xsalsa20poly1305);\n// const wchacha = managedNonce(chacha20poly1305);\n// const wxchacha = managedNonce(xchacha20poly1305);\n// // should fail\n// const wcbc2 = managedNonce(managedNonce(cbc));\n// const wctr = managedNonce(ctr);\n//# sourceMappingURL=webcrypto.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/ciphers/esm/webcrypto.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/_md.js":
/*!***************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/_md.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chi: () => (/* binding */ Chi),\n/* harmony export */   HashMD: () => (/* binding */ HashMD),\n/* harmony export */   Maj: () => (/* binding */ Maj),\n/* harmony export */   SHA224_IV: () => (/* binding */ SHA224_IV),\n/* harmony export */   SHA256_IV: () => (/* binding */ SHA256_IV),\n/* harmony export */   SHA384_IV: () => (/* binding */ SHA384_IV),\n/* harmony export */   SHA512_IV: () => (/* binding */ SHA512_IV),\n/* harmony export */   setBigUint64: () => (/* binding */ setBigUint64)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/utils.js\");\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(this.buffer);\n    }\n    update(data) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(data);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nconst SHA256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nconst SHA224_IV = /* @__PURE__ */ Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nconst SHA384_IV = /* @__PURE__ */ Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nconst SHA512_IV = /* @__PURE__ */ Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/_md.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/_u64.js":
/*!****************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/_u64.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   add3H: () => (/* binding */ add3H),\n/* harmony export */   add3L: () => (/* binding */ add3L),\n/* harmony export */   add4H: () => (/* binding */ add4H),\n/* harmony export */   add4L: () => (/* binding */ add4L),\n/* harmony export */   add5H: () => (/* binding */ add5H),\n/* harmony export */   add5L: () => (/* binding */ add5L),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fromBig: () => (/* binding */ fromBig),\n/* harmony export */   rotlBH: () => (/* binding */ rotlBH),\n/* harmony export */   rotlBL: () => (/* binding */ rotlBL),\n/* harmony export */   rotlSH: () => (/* binding */ rotlSH),\n/* harmony export */   rotlSL: () => (/* binding */ rotlSL),\n/* harmony export */   rotr32H: () => (/* binding */ rotr32H),\n/* harmony export */   rotr32L: () => (/* binding */ rotr32L),\n/* harmony export */   rotrBH: () => (/* binding */ rotrBH),\n/* harmony export */   rotrBL: () => (/* binding */ rotrBL),\n/* harmony export */   rotrSH: () => (/* binding */ rotrSH),\n/* harmony export */   rotrSL: () => (/* binding */ rotrSL),\n/* harmony export */   shrSH: () => (/* binding */ shrSH),\n/* harmony export */   shrSL: () => (/* binding */ shrSL),\n/* harmony export */   split: () => (/* binding */ split),\n/* harmony export */   toBig: () => (/* binding */ toBig)\n/* harmony export */ });\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\n\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0Bub2JsZS9oYXNoZXMvZXNtL191NjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsU0FBUztBQUM3QixnQkFBZ0IsT0FBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dMO0FBQ2hMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLEdBQUcsRUFBQztBQUNuQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL0Bub2JsZS9oYXNoZXMvZXNtL191NjQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBJbnRlcm5hbCBoZWxwZXJzIGZvciB1NjQuIEJpZ1VpbnQ2NEFycmF5IGlzIHRvbyBzbG93IGFzIHBlciAyMDI1LCBzbyB3ZSBpbXBsZW1lbnQgaXQgdXNpbmcgVWludDMyQXJyYXkuXG4gKiBAdG9kbyByZS1jaGVjayBodHRwczovL2lzc3Vlcy5jaHJvbWl1bS5vcmcvaXNzdWVzLzQyMjEyNTg4XG4gKiBAbW9kdWxlXG4gKi9cbmNvbnN0IFUzMl9NQVNLNjQgPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDIgKiogMzIgLSAxKTtcbmNvbnN0IF8zMm4gPSAvKiBAX19QVVJFX18gKi8gQmlnSW50KDMyKTtcbmZ1bmN0aW9uIGZyb21CaWcobiwgbGUgPSBmYWxzZSkge1xuICAgIGlmIChsZSlcbiAgICAgICAgcmV0dXJuIHsgaDogTnVtYmVyKG4gJiBVMzJfTUFTSzY0KSwgbDogTnVtYmVyKChuID4+IF8zMm4pICYgVTMyX01BU0s2NCkgfTtcbiAgICByZXR1cm4geyBoOiBOdW1iZXIoKG4gPj4gXzMybikgJiBVMzJfTUFTSzY0KSB8IDAsIGw6IE51bWJlcihuICYgVTMyX01BU0s2NCkgfCAwIH07XG59XG5mdW5jdGlvbiBzcGxpdChsc3QsIGxlID0gZmFsc2UpIHtcbiAgICBjb25zdCBsZW4gPSBsc3QubGVuZ3RoO1xuICAgIGxldCBBaCA9IG5ldyBVaW50MzJBcnJheShsZW4pO1xuICAgIGxldCBBbCA9IG5ldyBVaW50MzJBcnJheShsZW4pO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuOyBpKyspIHtcbiAgICAgICAgY29uc3QgeyBoLCBsIH0gPSBmcm9tQmlnKGxzdFtpXSwgbGUpO1xuICAgICAgICBbQWhbaV0sIEFsW2ldXSA9IFtoLCBsXTtcbiAgICB9XG4gICAgcmV0dXJuIFtBaCwgQWxdO1xufVxuY29uc3QgdG9CaWcgPSAoaCwgbCkgPT4gKEJpZ0ludChoID4+PiAwKSA8PCBfMzJuKSB8IEJpZ0ludChsID4+PiAwKTtcbi8vIGZvciBTaGlmdCBpbiBbMCwgMzIpXG5jb25zdCBzaHJTSCA9IChoLCBfbCwgcykgPT4gaCA+Pj4gcztcbmNvbnN0IHNoclNMID0gKGgsIGwsIHMpID0+IChoIDw8ICgzMiAtIHMpKSB8IChsID4+PiBzKTtcbi8vIFJpZ2h0IHJvdGF0ZSBmb3IgU2hpZnQgaW4gWzEsIDMyKVxuY29uc3Qgcm90clNIID0gKGgsIGwsIHMpID0+IChoID4+PiBzKSB8IChsIDw8ICgzMiAtIHMpKTtcbmNvbnN0IHJvdHJTTCA9IChoLCBsLCBzKSA9PiAoaCA8PCAoMzIgLSBzKSkgfCAobCA+Pj4gcyk7XG4vLyBSaWdodCByb3RhdGUgZm9yIFNoaWZ0IGluICgzMiwgNjQpLCBOT1RFOiAzMiBpcyBzcGVjaWFsIGNhc2UuXG5jb25zdCByb3RyQkggPSAoaCwgbCwgcykgPT4gKGggPDwgKDY0IC0gcykpIHwgKGwgPj4+IChzIC0gMzIpKTtcbmNvbnN0IHJvdHJCTCA9IChoLCBsLCBzKSA9PiAoaCA+Pj4gKHMgLSAzMikpIHwgKGwgPDwgKDY0IC0gcykpO1xuLy8gUmlnaHQgcm90YXRlIGZvciBzaGlmdD09PTMyIChqdXN0IHN3YXBzIGwmaClcbmNvbnN0IHJvdHIzMkggPSAoX2gsIGwpID0+IGw7XG5jb25zdCByb3RyMzJMID0gKGgsIF9sKSA9PiBoO1xuLy8gTGVmdCByb3RhdGUgZm9yIFNoaWZ0IGluIFsxLCAzMilcbmNvbnN0IHJvdGxTSCA9IChoLCBsLCBzKSA9PiAoaCA8PCBzKSB8IChsID4+PiAoMzIgLSBzKSk7XG5jb25zdCByb3RsU0wgPSAoaCwgbCwgcykgPT4gKGwgPDwgcykgfCAoaCA+Pj4gKDMyIC0gcykpO1xuLy8gTGVmdCByb3RhdGUgZm9yIFNoaWZ0IGluICgzMiwgNjQpLCBOT1RFOiAzMiBpcyBzcGVjaWFsIGNhc2UuXG5jb25zdCByb3RsQkggPSAoaCwgbCwgcykgPT4gKGwgPDwgKHMgLSAzMikpIHwgKGggPj4+ICg2NCAtIHMpKTtcbmNvbnN0IHJvdGxCTCA9IChoLCBsLCBzKSA9PiAoaCA8PCAocyAtIDMyKSkgfCAobCA+Pj4gKDY0IC0gcykpO1xuLy8gSlMgdXNlcyAzMi1iaXQgc2lnbmVkIGludGVnZXJzIGZvciBiaXR3aXNlIG9wZXJhdGlvbnMgd2hpY2ggbWVhbnMgd2UgY2Fubm90XG4vLyBzaW1wbGUgdGFrZSBjYXJyeSBvdXQgb2YgbG93IGJpdCBzdW0gYnkgc2hpZnQsIHdlIG5lZWQgdG8gdXNlIGRpdmlzaW9uLlxuZnVuY3Rpb24gYWRkKEFoLCBBbCwgQmgsIEJsKSB7XG4gICAgY29uc3QgbCA9IChBbCA+Pj4gMCkgKyAoQmwgPj4+IDApO1xuICAgIHJldHVybiB7IGg6IChBaCArIEJoICsgKChsIC8gMiAqKiAzMikgfCAwKSkgfCAwLCBsOiBsIHwgMCB9O1xufVxuLy8gQWRkaXRpb24gd2l0aCBtb3JlIHRoYW4gMiBlbGVtZW50c1xuY29uc3QgYWRkM0wgPSAoQWwsIEJsLCBDbCkgPT4gKEFsID4+PiAwKSArIChCbCA+Pj4gMCkgKyAoQ2wgPj4+IDApO1xuY29uc3QgYWRkM0ggPSAobG93LCBBaCwgQmgsIENoKSA9PiAoQWggKyBCaCArIENoICsgKChsb3cgLyAyICoqIDMyKSB8IDApKSB8IDA7XG5jb25zdCBhZGQ0TCA9IChBbCwgQmwsIENsLCBEbCkgPT4gKEFsID4+PiAwKSArIChCbCA+Pj4gMCkgKyAoQ2wgPj4+IDApICsgKERsID4+PiAwKTtcbmNvbnN0IGFkZDRIID0gKGxvdywgQWgsIEJoLCBDaCwgRGgpID0+IChBaCArIEJoICsgQ2ggKyBEaCArICgobG93IC8gMiAqKiAzMikgfCAwKSkgfCAwO1xuY29uc3QgYWRkNUwgPSAoQWwsIEJsLCBDbCwgRGwsIEVsKSA9PiAoQWwgPj4+IDApICsgKEJsID4+PiAwKSArIChDbCA+Pj4gMCkgKyAoRGwgPj4+IDApICsgKEVsID4+PiAwKTtcbmNvbnN0IGFkZDVIID0gKGxvdywgQWgsIEJoLCBDaCwgRGgsIEVoKSA9PiAoQWggKyBCaCArIENoICsgRGggKyBFaCArICgobG93IC8gMiAqKiAzMikgfCAwKSkgfCAwO1xuLy8gcHJldHRpZXItaWdub3JlXG5leHBvcnQgeyBhZGQsIGFkZDNILCBhZGQzTCwgYWRkNEgsIGFkZDRMLCBhZGQ1SCwgYWRkNUwsIGZyb21CaWcsIHJvdGxCSCwgcm90bEJMLCByb3RsU0gsIHJvdGxTTCwgcm90cjMySCwgcm90cjMyTCwgcm90ckJILCByb3RyQkwsIHJvdHJTSCwgcm90clNMLCBzaHJTSCwgc2hyU0wsIHNwbGl0LCB0b0JpZyB9O1xuLy8gcHJldHRpZXItaWdub3JlXG5jb25zdCB1NjQgPSB7XG4gICAgZnJvbUJpZywgc3BsaXQsIHRvQmlnLFxuICAgIHNoclNILCBzaHJTTCxcbiAgICByb3RyU0gsIHJvdHJTTCwgcm90ckJILCByb3RyQkwsXG4gICAgcm90cjMySCwgcm90cjMyTCxcbiAgICByb3RsU0gsIHJvdGxTTCwgcm90bEJILCByb3RsQkwsXG4gICAgYWRkLCBhZGQzTCwgYWRkM0gsIGFkZDRMLCBhZGQ0SCwgYWRkNUgsIGFkZDVMLFxufTtcbmV4cG9ydCBkZWZhdWx0IHU2NDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPV91NjQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/_u64.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/cryptoNode.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/cryptoNode.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crypto: () => (/* binding */ crypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\n\nconst crypto = /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"webcrypto\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n    ? node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto\n    : /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) && typeof /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2))) === 'object' && \"randomBytes\" in /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        ? /*#__PURE__*/ (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (node_crypto__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(node_crypto__WEBPACK_IMPORTED_MODULE_0__, 2)))\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0Bub2JsZS9oYXNoZXMvZXNtL2NyeXB0b05vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNrQztBQUMzQixlQUFlLDJNQUFFLFdBQVcsMk1BQUUsaUJBQWlCLDBOQUFpQjtBQUN2RSxNQUFNLGtEQUFZO0FBQ2xCLE1BQU0sMk1BQUUsV0FBVywyTUFBRSxpQkFBaUIsNE5BQW1CO0FBQ3pELFVBQVUsMk1BQUU7QUFDWjtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9lc20vY3J5cHRvTm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEludGVybmFsIHdlYmNyeXB0byBhbGlhcy5cbiAqIFdlIHByZWZlciBXZWJDcnlwdG8gYWthIGdsb2JhbFRoaXMuY3J5cHRvLCB3aGljaCBleGlzdHMgaW4gbm9kZS5qcyAxNisuXG4gKiBGYWxscyBiYWNrIHRvIE5vZGUuanMgYnVpbHQtaW4gY3J5cHRvIGZvciBOb2RlLmpzIDw9djE0LlxuICogU2VlIHV0aWxzLnRzIGZvciBkZXRhaWxzLlxuICogQG1vZHVsZVxuICovXG4vLyBAdHMtaWdub3JlXG5pbXBvcnQgKiBhcyBuYyBmcm9tICdub2RlOmNyeXB0byc7XG5leHBvcnQgY29uc3QgY3J5cHRvID0gbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAnd2ViY3J5cHRvJyBpbiBuY1xuICAgID8gbmMud2ViY3J5cHRvXG4gICAgOiBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICdyYW5kb21CeXRlcycgaW4gbmNcbiAgICAgICAgPyBuY1xuICAgICAgICA6IHVuZGVmaW5lZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyeXB0b05vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/cryptoNode.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/hmac.js":
/*!****************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/hmac.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(pad);\n    }\n    update(buf) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/hmac.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/pbkdf2.js":
/*!******************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/pbkdf2.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pbkdf2: () => (/* binding */ pbkdf2),\n/* harmony export */   pbkdf2Async: () => (/* binding */ pbkdf2Async)\n/* harmony export */ });\n/* harmony import */ var _hmac_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hmac.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/utils.js\");\n/**\n * PBKDF (RFC 2898). Can be used to create a key from password and salt.\n * @module\n */\n\n// prettier-ignore\n\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash, _password, _salt, _opts) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n    const opts = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.checkOpts)({ dkLen: 32, asyncTick: 10 }, _opts);\n    const { c, dkLen, asyncTick } = opts;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(c);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(dkLen);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(asyncTick);\n    if (c < 1)\n        throw new Error('iterations (c) should be >= 1');\n    const password = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.kdfInputToBytes)(_password);\n    const salt = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.kdfInputToBytes)(_salt);\n    // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n    const DK = new Uint8Array(dkLen);\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    const PRF = _hmac_js__WEBPACK_IMPORTED_MODULE_1__.hmac.create(hash, password);\n    const PRFSalt = PRF._cloneInto().update(salt);\n    return { c, dkLen, asyncTick, DK, PRF, PRFSalt };\n}\nfunction pbkdf2Output(PRF, PRFSalt, DK, prfW, u) {\n    PRF.destroy();\n    PRFSalt.destroy();\n    if (prfW)\n        prfW.destroy();\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(u);\n    return DK;\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n * @example\n * const key = pbkdf2(sha256, 'password', 'salt', { dkLen: 32, c: Math.pow(2, 18) });\n */\nfunction pbkdf2(hash, password, salt, opts) {\n    const { c, dkLen, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n    let prfW; // Working copy\n    const arr = new Uint8Array(4);\n    const view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(arr);\n    const u = new Uint8Array(PRF.outputLen);\n    // DK = T1 + T2 + ⋯ + Tdklen/hlen\n    for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n        // Ti = F(Password, Salt, c, i)\n        const Ti = DK.subarray(pos, pos + PRF.outputLen);\n        view.setInt32(0, ti, false);\n        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n        // U1 = PRF(Password, Salt + INT_32_BE(i))\n        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n        Ti.set(u.subarray(0, Ti.length));\n        for (let ui = 1; ui < c; ui++) {\n            // Uc = PRF(Password, Uc−1)\n            PRF._cloneInto(prfW).update(u).digestInto(u);\n            for (let i = 0; i < Ti.length; i++)\n                Ti[i] ^= u[i];\n        }\n    }\n    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function. Async version.\n * @example\n * await pbkdf2Async(sha256, 'password', 'salt', { dkLen: 32, c: 500_000 });\n */\nasync function pbkdf2Async(hash, password, salt, opts) {\n    const { c, dkLen, asyncTick, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n    let prfW; // Working copy\n    const arr = new Uint8Array(4);\n    const view = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createView)(arr);\n    const u = new Uint8Array(PRF.outputLen);\n    // DK = T1 + T2 + ⋯ + Tdklen/hlen\n    for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n        // Ti = F(Password, Salt, c, i)\n        const Ti = DK.subarray(pos, pos + PRF.outputLen);\n        view.setInt32(0, ti, false);\n        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n        // U1 = PRF(Password, Salt + INT_32_BE(i))\n        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n        Ti.set(u.subarray(0, Ti.length));\n        await (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.asyncLoop)(c - 1, asyncTick, () => {\n            // Uc = PRF(Password, Uc−1)\n            PRF._cloneInto(prfW).update(u).digestInto(u);\n            for (let i = 0; i < Ti.length; i++)\n                Ti[i] ^= u[i];\n        });\n    }\n    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n//# sourceMappingURL=pbkdf2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/pbkdf2.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/scrypt.js":
/*!******************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/scrypt.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrypt: () => (/* binding */ scrypt),\n/* harmony export */   scryptAsync: () => (/* binding */ scryptAsync)\n/* harmony export */ });\n/* harmony import */ var _pbkdf2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pbkdf2.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/pbkdf2.js\");\n/* harmony import */ var _sha2_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sha2.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/sha2.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/utils.js\");\n/**\n * RFC 7914 Scrypt KDF. Can be used to create a key from password and salt.\n * @module\n */\n\n\n// prettier-ignore\n\n// The main Scrypt loop: uses Salsa extensively.\n// Six versions of the function were tried, this is the fastest one.\n// prettier-ignore\nfunction XorAndSalsa(prev, pi, input, ii, out, oi) {\n    // Based on https://cr.yp.to/salsa20.html\n    // Xor blocks\n    let y00 = prev[pi++] ^ input[ii++], y01 = prev[pi++] ^ input[ii++];\n    let y02 = prev[pi++] ^ input[ii++], y03 = prev[pi++] ^ input[ii++];\n    let y04 = prev[pi++] ^ input[ii++], y05 = prev[pi++] ^ input[ii++];\n    let y06 = prev[pi++] ^ input[ii++], y07 = prev[pi++] ^ input[ii++];\n    let y08 = prev[pi++] ^ input[ii++], y09 = prev[pi++] ^ input[ii++];\n    let y10 = prev[pi++] ^ input[ii++], y11 = prev[pi++] ^ input[ii++];\n    let y12 = prev[pi++] ^ input[ii++], y13 = prev[pi++] ^ input[ii++];\n    let y14 = prev[pi++] ^ input[ii++], y15 = prev[pi++] ^ input[ii++];\n    // Save state to temporary variables (salsa)\n    let x00 = y00, x01 = y01, x02 = y02, x03 = y03, x04 = y04, x05 = y05, x06 = y06, x07 = y07, x08 = y08, x09 = y09, x10 = y10, x11 = y11, x12 = y12, x13 = y13, x14 = y14, x15 = y15;\n    // Main loop (salsa)\n    for (let i = 0; i < 8; i += 2) {\n        x04 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x00 + x12 | 0, 7);\n        x08 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 + x00 | 0, 9);\n        x12 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x08 + x04 | 0, 13);\n        x00 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 + x08 | 0, 18);\n        x09 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 + x01 | 0, 7);\n        x13 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x09 + x05 | 0, 9);\n        x01 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 + x09 | 0, 13);\n        x05 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x01 + x13 | 0, 18);\n        x14 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x10 + x06 | 0, 7);\n        x02 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 + x10 | 0, 9);\n        x06 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x02 + x14 | 0, 13);\n        x10 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 + x02 | 0, 18);\n        x03 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 + x11 | 0, 7);\n        x07 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x03 + x15 | 0, 9);\n        x11 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 + x03 | 0, 13);\n        x15 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x11 + x07 | 0, 18);\n        x01 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x00 + x03 | 0, 7);\n        x02 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x01 + x00 | 0, 9);\n        x03 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x02 + x01 | 0, 13);\n        x00 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x03 + x02 | 0, 18);\n        x06 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x05 + x04 | 0, 7);\n        x07 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x06 + x05 | 0, 9);\n        x04 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x07 + x06 | 0, 13);\n        x05 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x04 + x07 | 0, 18);\n        x11 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x10 + x09 | 0, 7);\n        x08 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x11 + x10 | 0, 9);\n        x09 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x08 + x11 | 0, 13);\n        x10 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x09 + x08 | 0, 18);\n        x12 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x15 + x14 | 0, 7);\n        x13 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x12 + x15 | 0, 9);\n        x14 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x13 + x12 | 0, 13);\n        x15 ^= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.rotl)(x14 + x13 | 0, 18);\n    }\n    // Write output (salsa)\n    out[oi++] = (y00 + x00) | 0;\n    out[oi++] = (y01 + x01) | 0;\n    out[oi++] = (y02 + x02) | 0;\n    out[oi++] = (y03 + x03) | 0;\n    out[oi++] = (y04 + x04) | 0;\n    out[oi++] = (y05 + x05) | 0;\n    out[oi++] = (y06 + x06) | 0;\n    out[oi++] = (y07 + x07) | 0;\n    out[oi++] = (y08 + x08) | 0;\n    out[oi++] = (y09 + x09) | 0;\n    out[oi++] = (y10 + x10) | 0;\n    out[oi++] = (y11 + x11) | 0;\n    out[oi++] = (y12 + x12) | 0;\n    out[oi++] = (y13 + x13) | 0;\n    out[oi++] = (y14 + x14) | 0;\n    out[oi++] = (y15 + x15) | 0;\n}\nfunction BlockMix(input, ii, out, oi, r) {\n    // The block B is r 128-byte chunks (which is equivalent of 2r 64-byte chunks)\n    let head = oi + 0;\n    let tail = oi + 16 * r;\n    for (let i = 0; i < 16; i++)\n        out[tail + i] = input[ii + (2 * r - 1) * 16 + i]; // X ← B[2r−1]\n    for (let i = 0; i < r; i++, head += 16, ii += 16) {\n        // We write odd & even Yi at same time. Even: 0bXXXXX0 Odd:  0bXXXXX1\n        XorAndSalsa(out, tail, input, ii, out, head); // head[i] = Salsa(blockIn[2*i] ^ tail[i-1])\n        if (i > 0)\n            tail += 16; // First iteration overwrites tmp value in tail\n        XorAndSalsa(out, head, input, (ii += 16), out, tail); // tail[i] = Salsa(blockIn[2*i+1] ^ head[i])\n    }\n}\n// Common prologue and epilogue for sync/async functions\nfunction scryptInit(password, salt, _opts) {\n    // Maxmem - 1GB+1KB by default\n    const opts = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.checkOpts)({\n        dkLen: 32,\n        asyncTick: 10,\n        maxmem: 1024 ** 3 + 1024,\n    }, _opts);\n    const { N, r, p, dkLen, asyncTick, maxmem, onProgress } = opts;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(N);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(r);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(p);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(dkLen);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(asyncTick);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.anumber)(maxmem);\n    if (onProgress !== undefined && typeof onProgress !== 'function')\n        throw new Error('progressCb should be function');\n    const blockSize = 128 * r;\n    const blockSize32 = blockSize / 4;\n    // Max N is 2^32 (Integrify is 32-bit). Real limit is 2^22: JS engines Uint8Array limit is 4GB in 2024.\n    // Spec check `N >= 2^(blockSize / 8)` is not done for compat with popular libs,\n    // which used incorrect r: 1, p: 8. Also, the check seems to be a spec error:\n    // https://www.rfc-editor.org/errata_search.php?rfc=7914\n    const pow32 = Math.pow(2, 32);\n    if (N <= 1 || (N & (N - 1)) !== 0 || N > pow32) {\n        throw new Error('Scrypt: N must be larger than 1, a power of 2, and less than 2^32');\n    }\n    if (p < 0 || p > ((pow32 - 1) * 32) / blockSize) {\n        throw new Error('Scrypt: p must be a positive integer less than or equal to ((2^32 - 1) * 32) / (128 * r)');\n    }\n    if (dkLen < 0 || dkLen > (pow32 - 1) * 32) {\n        throw new Error('Scrypt: dkLen should be positive integer less than or equal to (2^32 - 1) * 32');\n    }\n    const memUsed = blockSize * (N + p);\n    if (memUsed > maxmem) {\n        throw new Error('Scrypt: memused is bigger than maxMem. Expected 128 * r * (N + p) > maxmem of ' + maxmem);\n    }\n    // [B0...Bp−1] ← PBKDF2HMAC-SHA256(Passphrase, Salt, 1, blockSize*ParallelizationFactor)\n    // Since it has only one iteration there is no reason to use async variant\n    const B = (0,_pbkdf2_js__WEBPACK_IMPORTED_MODULE_1__.pbkdf2)(_sha2_js__WEBPACK_IMPORTED_MODULE_2__.sha256, password, salt, { c: 1, dkLen: blockSize * p });\n    const B32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(B);\n    // Re-used between parallel iterations. Array(iterations) of B\n    const V = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(new Uint8Array(blockSize * N));\n    const tmp = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.u32)(new Uint8Array(blockSize));\n    let blockMixCb = () => { };\n    if (onProgress) {\n        const totalBlockMix = 2 * N * p;\n        // Invoke callback if progress changes from 10.01 to 10.02\n        // Allows to draw smooth progress bar on up to 8K screen\n        const callbackPer = Math.max(Math.floor(totalBlockMix / 10000), 1);\n        let blockMixCnt = 0;\n        blockMixCb = () => {\n            blockMixCnt++;\n            if (onProgress && (!(blockMixCnt % callbackPer) || blockMixCnt === totalBlockMix))\n                onProgress(blockMixCnt / totalBlockMix);\n        };\n    }\n    return { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb, asyncTick };\n}\nfunction scryptOutput(password, dkLen, B, V, tmp) {\n    const res = (0,_pbkdf2_js__WEBPACK_IMPORTED_MODULE_1__.pbkdf2)(_sha2_js__WEBPACK_IMPORTED_MODULE_2__.sha256, password, B, { c: 1, dkLen });\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(B, V, tmp);\n    return res;\n}\n/**\n * Scrypt KDF from RFC 7914.\n * @param password - pass\n * @param salt - salt\n * @param opts - parameters\n * - `N` is cpu/mem work factor (power of 2 e.g. 2**18)\n * - `r` is block size (8 is common), fine-tunes sequential memory read size and performance\n * - `p` is parallelization factor (1 is common)\n * - `dkLen` is output key length in bytes e.g. 32.\n * - `asyncTick` - (default: 10) max time in ms for which async function can block execution\n * - `maxmem` - (default: `1024 ** 3 + 1024` aka 1GB+1KB). A limit that the app could use for scrypt\n * - `onProgress` - callback function that would be executed for progress report\n * @returns Derived key\n * @example\n * scrypt('password', 'salt', { N: 2**18, r: 8, p: 1, dkLen: 32 });\n */\nfunction scrypt(password, salt, opts) {\n    const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb } = scryptInit(password, salt, opts);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.swap32IfBE)(B32);\n    for (let pi = 0; pi < p; pi++) {\n        const Pi = blockSize32 * pi;\n        for (let i = 0; i < blockSize32; i++)\n            V[i] = B32[Pi + i]; // V[0] = B[i]\n        for (let i = 0, pos = 0; i < N - 1; i++) {\n            BlockMix(V, pos, V, (pos += blockSize32), r); // V[i] = BlockMix(V[i-1]);\n            blockMixCb();\n        }\n        BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n        blockMixCb();\n        for (let i = 0; i < N; i++) {\n            // First u32 of the last 64-byte block (u32 is LE)\n            const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n            for (let k = 0; k < blockSize32; k++)\n                tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n            BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n            blockMixCb();\n        }\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.swap32IfBE)(B32);\n    return scryptOutput(password, dkLen, B, V, tmp);\n}\n/**\n * Scrypt KDF from RFC 7914. Async version.\n * @example\n * await scryptAsync('password', 'salt', { N: 2**18, r: 8, p: 1, dkLen: 32 });\n */\nasync function scryptAsync(password, salt, opts) {\n    const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb, asyncTick } = scryptInit(password, salt, opts);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.swap32IfBE)(B32);\n    for (let pi = 0; pi < p; pi++) {\n        const Pi = blockSize32 * pi;\n        for (let i = 0; i < blockSize32; i++)\n            V[i] = B32[Pi + i]; // V[0] = B[i]\n        let pos = 0;\n        await (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.asyncLoop)(N - 1, asyncTick, () => {\n            BlockMix(V, pos, V, (pos += blockSize32), r); // V[i] = BlockMix(V[i-1]);\n            blockMixCb();\n        });\n        BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n        blockMixCb();\n        await (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.asyncLoop)(N, asyncTick, () => {\n            // First u32 of the last 64-byte block (u32 is LE)\n            const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n            for (let k = 0; k < blockSize32; k++)\n                tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n            BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n            blockMixCb();\n        });\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.swap32IfBE)(B32);\n    return scryptOutput(password, dkLen, B, V, tmp);\n}\n//# sourceMappingURL=scrypt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/scrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/sha2.js":
/*!****************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/sha2.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SHA224: () => (/* binding */ SHA224),\n/* harmony export */   SHA256: () => (/* binding */ SHA256),\n/* harmony export */   SHA384: () => (/* binding */ SHA384),\n/* harmony export */   SHA512: () => (/* binding */ SHA512),\n/* harmony export */   SHA512_224: () => (/* binding */ SHA512_224),\n/* harmony export */   SHA512_256: () => (/* binding */ SHA512_256),\n/* harmony export */   sha224: () => (/* binding */ sha224),\n/* harmony export */   sha256: () => (/* binding */ sha256),\n/* harmony export */   sha384: () => (/* binding */ sha384),\n/* harmony export */   sha512: () => (/* binding */ sha512),\n/* harmony export */   sha512_224: () => (/* binding */ sha512_224),\n/* harmony export */   sha512_256: () => (/* binding */ sha512_256)\n/* harmony export */ });\n/* harmony import */ var _md_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_md.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/_md.js\");\n/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_u64.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/_u64.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/../../node_modules/@noble/hashes/esm/utils.js\");\n/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\n\n\n\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 32) {\n        super(64, outputLen, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 7) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 17) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 6) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 11) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 2) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 13) ^ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.rotr)(A, 22);\n            const T2 = (sigma0 + (0,_md_js__WEBPACK_IMPORTED_MODULE_0__.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA256_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n    }\n}\nclass SHA224 extends SHA256 {\n    constructor() {\n        super(28);\n        this.A = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[0] | 0;\n        this.B = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[1] | 0;\n        this.C = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[2] | 0;\n        this.D = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[3] | 0;\n        this.E = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[4] | 0;\n        this.F = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[5] | 0;\n        this.G = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[6] | 0;\n        this.H = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA224_IV[7] | 0;\n    }\n}\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => _u64_js__WEBPACK_IMPORTED_MODULE_2__.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nclass SHA512 extends _md_js__WEBPACK_IMPORTED_MODULE_0__.HashMD {\n    constructor(outputLen = 64) {\n        super(128, outputLen, 16, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA512_IV[15] | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W15h, W15l, 7);\n            const s0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 1) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W15h, W15l, 8) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSH(W2h, W2l, 6);\n            const s1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(W2h, W2l, 19) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(W2h, W2l, 61) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Eh, El, 41);\n            const sigma1l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 14) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Eh, El, 18) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSH(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBH(Ah, Al, 39);\n            const sigma0l = _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrSL(Ah, Al, 28) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 34) ^ _u64_js__WEBPACK_IMPORTED_MODULE_2__.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3L(T1l, sigma0l, MAJl);\n            Ah = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = _u64_js__WEBPACK_IMPORTED_MODULE_2__.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(SHA512_W_H, SHA512_W_L);\n    }\n    destroy() {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nclass SHA384 extends SHA512 {\n    constructor() {\n        super(48);\n        this.Ah = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[0] | 0;\n        this.Al = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[1] | 0;\n        this.Bh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[2] | 0;\n        this.Bl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[3] | 0;\n        this.Ch = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[4] | 0;\n        this.Cl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[5] | 0;\n        this.Dh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[6] | 0;\n        this.Dl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[7] | 0;\n        this.Eh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[8] | 0;\n        this.El = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[9] | 0;\n        this.Fh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[10] | 0;\n        this.Fl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[11] | 0;\n        this.Gh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[12] | 0;\n        this.Gl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[13] | 0;\n        this.Hh = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[14] | 0;\n        this.Hl = _md_js__WEBPACK_IMPORTED_MODULE_0__.SHA384_IV[15] | 0;\n    }\n}\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n    0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n    0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n    0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super(28);\n        this.Ah = T224_IV[0] | 0;\n        this.Al = T224_IV[1] | 0;\n        this.Bh = T224_IV[2] | 0;\n        this.Bl = T224_IV[3] | 0;\n        this.Ch = T224_IV[4] | 0;\n        this.Cl = T224_IV[5] | 0;\n        this.Dh = T224_IV[6] | 0;\n        this.Dl = T224_IV[7] | 0;\n        this.Eh = T224_IV[8] | 0;\n        this.El = T224_IV[9] | 0;\n        this.Fh = T224_IV[10] | 0;\n        this.Fl = T224_IV[11] | 0;\n        this.Gh = T224_IV[12] | 0;\n        this.Gl = T224_IV[13] | 0;\n        this.Hh = T224_IV[14] | 0;\n        this.Hl = T224_IV[15] | 0;\n    }\n}\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super(32);\n        this.Ah = T256_IV[0] | 0;\n        this.Al = T256_IV[1] | 0;\n        this.Bh = T256_IV[2] | 0;\n        this.Bl = T256_IV[3] | 0;\n        this.Ch = T256_IV[4] | 0;\n        this.Cl = T256_IV[5] | 0;\n        this.Dh = T256_IV[6] | 0;\n        this.Dl = T256_IV[7] | 0;\n        this.Eh = T256_IV[8] | 0;\n        this.El = T256_IV[9] | 0;\n        this.Fh = T256_IV[10] | 0;\n        this.Fl = T256_IV[11] | 0;\n        this.Gh = T256_IV[12] | 0;\n        this.Gl = T256_IV[13] | 0;\n        this.Hh = T256_IV[14] | 0;\n        this.Hl = T256_IV[15] | 0;\n    }\n}\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nconst sha256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nconst sha224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nconst sha512 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nconst sha384 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_256 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nconst sha512_224 = /* @__PURE__ */ (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.createHasher)(() => new SHA512_224());\n//# sourceMappingURL=sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/sha2.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@noble/hashes/esm/utils.js":
/*!*****************************************************!*\
  !*** ../../node_modules/@noble/hashes/esm/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash),\n/* harmony export */   abytes: () => (/* binding */ abytes),\n/* harmony export */   aexists: () => (/* binding */ aexists),\n/* harmony export */   ahash: () => (/* binding */ ahash),\n/* harmony export */   anumber: () => (/* binding */ anumber),\n/* harmony export */   aoutput: () => (/* binding */ aoutput),\n/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),\n/* harmony export */   byteSwap: () => (/* binding */ byteSwap),\n/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),\n/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),\n/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),\n/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),\n/* harmony export */   checkOpts: () => (/* binding */ checkOpts),\n/* harmony export */   clean: () => (/* binding */ clean),\n/* harmony export */   concatBytes: () => (/* binding */ concatBytes),\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   createOptHasher: () => (/* binding */ createOptHasher),\n/* harmony export */   createView: () => (/* binding */ createView),\n/* harmony export */   createXOFer: () => (/* binding */ createXOFer),\n/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),\n/* harmony export */   isBytes: () => (/* binding */ isBytes),\n/* harmony export */   isLE: () => (/* binding */ isLE),\n/* harmony export */   kdfInputToBytes: () => (/* binding */ kdfInputToBytes),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   randomBytes: () => (/* binding */ randomBytes),\n/* harmony export */   rotl: () => (/* binding */ rotl),\n/* harmony export */   rotr: () => (/* binding */ rotr),\n/* harmony export */   swap32IfBE: () => (/* binding */ swap32IfBE),\n/* harmony export */   swap8IfBE: () => (/* binding */ swap8IfBE),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   u32: () => (/* binding */ u32),\n/* harmony export */   u8: () => (/* binding */ u8),\n/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),\n/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),\n/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),\n/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/crypto */ \"(rsc)/../../node_modules/@noble/hashes/esm/cryptoNode.js\");\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\n\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nconst isLE = /* @__PURE__ */ (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nconst swap8IfBE = isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nconst byteSwapIfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nconst swap32IfBE = isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nconst wrapConstructor = createHasher;\nconst wrapConstructorWithOpts = createOptHasher;\nconst wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues === 'function') {\n        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes === 'function') {\n        return Uint8Array.from(_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_0__.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@noble/hashes/esm/utils.js\n");

/***/ })

};
;