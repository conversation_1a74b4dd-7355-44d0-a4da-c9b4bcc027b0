"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@better-auth";
exports.ids = ["vendor-chunks/@better-auth"];
exports.modules = {

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/base64.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/base64.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64: () => (/* binding */ base64),\n/* harmony export */   base64Url: () => (/* binding */ base64Url)\n/* harmony export */ });\nfunction getAlphabet(urlSafe) {\n  return urlSafe ? \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\" : \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n}\nfunction base64Encode(data, alphabet, padding) {\n  let result = \"\";\n  let buffer = 0;\n  let shift = 0;\n  for (const byte of data) {\n    buffer = buffer << 8 | byte;\n    shift += 8;\n    while (shift >= 6) {\n      shift -= 6;\n      result += alphabet[buffer >> shift & 63];\n    }\n  }\n  if (shift > 0) {\n    result += alphabet[buffer << 6 - shift & 63];\n  }\n  if (padding) {\n    const padCount = (4 - result.length % 4) % 4;\n    result += \"=\".repeat(padCount);\n  }\n  return result;\n}\nfunction base64Decode(data, alphabet) {\n  const decodeMap = /* @__PURE__ */ new Map();\n  for (let i = 0; i < alphabet.length; i++) {\n    decodeMap.set(alphabet[i], i);\n  }\n  const result = [];\n  let buffer = 0;\n  let bitsCollected = 0;\n  for (const char of data) {\n    if (char === \"=\")\n      break;\n    const value = decodeMap.get(char);\n    if (value === void 0) {\n      throw new Error(`Invalid Base64 character: ${char}`);\n    }\n    buffer = buffer << 6 | value;\n    bitsCollected += 6;\n    if (bitsCollected >= 8) {\n      bitsCollected -= 8;\n      result.push(buffer >> bitsCollected & 255);\n    }\n  }\n  return Uint8Array.from(result);\n}\nconst base64 = {\n  encode(data, options = {}) {\n    const alphabet = getAlphabet(false);\n    const buffer = typeof data === \"string\" ? new TextEncoder().encode(data) : new Uint8Array(data);\n    return base64Encode(buffer, alphabet, options.padding ?? true);\n  },\n  decode(data) {\n    if (typeof data !== \"string\") {\n      data = new TextDecoder().decode(data);\n    }\n    const urlSafe = data.includes(\"-\") || data.includes(\"_\");\n    const alphabet = getAlphabet(urlSafe);\n    return base64Decode(data, alphabet);\n  }\n};\nconst base64Url = {\n  encode(data, options = {}) {\n    const alphabet = getAlphabet(true);\n    const buffer = typeof data === \"string\" ? new TextEncoder().encode(data) : new Uint8Array(data);\n    return base64Encode(buffer, alphabet, options.padding ?? true);\n  },\n  decode(data) {\n    const urlSafe = data.includes(\"-\") || data.includes(\"_\");\n    const alphabet = getAlphabet(urlSafe);\n    return base64Decode(data, alphabet);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/base64.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/binary.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/binary.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   binary: () => (/* binding */ binary)\n/* harmony export */ });\nconst decoders = /* @__PURE__ */ new Map();\nconst encoder = new TextEncoder();\nconst binary = {\n  decode: (data, encoding = \"utf-8\") => {\n    if (!decoders.has(encoding)) {\n      decoders.set(encoding, new TextDecoder(encoding));\n    }\n    const decoder = decoders.get(encoding);\n    return decoder.decode(data);\n  },\n  encode: encoder.encode\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BiZXR0ZXItYXV0aC91dGlscy9kaXN0L2JpbmFyeS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL0BiZXR0ZXItYXV0aC91dGlscy9kaXN0L2JpbmFyeS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZGVjb2RlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuY29uc3QgZW5jb2RlciA9IG5ldyBUZXh0RW5jb2RlcigpO1xuY29uc3QgYmluYXJ5ID0ge1xuICBkZWNvZGU6IChkYXRhLCBlbmNvZGluZyA9IFwidXRmLThcIikgPT4ge1xuICAgIGlmICghZGVjb2RlcnMuaGFzKGVuY29kaW5nKSkge1xuICAgICAgZGVjb2RlcnMuc2V0KGVuY29kaW5nLCBuZXcgVGV4dERlY29kZXIoZW5jb2RpbmcpKTtcbiAgICB9XG4gICAgY29uc3QgZGVjb2RlciA9IGRlY29kZXJzLmdldChlbmNvZGluZyk7XG4gICAgcmV0dXJuIGRlY29kZXIuZGVjb2RlKGRhdGEpO1xuICB9LFxuICBlbmNvZGU6IGVuY29kZXIuZW5jb2RlXG59O1xuXG5leHBvcnQgeyBiaW5hcnkgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/binary.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/hash.mjs":
/*!***********************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/hash.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHash: () => (/* binding */ createHash)\n/* harmony export */ });\n/* harmony import */ var uncrypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uncrypto */ \"(rsc)/../../node_modules/uncrypto/dist/crypto.node.mjs\");\n/* harmony import */ var _base64_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base64.mjs */ \"(rsc)/../../node_modules/@better-auth/utils/dist/base64.mjs\");\n\n\n\nfunction createHash(algorithm, encoding) {\n  return {\n    digest: async (input) => {\n      const encoder = new TextEncoder();\n      const data = typeof input === \"string\" ? encoder.encode(input) : input;\n      const hashBuffer = await uncrypto__WEBPACK_IMPORTED_MODULE_1__.subtle.digest(algorithm, data);\n      if (encoding === \"hex\") {\n        const hashArray = Array.from(new Uint8Array(hashBuffer));\n        const hashHex = hashArray.map((b) => b.toString(16).padStart(2, \"0\")).join(\"\");\n        return hashHex;\n      }\n      if (encoding === \"base64\" || encoding === \"base64url\" || encoding === \"base64urlnopad\") {\n        if (encoding.includes(\"url\")) {\n          return _base64_mjs__WEBPACK_IMPORTED_MODULE_0__.base64Url.encode(hashBuffer, {\n            padding: encoding !== \"base64urlnopad\"\n          });\n        }\n        const hashBase64 = _base64_mjs__WEBPACK_IMPORTED_MODULE_0__.base64.encode(hashBuffer);\n        return hashBase64;\n      }\n      return hashBuffer;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BiZXR0ZXItYXV0aC91dGlscy9kaXN0L2hhc2gubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUNlOztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLDRDQUFNO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGtEQUFTO0FBQzFCO0FBQ0EsV0FBVztBQUNYO0FBQ0EsMkJBQTJCLCtDQUFNO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9AYmV0dGVyLWF1dGgvdXRpbHMvZGlzdC9oYXNoLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdWJ0bGUgfSBmcm9tICd1bmNyeXB0byc7XG5pbXBvcnQgeyBiYXNlNjRVcmwsIGJhc2U2NCB9IGZyb20gJy4vYmFzZTY0Lm1qcyc7XG5cbmZ1bmN0aW9uIGNyZWF0ZUhhc2goYWxnb3JpdGhtLCBlbmNvZGluZykge1xuICByZXR1cm4ge1xuICAgIGRpZ2VzdDogYXN5bmMgKGlucHV0KSA9PiB7XG4gICAgICBjb25zdCBlbmNvZGVyID0gbmV3IFRleHRFbmNvZGVyKCk7XG4gICAgICBjb25zdCBkYXRhID0gdHlwZW9mIGlucHV0ID09PSBcInN0cmluZ1wiID8gZW5jb2Rlci5lbmNvZGUoaW5wdXQpIDogaW5wdXQ7XG4gICAgICBjb25zdCBoYXNoQnVmZmVyID0gYXdhaXQgc3VidGxlLmRpZ2VzdChhbGdvcml0aG0sIGRhdGEpO1xuICAgICAgaWYgKGVuY29kaW5nID09PSBcImhleFwiKSB7XG4gICAgICAgIGNvbnN0IGhhc2hBcnJheSA9IEFycmF5LmZyb20obmV3IFVpbnQ4QXJyYXkoaGFzaEJ1ZmZlcikpO1xuICAgICAgICBjb25zdCBoYXNoSGV4ID0gaGFzaEFycmF5Lm1hcCgoYikgPT4gYi50b1N0cmluZygxNikucGFkU3RhcnQoMiwgXCIwXCIpKS5qb2luKFwiXCIpO1xuICAgICAgICByZXR1cm4gaGFzaEhleDtcbiAgICAgIH1cbiAgICAgIGlmIChlbmNvZGluZyA9PT0gXCJiYXNlNjRcIiB8fCBlbmNvZGluZyA9PT0gXCJiYXNlNjR1cmxcIiB8fCBlbmNvZGluZyA9PT0gXCJiYXNlNjR1cmxub3BhZFwiKSB7XG4gICAgICAgIGlmIChlbmNvZGluZy5pbmNsdWRlcyhcInVybFwiKSkge1xuICAgICAgICAgIHJldHVybiBiYXNlNjRVcmwuZW5jb2RlKGhhc2hCdWZmZXIsIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IGVuY29kaW5nICE9PSBcImJhc2U2NHVybG5vcGFkXCJcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBoYXNoQmFzZTY0ID0gYmFzZTY0LmVuY29kZShoYXNoQnVmZmVyKTtcbiAgICAgICAgcmV0dXJuIGhhc2hCYXNlNjQ7XG4gICAgICB9XG4gICAgICByZXR1cm4gaGFzaEJ1ZmZlcjtcbiAgICB9XG4gIH07XG59XG5cbmV4cG9ydCB7IGNyZWF0ZUhhc2ggfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/hash.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/hex.mjs":
/*!**********************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/hex.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hex: () => (/* binding */ hex)\n/* harmony export */ });\nconst hexadecimal = \"0123456789abcdef\";\nconst hex = {\n  encode: (data) => {\n    if (typeof data === \"string\") {\n      data = new TextEncoder().encode(data);\n    }\n    if (data.byteLength === 0) {\n      return \"\";\n    }\n    const buffer = new Uint8Array(data);\n    let result = \"\";\n    for (const byte of buffer) {\n      result += byte.toString(16).padStart(2, \"0\");\n    }\n    return result;\n  },\n  decode: (data) => {\n    if (!data) {\n      return \"\";\n    }\n    if (typeof data === \"string\") {\n      if (data.length % 2 !== 0) {\n        throw new Error(\"Invalid hexadecimal string\");\n      }\n      if (!new RegExp(`^[${hexadecimal}]+$`).test(data)) {\n        throw new Error(\"Invalid hexadecimal string\");\n      }\n      const result = new Uint8Array(data.length / 2);\n      for (let i = 0; i < data.length; i += 2) {\n        result[i / 2] = parseInt(data.slice(i, i + 2), 16);\n      }\n      return new TextDecoder().decode(result);\n    }\n    return new TextDecoder().decode(data);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/hex.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/hmac.mjs":
/*!***********************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/hmac.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHMAC: () => (/* binding */ createHMAC)\n/* harmony export */ });\n/* harmony import */ var uncrypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uncrypto */ \"(rsc)/../../node_modules/uncrypto/dist/crypto.node.mjs\");\n/* harmony import */ var _hex_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hex.mjs */ \"(rsc)/../../node_modules/@better-auth/utils/dist/hex.mjs\");\n/* harmony import */ var _base64_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base64.mjs */ \"(rsc)/../../node_modules/@better-auth/utils/dist/base64.mjs\");\n\n\n\n\nconst createHMAC = (algorithm = \"SHA-256\", encoding = \"none\") => {\n  const hmac = {\n    importKey: async (key, keyUsage) => {\n      return uncrypto__WEBPACK_IMPORTED_MODULE_2__.subtle.importKey(\n        \"raw\",\n        typeof key === \"string\" ? new TextEncoder().encode(key) : key,\n        { name: \"HMAC\", hash: { name: algorithm } },\n        false,\n        [keyUsage]\n      );\n    },\n    sign: async (hmacKey, data) => {\n      if (typeof hmacKey === \"string\") {\n        hmacKey = await hmac.importKey(hmacKey, \"sign\");\n      }\n      const signature = await uncrypto__WEBPACK_IMPORTED_MODULE_2__.subtle.sign(\n        \"HMAC\",\n        hmacKey,\n        typeof data === \"string\" ? new TextEncoder().encode(data) : data\n      );\n      if (encoding === \"hex\") {\n        return _hex_mjs__WEBPACK_IMPORTED_MODULE_0__.hex.encode(signature);\n      }\n      if (encoding === \"base64\" || encoding === \"base64url\" || encoding === \"base64urlnopad\") {\n        return _base64_mjs__WEBPACK_IMPORTED_MODULE_1__.base64Url.encode(signature, {\n          padding: encoding !== \"base64urlnopad\"\n        });\n      }\n      return signature;\n    },\n    verify: async (hmacKey, data, signature) => {\n      if (typeof hmacKey === \"string\") {\n        hmacKey = await hmac.importKey(hmacKey, \"verify\");\n      }\n      if (encoding === \"hex\") {\n        signature = _hex_mjs__WEBPACK_IMPORTED_MODULE_0__.hex.decode(signature);\n      }\n      if (encoding === \"base64\" || encoding === \"base64url\" || encoding === \"base64urlnopad\") {\n        signature = await _base64_mjs__WEBPACK_IMPORTED_MODULE_1__.base64.decode(signature);\n      }\n      return uncrypto__WEBPACK_IMPORTED_MODULE_2__.subtle.verify(\n        \"HMAC\",\n        hmacKey,\n        typeof signature === \"string\" ? new TextEncoder().encode(signature) : signature,\n        typeof data === \"string\" ? new TextEncoder().encode(data) : data\n      );\n    }\n  };\n  return hmac;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/hmac.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/index.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomValues: () => (/* reexport safe */ uncrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues),\n/* harmony export */   randomUUID: () => (/* reexport safe */ uncrypto__WEBPACK_IMPORTED_MODULE_0__.randomUUID),\n/* harmony export */   subtle: () => (/* reexport safe */ uncrypto__WEBPACK_IMPORTED_MODULE_0__.subtle)\n/* harmony export */ });\n/* harmony import */ var uncrypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uncrypto */ \"(rsc)/../../node_modules/uncrypto/dist/crypto.node.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BiZXR0ZXItYXV0aC91dGlscy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvQGJldHRlci1hdXRoL3V0aWxzL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJ3VuY3J5cHRvJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@better-auth/utils/dist/random.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/@better-auth/utils/dist/random.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRandomStringGenerator: () => (/* binding */ createRandomStringGenerator)\n/* harmony export */ });\n/* harmony import */ var uncrypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uncrypto */ \"(rsc)/../../node_modules/uncrypto/dist/crypto.node.mjs\");\n\n\nfunction expandAlphabet(alphabet) {\n  switch (alphabet) {\n    case \"a-z\":\n      return \"abcdefghijklmnopqrstuvwxyz\";\n    case \"A-Z\":\n      return \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n    case \"0-9\":\n      return \"0123456789\";\n    case \"-_\":\n      return \"-_\";\n    default:\n      throw new Error(`Unsupported alphabet: ${alphabet}`);\n  }\n}\nfunction createRandomStringGenerator(...baseAlphabets) {\n  const baseCharSet = baseAlphabets.map(expandAlphabet).join(\"\");\n  if (baseCharSet.length === 0) {\n    throw new Error(\n      \"No valid characters provided for random string generation.\"\n    );\n  }\n  const baseCharSetLength = baseCharSet.length;\n  return (length, ...alphabets) => {\n    if (length <= 0) {\n      throw new Error(\"Length must be a positive integer.\");\n    }\n    let charSet = baseCharSet;\n    let charSetLength = baseCharSetLength;\n    if (alphabets.length > 0) {\n      charSet = alphabets.map(expandAlphabet).join(\"\");\n      charSetLength = charSet.length;\n    }\n    const maxValid = Math.floor(256 / charSetLength) * charSetLength;\n    const buf = new Uint8Array(length * 2);\n    const bufLength = buf.length;\n    let result = \"\";\n    let bufIndex = bufLength;\n    let rand;\n    while (result.length < length) {\n      if (bufIndex >= bufLength) {\n        (0,uncrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues)(buf);\n        bufIndex = 0;\n      }\n      rand = buf[bufIndex++];\n      if (rand < maxValid) {\n        result += charSet[rand % charSetLength];\n      }\n    }\n    return result;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BiZXR0ZXItYXV0aC91dGlscy9kaXN0L3JhbmRvbS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsU0FBUztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEseURBQWU7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVDIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvQGJldHRlci1hdXRoL3V0aWxzL2Rpc3QvcmFuZG9tLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRSYW5kb21WYWx1ZXMgfSBmcm9tICd1bmNyeXB0byc7XG5cbmZ1bmN0aW9uIGV4cGFuZEFscGhhYmV0KGFscGhhYmV0KSB7XG4gIHN3aXRjaCAoYWxwaGFiZXQpIHtcbiAgICBjYXNlIFwiYS16XCI6XG4gICAgICByZXR1cm4gXCJhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5elwiO1xuICAgIGNhc2UgXCJBLVpcIjpcbiAgICAgIHJldHVybiBcIkFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaXCI7XG4gICAgY2FzZSBcIjAtOVwiOlxuICAgICAgcmV0dXJuIFwiMDEyMzQ1Njc4OVwiO1xuICAgIGNhc2UgXCItX1wiOlxuICAgICAgcmV0dXJuIFwiLV9cIjtcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbnN1cHBvcnRlZCBhbHBoYWJldDogJHthbHBoYWJldH1gKTtcbiAgfVxufVxuZnVuY3Rpb24gY3JlYXRlUmFuZG9tU3RyaW5nR2VuZXJhdG9yKC4uLmJhc2VBbHBoYWJldHMpIHtcbiAgY29uc3QgYmFzZUNoYXJTZXQgPSBiYXNlQWxwaGFiZXRzLm1hcChleHBhbmRBbHBoYWJldCkuam9pbihcIlwiKTtcbiAgaWYgKGJhc2VDaGFyU2V0Lmxlbmd0aCA9PT0gMCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIFwiTm8gdmFsaWQgY2hhcmFjdGVycyBwcm92aWRlZCBmb3IgcmFuZG9tIHN0cmluZyBnZW5lcmF0aW9uLlwiXG4gICAgKTtcbiAgfVxuICBjb25zdCBiYXNlQ2hhclNldExlbmd0aCA9IGJhc2VDaGFyU2V0Lmxlbmd0aDtcbiAgcmV0dXJuIChsZW5ndGgsIC4uLmFscGhhYmV0cykgPT4ge1xuICAgIGlmIChsZW5ndGggPD0gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTGVuZ3RoIG11c3QgYmUgYSBwb3NpdGl2ZSBpbnRlZ2VyLlwiKTtcbiAgICB9XG4gICAgbGV0IGNoYXJTZXQgPSBiYXNlQ2hhclNldDtcbiAgICBsZXQgY2hhclNldExlbmd0aCA9IGJhc2VDaGFyU2V0TGVuZ3RoO1xuICAgIGlmIChhbHBoYWJldHMubGVuZ3RoID4gMCkge1xuICAgICAgY2hhclNldCA9IGFscGhhYmV0cy5tYXAoZXhwYW5kQWxwaGFiZXQpLmpvaW4oXCJcIik7XG4gICAgICBjaGFyU2V0TGVuZ3RoID0gY2hhclNldC5sZW5ndGg7XG4gICAgfVxuICAgIGNvbnN0IG1heFZhbGlkID0gTWF0aC5mbG9vcigyNTYgLyBjaGFyU2V0TGVuZ3RoKSAqIGNoYXJTZXRMZW5ndGg7XG4gICAgY29uc3QgYnVmID0gbmV3IFVpbnQ4QXJyYXkobGVuZ3RoICogMik7XG4gICAgY29uc3QgYnVmTGVuZ3RoID0gYnVmLmxlbmd0aDtcbiAgICBsZXQgcmVzdWx0ID0gXCJcIjtcbiAgICBsZXQgYnVmSW5kZXggPSBidWZMZW5ndGg7XG4gICAgbGV0IHJhbmQ7XG4gICAgd2hpbGUgKHJlc3VsdC5sZW5ndGggPCBsZW5ndGgpIHtcbiAgICAgIGlmIChidWZJbmRleCA+PSBidWZMZW5ndGgpIHtcbiAgICAgICAgZ2V0UmFuZG9tVmFsdWVzKGJ1Zik7XG4gICAgICAgIGJ1ZkluZGV4ID0gMDtcbiAgICAgIH1cbiAgICAgIHJhbmQgPSBidWZbYnVmSW5kZXgrK107XG4gICAgICBpZiAocmFuZCA8IG1heFZhbGlkKSB7XG4gICAgICAgIHJlc3VsdCArPSBjaGFyU2V0W3JhbmQgJSBjaGFyU2V0TGVuZ3RoXTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlUmFuZG9tU3RyaW5nR2VuZXJhdG9yIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@better-auth/utils/dist/random.mjs\n");

/***/ })

};
;