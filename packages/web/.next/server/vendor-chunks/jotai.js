"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai";
exports.ids = ["vendor-chunks/jotai"];
exports.modules = {

/***/ "(ssr)/../../node_modules/jotai/esm/react.mjs":
/*!**********************************************!*\
  !*** ../../node_modules/jotai/esm/react.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useAtom: () => (/* binding */ useAtom),\n/* harmony export */   useAtomValue: () => (/* binding */ useAtomValue),\n/* harmony export */   useSetAtom: () => (/* binding */ useSetAtom),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/../../node_modules/jotai/esm/vanilla.mjs\");\n/* harmony import */ var jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jotai/vanilla/internals */ \"(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\");\n/* __next_internal_client_entry_do_not_use__ Provider,useAtom,useAtomValue,useSetAtom,useStore auto */ \n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nfunction useStore(options) {\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(StoreContext);\n    return (options == null ? void 0 : options.store) || store || (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.getDefaultStore)();\n}\nfunction Provider({ children, store }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n    if (!store && !storeRef.current) {\n        storeRef.current = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)();\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(StoreContext.Provider, {\n        value: store || storeRef.current\n    }, children);\n}\nconst isPromiseLike = (x)=>typeof (x == null ? void 0 : x.then) === \"function\";\nconst attachPromiseStatus = (promise)=>{\n    if (!promise.status) {\n        promise.status = \"pending\";\n        promise.then((v)=>{\n            promise.status = \"fulfilled\";\n            promise.value = v;\n        }, (e)=>{\n            promise.status = \"rejected\";\n            promise.reason = e;\n        });\n    }\n};\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // A shim for older React versions\n((promise)=>{\n    if (promise.status === \"pending\") {\n        throw promise;\n    } else if (promise.status === \"fulfilled\") {\n        return promise.value;\n    } else if (promise.status === \"rejected\") {\n        throw promise.reason;\n    } else {\n        attachPromiseStatus(promise);\n        throw promise;\n    }\n});\nconst continuablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst createContinuablePromise = (promise, getValue)=>{\n    let continuablePromise = continuablePromiseMap.get(promise);\n    if (!continuablePromise) {\n        continuablePromise = new Promise((resolve, reject)=>{\n            let curr = promise;\n            const onFulfilled = (me)=>(v)=>{\n                    if (curr === me) {\n                        resolve(v);\n                    }\n                };\n            const onRejected = (me)=>(e)=>{\n                    if (curr === me) {\n                        reject(e);\n                    }\n                };\n            const onAbort = ()=>{\n                try {\n                    const nextValue = getValue();\n                    if (isPromiseLike(nextValue)) {\n                        continuablePromiseMap.set(nextValue, continuablePromise);\n                        curr = nextValue;\n                        nextValue.then(onFulfilled(nextValue), onRejected(nextValue));\n                        (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__.INTERNAL_registerAbortHandler)(nextValue, onAbort);\n                    } else {\n                        resolve(nextValue);\n                    }\n                } catch (e) {\n                    reject(e);\n                }\n            };\n            promise.then(onFulfilled(promise), onRejected(promise));\n            (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_2__.INTERNAL_registerAbortHandler)(promise, onAbort);\n        });\n        continuablePromiseMap.set(promise, continuablePromise);\n    }\n    return continuablePromise;\n};\nfunction useAtomValue(atom, options) {\n    const { delay, unstable_promiseStatus: promiseStatus = !react__WEBPACK_IMPORTED_MODULE_0__.use } = options || {};\n    const store = useStore(options);\n    const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)({\n        \"useAtomValue.useReducer\": (prev)=>{\n            const nextValue = store.get(atom);\n            if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n                return prev;\n            }\n            return [\n                nextValue,\n                store,\n                atom\n            ];\n        }\n    }[\"useAtomValue.useReducer\"], void 0, {\n        \"useAtomValue.useReducer\": ()=>[\n                store.get(atom),\n                store,\n                atom\n            ]\n    }[\"useAtomValue.useReducer\"]);\n    let value = valueFromReducer;\n    if (storeFromReducer !== store || atomFromReducer !== atom) {\n        rerender();\n        value = store.get(atom);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAtomValue.useEffect\": ()=>{\n            const unsub = store.sub(atom, {\n                \"useAtomValue.useEffect.unsub\": ()=>{\n                    if (promiseStatus) {\n                        try {\n                            const value2 = store.get(atom);\n                            if (isPromiseLike(value2)) {\n                                attachPromiseStatus(createContinuablePromise(value2, {\n                                    \"useAtomValue.useEffect.unsub\": ()=>store.get(atom)\n                                }[\"useAtomValue.useEffect.unsub\"]));\n                            }\n                        } catch (e) {}\n                    }\n                    if (typeof delay === \"number\") {\n                        setTimeout(rerender, delay);\n                        return;\n                    }\n                    rerender();\n                }\n            }[\"useAtomValue.useEffect.unsub\"]);\n            rerender();\n            return unsub;\n        }\n    }[\"useAtomValue.useEffect\"], [\n        store,\n        atom,\n        delay,\n        promiseStatus\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(value);\n    if (isPromiseLike(value)) {\n        const promise = createContinuablePromise(value, ()=>store.get(atom));\n        if (promiseStatus) {\n            attachPromiseStatus(promise);\n        }\n        return use(promise);\n    }\n    return value;\n}\nfunction useSetAtom(atom, options) {\n    const store = useStore(options);\n    const setAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSetAtom.useCallback[setAtom]\": (...args)=>{\n            if (( false ? 0 : void 0) !== \"production\" && !(\"write\" in atom)) {\n                throw new Error(\"not writable atom\");\n            }\n            return store.set(atom, ...args);\n        }\n    }[\"useSetAtom.useCallback[setAtom]\"], [\n        store,\n        atom\n    ]);\n    return setAtom;\n}\nfunction useAtom(atom, options) {\n    return [\n        useAtomValue(atom, options),\n        // We do wrong type assertion here, which results in throwing an error.\n        useSetAtom(atom, options)\n    ];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/jotai/esm/vanilla.mjs":
/*!************************************************!*\
  !*** ../../node_modules/jotai/esm/vanilla.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INTERNAL_overrideCreateStore: () => (/* binding */ INTERNAL_overrideCreateStore),\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   getDefaultStore: () => (/* binding */ getDefaultStore)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla/internals */ \"(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\");\n\n\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString() {\n      return ( false ? 0 : void 0) !== \"production\" && this.debugLabel ? key + \":\" + this.debugLabel : key;\n    }\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nconst createDevStoreRev4 = () => {\n  let inRestoreAtom = 0;\n  const storeHooks = (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_initializeStoreHooks)({});\n  const atomStateMap = /* @__PURE__ */ new WeakMap();\n  const mountedAtoms = /* @__PURE__ */ new WeakMap();\n  const store = (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_buildStoreRev1)(\n    atomStateMap,\n    mountedAtoms,\n    void 0,\n    void 0,\n    void 0,\n    void 0,\n    storeHooks,\n    void 0,\n    (atom, get, set, ...args) => {\n      if (inRestoreAtom) {\n        return set(atom, ...args);\n      }\n      return atom.write(get, set, ...args);\n    }\n  );\n  const debugMountedAtoms = /* @__PURE__ */ new Set();\n  storeHooks.m.add(void 0, (atom) => {\n    debugMountedAtoms.add(atom);\n    const atomState = atomStateMap.get(atom);\n    atomState.m = mountedAtoms.get(atom);\n  });\n  storeHooks.u.add(void 0, (atom) => {\n    debugMountedAtoms.delete(atom);\n    const atomState = atomStateMap.get(atom);\n    delete atomState.m;\n  });\n  const devStore = {\n    // store dev methods (these are tentative and subject to change without notice)\n    dev4_get_internal_weak_map: () => {\n      console.log(\"Deprecated: Use devstore from the devtools library\");\n      return atomStateMap;\n    },\n    dev4_get_mounted_atoms: () => debugMountedAtoms,\n    dev4_restore_atoms: (values) => {\n      const restoreAtom = {\n        read: () => null,\n        write: (_get, set) => {\n          ++inRestoreAtom;\n          try {\n            for (const [atom, value] of values) {\n              if (\"init\" in atom) {\n                set(atom, value);\n              }\n            }\n          } finally {\n            --inRestoreAtom;\n          }\n        }\n      };\n      store.set(restoreAtom);\n    }\n  };\n  return Object.assign(store, devStore);\n};\nlet overiddenCreateStore;\nfunction INTERNAL_overrideCreateStore(fn) {\n  overiddenCreateStore = fn(overiddenCreateStore);\n}\nfunction createStore() {\n  if (overiddenCreateStore) {\n    return overiddenCreateStore();\n  }\n  if (( false ? 0 : void 0) !== \"production\") {\n    return createDevStoreRev4();\n  }\n  return (0,jotai_vanilla_internals__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_buildStoreRev1)();\n}\nlet defaultStore;\nfunction getDefaultStore() {\n  if (!defaultStore) {\n    defaultStore = createStore();\n    if (( false ? 0 : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs":
/*!**********************************************************!*\
  !*** ../../node_modules/jotai/esm/vanilla/internals.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INTERNAL_abortPromise: () => (/* binding */ INTERNAL_abortPromise),\n/* harmony export */   INTERNAL_addPendingPromiseToDependency: () => (/* binding */ INTERNAL_addPendingPromiseToDependency),\n/* harmony export */   INTERNAL_buildStoreRev1: () => (/* binding */ INTERNAL_buildStoreRev1),\n/* harmony export */   INTERNAL_getBuildingBlocksRev1: () => (/* binding */ INTERNAL_getBuildingBlocksRev1),\n/* harmony export */   INTERNAL_getMountedOrPendingDependents: () => (/* binding */ INTERNAL_getMountedOrPendingDependents),\n/* harmony export */   INTERNAL_hasInitialValue: () => (/* binding */ INTERNAL_hasInitialValue),\n/* harmony export */   INTERNAL_initializeStoreHooks: () => (/* binding */ INTERNAL_initializeStoreHooks),\n/* harmony export */   INTERNAL_isActuallyWritableAtom: () => (/* binding */ INTERNAL_isActuallyWritableAtom),\n/* harmony export */   INTERNAL_isAtomStateInitialized: () => (/* binding */ INTERNAL_isAtomStateInitialized),\n/* harmony export */   INTERNAL_isPendingPromise: () => (/* binding */ INTERNAL_isPendingPromise),\n/* harmony export */   INTERNAL_isPromiseLike: () => (/* binding */ INTERNAL_isPromiseLike),\n/* harmony export */   INTERNAL_isSelfAtom: () => (/* binding */ INTERNAL_isSelfAtom),\n/* harmony export */   INTERNAL_promiseStateMap: () => (/* binding */ INTERNAL_promiseStateMap),\n/* harmony export */   INTERNAL_registerAbortHandler: () => (/* binding */ INTERNAL_registerAbortHandler),\n/* harmony export */   INTERNAL_returnAtomValue: () => (/* binding */ INTERNAL_returnAtomValue),\n/* harmony export */   INTERNAL_setAtomStateValueOrPromise: () => (/* binding */ INTERNAL_setAtomStateValueOrPromise)\n/* harmony export */ });\nconst isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst isAtomStateInitialized = (atomState) => \"v\" in atomState || \"e\" in atomState;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  if (( false ? 0 : void 0) !== \"production\" && !(\"v\" in atomState)) {\n    throw new Error(\"[Bug] atom state is not initialized\");\n  }\n  return atomState.v;\n};\nconst promiseStateMap = /* @__PURE__ */ new WeakMap();\nconst isPendingPromise = (value) => {\n  var _a;\n  return isPromiseLike(value) && !!((_a = promiseStateMap.get(value)) == null ? void 0 : _a[0]);\n};\nconst abortPromise = (promise) => {\n  const promiseState = promiseStateMap.get(promise);\n  if (promiseState == null ? void 0 : promiseState[0]) {\n    promiseState[0] = false;\n    promiseState[1].forEach((fn) => fn());\n  }\n};\nconst registerAbortHandler = (promise, abortHandler) => {\n  let promiseState = promiseStateMap.get(promise);\n  if (!promiseState) {\n    promiseState = [true, /* @__PURE__ */ new Set()];\n    promiseStateMap.set(promise, promiseState);\n    const settle = () => {\n      promiseState[0] = false;\n    };\n    promise.then(settle, settle);\n  }\n  promiseState[1].add(abortHandler);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst addPendingPromiseToDependency = (atom, promise, dependencyAtomState) => {\n  if (!dependencyAtomState.p.has(atom)) {\n    dependencyAtomState.p.add(atom);\n    promise.then(\n      () => {\n        dependencyAtomState.p.delete(atom);\n      },\n      () => {\n        dependencyAtomState.p.delete(atom);\n      }\n    );\n  }\n};\nconst setAtomStateValueOrPromise = (atom, valueOrPromise, ensureAtomState) => {\n  const atomState = ensureAtomState(atom);\n  const hasPrevValue = \"v\" in atomState;\n  const prevValue = atomState.v;\n  if (isPromiseLike(valueOrPromise)) {\n    for (const a of atomState.d.keys()) {\n      addPendingPromiseToDependency(atom, valueOrPromise, ensureAtomState(a));\n    }\n  }\n  atomState.v = valueOrPromise;\n  delete atomState.e;\n  if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {\n    ++atomState.n;\n    if (isPromiseLike(prevValue)) {\n      abortPromise(prevValue);\n    }\n  }\n};\nconst getMountedOrPendingDependents = (atom, atomState, mountedMap) => {\n  var _a;\n  const dependents = /* @__PURE__ */ new Set();\n  for (const a of ((_a = mountedMap.get(atom)) == null ? void 0 : _a.t) || []) {\n    if (mountedMap.has(a)) {\n      dependents.add(a);\n    }\n  }\n  for (const atomWithPendingPromise of atomState.p) {\n    dependents.add(atomWithPendingPromise);\n  }\n  return dependents;\n};\nconst createStoreHook = () => {\n  const callbacks = /* @__PURE__ */ new Set();\n  const notify = () => {\n    callbacks.forEach((fn) => fn());\n  };\n  notify.add = (fn) => {\n    callbacks.add(fn);\n    return () => {\n      callbacks.delete(fn);\n    };\n  };\n  return notify;\n};\nconst createStoreHookForAtoms = () => {\n  const all = {};\n  const callbacks = /* @__PURE__ */ new WeakMap();\n  const notify = (atom) => {\n    var _a, _b;\n    (_a = callbacks.get(all)) == null ? void 0 : _a.forEach((fn) => fn(atom));\n    (_b = callbacks.get(atom)) == null ? void 0 : _b.forEach((fn) => fn());\n  };\n  notify.add = (atom, fn) => {\n    const key = atom || all;\n    const fns = (callbacks.has(key) ? callbacks : callbacks.set(key, /* @__PURE__ */ new Set())).get(key);\n    fns.add(fn);\n    return () => {\n      fns == null ? void 0 : fns.delete(fn);\n      if (!fns.size) {\n        callbacks.delete(key);\n      }\n    };\n  };\n  return notify;\n};\nconst initializeStoreHooks = (storeHooks) => {\n  storeHooks.c || (storeHooks.c = createStoreHookForAtoms());\n  storeHooks.m || (storeHooks.m = createStoreHookForAtoms());\n  storeHooks.u || (storeHooks.u = createStoreHookForAtoms());\n  storeHooks.f || (storeHooks.f = createStoreHook());\n  return storeHooks;\n};\nconst BUILDING_BLOCKS = Symbol();\nconst getBuildingBlocks = (store) => store[BUILDING_BLOCKS];\nconst buildStore = (atomStateMap = /* @__PURE__ */ new WeakMap(), mountedMap = /* @__PURE__ */ new WeakMap(), invalidatedAtoms = /* @__PURE__ */ new WeakMap(), changedAtoms = /* @__PURE__ */ new Set(), mountCallbacks = /* @__PURE__ */ new Set(), unmountCallbacks = /* @__PURE__ */ new Set(), storeHooks = {}, atomRead = (atom, ...params) => atom.read(...params), atomWrite = (atom, ...params) => atom.write(...params), atomOnInit = (atom, store) => {\n  var _a;\n  return (_a = atom.unstable_onInit) == null ? void 0 : _a.call(atom, store);\n}, atomOnMount = (atom, setAtom) => {\n  var _a;\n  return (_a = atom.onMount) == null ? void 0 : _a.call(atom, setAtom);\n}, ...buildingBlockFunctions) => {\n  const ensureAtomState = buildingBlockFunctions[0] || ((atom) => {\n    if (( false ? 0 : void 0) !== \"production\" && !atom) {\n      throw new Error(\"Atom is undefined or null\");\n    }\n    let atomState = atomStateMap.get(atom);\n    if (!atomState) {\n      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };\n      atomStateMap.set(atom, atomState);\n      atomOnInit == null ? void 0 : atomOnInit(atom, store);\n    }\n    return atomState;\n  });\n  const flushCallbacks = buildingBlockFunctions[1] || (() => {\n    const errors = [];\n    const call = (fn) => {\n      try {\n        fn();\n      } catch (e) {\n        errors.push(e);\n      }\n    };\n    do {\n      if (storeHooks.f) {\n        call(storeHooks.f);\n      }\n      const callbacks = /* @__PURE__ */ new Set();\n      const add = callbacks.add.bind(callbacks);\n      changedAtoms.forEach((atom) => {\n        var _a;\n        return (_a = mountedMap.get(atom)) == null ? void 0 : _a.l.forEach(add);\n      });\n      changedAtoms.clear();\n      unmountCallbacks.forEach(add);\n      unmountCallbacks.clear();\n      mountCallbacks.forEach(add);\n      mountCallbacks.clear();\n      callbacks.forEach(call);\n      if (changedAtoms.size) {\n        recomputeInvalidatedAtoms();\n      }\n    } while (changedAtoms.size || unmountCallbacks.size || mountCallbacks.size);\n    if (errors.length) {\n      throw new AggregateError(errors);\n    }\n  });\n  const recomputeInvalidatedAtoms = buildingBlockFunctions[2] || (() => {\n    const topSortedReversed = [];\n    const visiting = /* @__PURE__ */ new WeakSet();\n    const visited = /* @__PURE__ */ new WeakSet();\n    const stack = Array.from(changedAtoms);\n    while (stack.length) {\n      const a = stack[stack.length - 1];\n      const aState = ensureAtomState(a);\n      if (visited.has(a)) {\n        stack.pop();\n        continue;\n      }\n      if (visiting.has(a)) {\n        if (invalidatedAtoms.get(a) === aState.n) {\n          topSortedReversed.push([a, aState]);\n        } else if (( false ? 0 : void 0) !== \"production\" && invalidatedAtoms.has(a)) {\n          throw new Error(\"[Bug] invalidated atom exists\");\n        }\n        visited.add(a);\n        stack.pop();\n        continue;\n      }\n      visiting.add(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        if (!visiting.has(d)) {\n          stack.push(d);\n        }\n      }\n    }\n    for (let i = topSortedReversed.length - 1; i >= 0; --i) {\n      const [a, aState] = topSortedReversed[i];\n      let hasChangedDeps = false;\n      for (const dep of aState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        readAtomState(a);\n        mountDependencies(a);\n      }\n      invalidatedAtoms.delete(a);\n    }\n  });\n  const readAtomState = buildingBlockFunctions[3] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    if (isAtomStateInitialized(atomState)) {\n      if (mountedMap.has(atom) && invalidatedAtoms.get(atom) !== atomState.n) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(\n        ([a, n]) => (\n          // Recursively, read the atom state of the dependency, and\n          // check if the atom epoch number is unchanged\n          readAtomState(a).n === n\n        )\n      )) {\n        return atomState;\n      }\n    }\n    atomState.d.clear();\n    let isSync = true;\n    const mountDependenciesIfAsync = () => {\n      if (mountedMap.has(atom)) {\n        mountDependencies(atom);\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    };\n    const getter = (a) => {\n      var _a2;\n      if (isSelfAtom(atom, a)) {\n        const aState2 = ensureAtomState(a);\n        if (!isAtomStateInitialized(aState2)) {\n          if (hasInitialValue(a)) {\n            setAtomStateValueOrPromise(a, a.init, ensureAtomState);\n          } else {\n            throw new Error(\"no atom init\");\n          }\n        }\n        return returnAtomValue(aState2);\n      }\n      const aState = readAtomState(a);\n      try {\n        return returnAtomValue(aState);\n      } finally {\n        atomState.d.set(a, aState.n);\n        if (isPendingPromise(atomState.v)) {\n          addPendingPromiseToDependency(atom, atomState.v, aState);\n        }\n        (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.t.add(atom);\n        if (!isSync) {\n          mountDependenciesIfAsync();\n        }\n      }\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if (( false ? 0 : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if (( false ? 0 : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              try {\n                return writeAtomState(atom, ...args);\n              } finally {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    const prevEpochNumber = atomState.n;\n    try {\n      const valueOrPromise = atomRead(atom, getter, options);\n      setAtomStateValueOrPromise(atom, valueOrPromise, ensureAtomState);\n      if (isPromiseLike(valueOrPromise)) {\n        registerAbortHandler(valueOrPromise, () => controller == null ? void 0 : controller.abort());\n        valueOrPromise.then(\n          mountDependenciesIfAsync,\n          mountDependenciesIfAsync\n        );\n      }\n      return atomState;\n    } catch (error) {\n      delete atomState.v;\n      atomState.e = error;\n      ++atomState.n;\n      return atomState;\n    } finally {\n      isSync = false;\n      if (prevEpochNumber !== atomState.n && invalidatedAtoms.get(atom) === prevEpochNumber) {\n        invalidatedAtoms.set(atom, atomState.n);\n        changedAtoms.add(atom);\n        (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, atom);\n      }\n    }\n  });\n  const invalidateDependents = buildingBlockFunctions[4] || ((atom) => {\n    const stack = [atom];\n    while (stack.length) {\n      const a = stack.pop();\n      const aState = ensureAtomState(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        const dState = ensureAtomState(d);\n        invalidatedAtoms.set(d, dState.n);\n        stack.push(d);\n      }\n    }\n  });\n  const writeAtomState = buildingBlockFunctions[5] || ((atom, ...args) => {\n    let isSync = true;\n    const getter = (a) => returnAtomValue(readAtomState(a));\n    const setter = (a, ...args2) => {\n      var _a;\n      const aState = ensureAtomState(a);\n      try {\n        if (isSelfAtom(atom, a)) {\n          if (!hasInitialValue(a)) {\n            throw new Error(\"atom not writable\");\n          }\n          const prevEpochNumber = aState.n;\n          const v = args2[0];\n          setAtomStateValueOrPromise(a, v, ensureAtomState);\n          mountDependencies(a);\n          if (prevEpochNumber !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n          return void 0;\n        } else {\n          return writeAtomState(a, ...args2);\n        }\n      } finally {\n        if (!isSync) {\n          recomputeInvalidatedAtoms();\n          flushCallbacks();\n        }\n      }\n    };\n    try {\n      return atomWrite(atom, getter, setter, ...args);\n    } finally {\n      isSync = false;\n    }\n  });\n  const mountDependencies = buildingBlockFunctions[6] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    const mounted = mountedMap.get(atom);\n    if (mounted && !isPendingPromise(atomState.v)) {\n      for (const [a, n] of atomState.d) {\n        if (!mounted.d.has(a)) {\n          const aState = ensureAtomState(a);\n          const aMounted = mountAtom(a);\n          aMounted.t.add(atom);\n          mounted.d.add(a);\n          if (n !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n        }\n      }\n      for (const a of mounted.d || []) {\n        if (!atomState.d.has(a)) {\n          mounted.d.delete(a);\n          const aMounted = unmountAtom(a);\n          aMounted == null ? void 0 : aMounted.t.delete(atom);\n        }\n      }\n    }\n  });\n  const mountAtom = buildingBlockFunctions[7] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (!mounted) {\n      readAtomState(atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = mountAtom(a);\n        aMounted.t.add(atom);\n      }\n      mounted = {\n        l: /* @__PURE__ */ new Set(),\n        d: new Set(atomState.d.keys()),\n        t: /* @__PURE__ */ new Set()\n      };\n      mountedMap.set(atom, mounted);\n      (_a = storeHooks.m) == null ? void 0 : _a.call(storeHooks, atom);\n      if (isActuallyWritableAtom(atom)) {\n        const processOnMount = () => {\n          let isSync = true;\n          const setAtom = (...args) => {\n            try {\n              return writeAtomState(atom, ...args);\n            } finally {\n              if (!isSync) {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n          try {\n            const onUnmount = atomOnMount(atom, setAtom);\n            if (onUnmount) {\n              mounted.u = () => {\n                isSync = true;\n                try {\n                  onUnmount();\n                } finally {\n                  isSync = false;\n                }\n              };\n            }\n          } finally {\n            isSync = false;\n          }\n        };\n        mountCallbacks.add(processOnMount);\n      }\n    }\n    return mounted;\n  });\n  const unmountAtom = buildingBlockFunctions[8] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (mounted && !mounted.l.size && !Array.from(mounted.t).some((a) => {\n      var _a2;\n      return (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.d.has(atom);\n    })) {\n      if (mounted.u) {\n        unmountCallbacks.add(mounted.u);\n      }\n      mounted = void 0;\n      mountedMap.delete(atom);\n      (_a = storeHooks.u) == null ? void 0 : _a.call(storeHooks, atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = unmountAtom(a);\n        aMounted == null ? void 0 : aMounted.t.delete(atom);\n      }\n      return void 0;\n    }\n    return mounted;\n  });\n  const buildingBlocks = [\n    // store state\n    atomStateMap,\n    mountedMap,\n    invalidatedAtoms,\n    changedAtoms,\n    mountCallbacks,\n    unmountCallbacks,\n    storeHooks,\n    // atom interceptors\n    atomRead,\n    atomWrite,\n    atomOnInit,\n    atomOnMount,\n    // building-block functions\n    ensureAtomState,\n    flushCallbacks,\n    recomputeInvalidatedAtoms,\n    readAtomState,\n    invalidateDependents,\n    writeAtomState,\n    mountDependencies,\n    mountAtom,\n    unmountAtom\n  ];\n  const store = {\n    get: (atom) => returnAtomValue(readAtomState(atom)),\n    set: (atom, ...args) => {\n      try {\n        return writeAtomState(atom, ...args);\n      } finally {\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    },\n    sub: (atom, listener) => {\n      const mounted = mountAtom(atom);\n      const listeners = mounted.l;\n      listeners.add(listener);\n      flushCallbacks();\n      return () => {\n        listeners.delete(listener);\n        unmountAtom(atom);\n        flushCallbacks();\n      };\n    }\n  };\n  Object.defineProperty(store, BUILDING_BLOCKS, { value: buildingBlocks });\n  return store;\n};\nconst INTERNAL_buildStoreRev1 = buildStore;\nconst INTERNAL_getBuildingBlocksRev1 = getBuildingBlocks;\nconst INTERNAL_initializeStoreHooks = initializeStoreHooks;\nconst INTERNAL_isSelfAtom = isSelfAtom;\nconst INTERNAL_hasInitialValue = hasInitialValue;\nconst INTERNAL_isActuallyWritableAtom = isActuallyWritableAtom;\nconst INTERNAL_isAtomStateInitialized = isAtomStateInitialized;\nconst INTERNAL_returnAtomValue = returnAtomValue;\nconst INTERNAL_promiseStateMap = promiseStateMap;\nconst INTERNAL_isPendingPromise = isPendingPromise;\nconst INTERNAL_abortPromise = abortPromise;\nconst INTERNAL_registerAbortHandler = registerAbortHandler;\nconst INTERNAL_isPromiseLike = isPromiseLike;\nconst INTERNAL_addPendingPromiseToDependency = addPendingPromiseToDependency;\nconst INTERNAL_setAtomStateValueOrPromise = setAtomStateValueOrPromise;\nconst INTERNAL_getMountedOrPendingDependents = getMountedOrPendingDependents;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/vanilla/internals.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/jotai/esm/vanilla/utils.mjs":
/*!******************************************************!*\
  !*** ../../node_modules/jotai/esm/vanilla/utils.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESET: () => (/* binding */ RESET),\n/* harmony export */   atomFamily: () => (/* binding */ atomFamily),\n/* harmony export */   atomWithDefault: () => (/* binding */ atomWithDefault),\n/* harmony export */   atomWithLazy: () => (/* binding */ atomWithLazy),\n/* harmony export */   atomWithObservable: () => (/* binding */ atomWithObservable),\n/* harmony export */   atomWithReducer: () => (/* binding */ atomWithReducer),\n/* harmony export */   atomWithRefresh: () => (/* binding */ atomWithRefresh),\n/* harmony export */   atomWithReset: () => (/* binding */ atomWithReset),\n/* harmony export */   atomWithStorage: () => (/* binding */ atomWithStorage),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   freezeAtom: () => (/* binding */ freezeAtom),\n/* harmony export */   freezeAtomCreator: () => (/* binding */ freezeAtomCreator),\n/* harmony export */   loadable: () => (/* binding */ loadable),\n/* harmony export */   selectAtom: () => (/* binding */ selectAtom),\n/* harmony export */   splitAtom: () => (/* binding */ splitAtom),\n/* harmony export */   unstable_withStorageValidator: () => (/* binding */ withStorageValidator),\n/* harmony export */   unwrap: () => (/* binding */ unwrap)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/../../node_modules/jotai/esm/vanilla.mjs\");\n\n\nconst RESET = Symbol(\n  ( false ? 0 : void 0) !== \"production\" ? \"RESET\" : \"\"\n);\n\nfunction atomWithReset(initialValue) {\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    initialValue,\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(anAtom)) : update;\n      set(anAtom, nextValue === RESET ? initialValue : nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithReducer(initialValue, reducer) {\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(initialValue, function(get, set, action) {\n    set(this, reducer(get(this), action));\n  });\n}\n\nfunction atomFamily(initializeAtom, areEqual) {\n  let shouldRemove = null;\n  const atoms = /* @__PURE__ */ new Map();\n  const listeners = /* @__PURE__ */ new Set();\n  const createAtom = (param) => {\n    let item;\n    if (areEqual === void 0) {\n      item = atoms.get(param);\n    } else {\n      for (const [key, value] of atoms) {\n        if (areEqual(key, param)) {\n          item = value;\n          break;\n        }\n      }\n    }\n    if (item !== void 0) {\n      if (shouldRemove == null ? void 0 : shouldRemove(item[1], param)) {\n        createAtom.remove(param);\n      } else {\n        return item[0];\n      }\n    }\n    const newAtom = initializeAtom(param);\n    atoms.set(param, [newAtom, Date.now()]);\n    notifyListeners(\"CREATE\", param, newAtom);\n    return newAtom;\n  };\n  const notifyListeners = (type, param, atom) => {\n    for (const listener of listeners) {\n      listener({ type, param, atom });\n    }\n  };\n  createAtom.unstable_listen = (callback) => {\n    listeners.add(callback);\n    return () => {\n      listeners.delete(callback);\n    };\n  };\n  createAtom.getParams = () => atoms.keys();\n  createAtom.remove = (param) => {\n    if (areEqual === void 0) {\n      if (!atoms.has(param)) return;\n      const [atom] = atoms.get(param);\n      atoms.delete(param);\n      notifyListeners(\"REMOVE\", param, atom);\n    } else {\n      for (const [key, [atom]] of atoms) {\n        if (areEqual(key, param)) {\n          atoms.delete(key);\n          notifyListeners(\"REMOVE\", key, atom);\n          break;\n        }\n      }\n    }\n  };\n  createAtom.setShouldRemove = (fn) => {\n    shouldRemove = fn;\n    if (!shouldRemove) return;\n    for (const [key, [atom, createdAt]] of atoms) {\n      if (shouldRemove(createdAt, key)) {\n        atoms.delete(key);\n        notifyListeners(\"REMOVE\", key, atom);\n      }\n    }\n  };\n  return createAtom;\n}\n\nconst getCached$2 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$3 = /* @__PURE__ */ new WeakMap();\nconst memo3 = (create, dep1, dep2, dep3) => {\n  const cache2 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache1$3, dep1);\n  const cache3 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache2, dep2);\n  return getCached$2(create, cache3, dep3);\n};\nfunction selectAtom(anAtom, selector, equalityFn = Object.is) {\n  return memo3(\n    () => {\n      const EMPTY = Symbol();\n      const selectValue = ([value, prevSlice]) => {\n        if (prevSlice === EMPTY) {\n          return selector(value);\n        }\n        const slice = selector(value, prevSlice);\n        return equalityFn(prevSlice, slice) ? prevSlice : slice;\n      };\n      const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(derivedAtom);\n        const value = get(anAtom);\n        return selectValue([value, prev]);\n      });\n      derivedAtom.init = EMPTY;\n      return derivedAtom;\n    },\n    anAtom,\n    selector,\n    equalityFn\n  );\n}\n\nconst frozenAtoms = /* @__PURE__ */ new WeakSet();\nconst deepFreeze = (value) => {\n  if (typeof value !== \"object\" || value === null) {\n    return value;\n  }\n  Object.freeze(value);\n  const propNames = Object.getOwnPropertyNames(value);\n  for (const name of propNames) {\n    deepFreeze(value[name]);\n  }\n  return value;\n};\nfunction freezeAtom(anAtom) {\n  if (frozenAtoms.has(anAtom)) {\n    return anAtom;\n  }\n  frozenAtoms.add(anAtom);\n  const origRead = anAtom.read;\n  anAtom.read = function(get, options) {\n    return deepFreeze(origRead.call(this, get, options));\n  };\n  if (\"write\" in anAtom) {\n    const origWrite = anAtom.write;\n    anAtom.write = function(get, set, ...args) {\n      return origWrite.call(\n        this,\n        get,\n        (...setArgs) => {\n          if (setArgs[0] === anAtom) {\n            setArgs[1] = deepFreeze(setArgs[1]);\n          }\n          return set(...setArgs);\n        },\n        ...args\n      );\n    };\n  }\n  return anAtom;\n}\nfunction freezeAtomCreator(createAtom) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] freezeAtomCreator is deprecated, define it on users end\"\n    );\n  }\n  return (...args) => freezeAtom(createAtom(...args));\n}\n\nconst getCached$1 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$2 = /* @__PURE__ */ new WeakMap();\nconst memo2$1 = (create, dep1, dep2) => {\n  const cache2 = getCached$1(() => /* @__PURE__ */ new WeakMap(), cache1$2, dep1);\n  return getCached$1(create, cache2, dep2);\n};\nconst cacheKeyForEmptyKeyExtractor = {};\nconst isWritable = (atom2) => !!atom2.write;\nconst isFunction = (x) => typeof x === \"function\";\nfunction splitAtom(arrAtom, keyExtractor) {\n  return memo2$1(\n    () => {\n      const mappingCache = /* @__PURE__ */ new WeakMap();\n      const getMapping = (arr, prev) => {\n        let mapping = mappingCache.get(arr);\n        if (mapping) {\n          return mapping;\n        }\n        const prevMapping = prev && mappingCache.get(prev);\n        const atomList = [];\n        const keyList = [];\n        arr.forEach((item, index) => {\n          const key = keyExtractor ? keyExtractor(item) : index;\n          keyList[index] = key;\n          const cachedAtom = prevMapping && prevMapping.atomList[prevMapping.keyList.indexOf(key)];\n          if (cachedAtom) {\n            atomList[index] = cachedAtom;\n            return;\n          }\n          const read = (get) => {\n            const prev2 = get(mappingAtom);\n            const currArr = get(arrAtom);\n            const mapping2 = getMapping(currArr, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= currArr.length) {\n              const prevItem = arr[getMapping(arr).keyList.indexOf(key)];\n              if (prevItem) {\n                return prevItem;\n              }\n              throw new Error(\"splitAtom: index out of bounds for read\");\n            }\n            return currArr[index2];\n          };\n          const write = (get, set, update) => {\n            const prev2 = get(mappingAtom);\n            const arr2 = get(arrAtom);\n            const mapping2 = getMapping(arr2, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= arr2.length) {\n              throw new Error(\"splitAtom: index out of bounds for write\");\n            }\n            const nextItem = isFunction(update) ? update(arr2[index2]) : update;\n            if (!Object.is(arr2[index2], nextItem)) {\n              set(arrAtom, [\n                ...arr2.slice(0, index2),\n                nextItem,\n                ...arr2.slice(index2 + 1)\n              ]);\n            }\n          };\n          atomList[index] = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read, write) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read);\n        });\n        if (prevMapping && prevMapping.keyList.length === keyList.length && prevMapping.keyList.every((x, i) => x === keyList[i])) {\n          mapping = prevMapping;\n        } else {\n          mapping = { arr, atomList, keyList };\n        }\n        mappingCache.set(arr, mapping);\n        return mapping;\n      };\n      const mappingAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(mappingAtom);\n        const arr = get(arrAtom);\n        const mapping = getMapping(arr, prev == null ? void 0 : prev.arr);\n        return mapping;\n      });\n      if (( false ? 0 : void 0) !== \"production\") {\n        mappingAtom.debugPrivate = true;\n      }\n      mappingAtom.init = void 0;\n      const splittedAtom = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => get(mappingAtom).atomList,\n        (get, set, action) => {\n          switch (action.type) {\n            case \"remove\": {\n              const index = get(splittedAtom).indexOf(action.atom);\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  ...arr.slice(index + 1)\n                ]);\n              }\n              break;\n            }\n            case \"insert\": {\n              const index = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  action.value,\n                  ...arr.slice(index)\n                ]);\n              }\n              break;\n            }\n            case \"move\": {\n              const index1 = get(splittedAtom).indexOf(action.atom);\n              const index2 = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index1 >= 0 && index2 >= 0) {\n                const arr = get(arrAtom);\n                if (index1 < index2) {\n                  set(arrAtom, [\n                    ...arr.slice(0, index1),\n                    ...arr.slice(index1 + 1, index2),\n                    arr[index1],\n                    ...arr.slice(index2)\n                  ]);\n                } else {\n                  set(arrAtom, [\n                    ...arr.slice(0, index2),\n                    arr[index1],\n                    ...arr.slice(index2, index1),\n                    ...arr.slice(index1 + 1)\n                  ]);\n                }\n              }\n              break;\n            }\n          }\n        }\n      ) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(mappingAtom).atomList);\n      return splittedAtom;\n    },\n    arrAtom,\n    keyExtractor || cacheKeyForEmptyKeyExtractor\n  );\n}\n\nfunction atomWithDefault(getDefault) {\n  const EMPTY = Symbol();\n  const overwrittenAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(EMPTY);\n  if (( false ? 0 : void 0) !== \"production\") {\n    overwrittenAtom.debugPrivate = true;\n  }\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      const overwritten = get(overwrittenAtom);\n      if (overwritten !== EMPTY) {\n        return overwritten;\n      }\n      return getDefault(get, options);\n    },\n    (get, set, update) => {\n      if (update === RESET) {\n        set(overwrittenAtom, EMPTY);\n      } else if (typeof update === \"function\") {\n        const prevValue = get(anAtom);\n        set(overwrittenAtom, update(prevValue));\n      } else {\n        set(overwrittenAtom, update);\n      }\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike$3 = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction withStorageValidator(validator) {\n  return (unknownStorage) => {\n    const storage = {\n      ...unknownStorage,\n      getItem: (key, initialValue) => {\n        const validate = (value2) => {\n          if (!validator(value2)) {\n            return initialValue;\n          }\n          return value2;\n        };\n        const value = unknownStorage.getItem(key, initialValue);\n        if (isPromiseLike$3(value)) {\n          return value.then(validate);\n        }\n        return validate(value);\n      }\n    };\n    return storage;\n  };\n}\nfunction createJSONStorage(getStringStorage = () => {\n  try {\n    return window.localStorage;\n  } catch (e) {\n    if (( false ? 0 : void 0) !== \"production\") {\n      if (typeof window !== \"undefined\") {\n        console.warn(e);\n      }\n    }\n    return void 0;\n  }\n}, options) {\n  var _a;\n  let lastStr;\n  let lastValue;\n  const storage = {\n    getItem: (key, initialValue) => {\n      var _a2, _b;\n      const parse = (str2) => {\n        str2 = str2 || \"\";\n        if (lastStr !== str2) {\n          try {\n            lastValue = JSON.parse(str2, options == null ? void 0 : options.reviver);\n          } catch (e) {\n            return initialValue;\n          }\n          lastStr = str2;\n        }\n        return lastValue;\n      };\n      const str = (_b = (_a2 = getStringStorage()) == null ? void 0 : _a2.getItem(key)) != null ? _b : null;\n      if (isPromiseLike$3(str)) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (key, newValue) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.setItem(\n        key,\n        JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n      );\n    },\n    removeItem: (key) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.removeItem(key);\n    }\n  };\n  const createHandleSubscribe = (subscriber2) => (key, callback, initialValue) => subscriber2(key, (v) => {\n    let newValue;\n    try {\n      newValue = JSON.parse(v || \"\");\n    } catch (e) {\n      newValue = initialValue;\n    }\n    callback(newValue);\n  });\n  let subscriber;\n  try {\n    subscriber = (_a = getStringStorage()) == null ? void 0 : _a.subscribe;\n  } catch (e) {\n  }\n  if (!subscriber && typeof window !== \"undefined\" && typeof window.addEventListener === \"function\" && window.Storage) {\n    subscriber = (key, callback) => {\n      if (!(getStringStorage() instanceof window.Storage)) {\n        return () => {\n        };\n      }\n      const storageEventCallback = (e) => {\n        if (e.storageArea === getStringStorage() && e.key === key) {\n          callback(e.newValue);\n        }\n      };\n      window.addEventListener(\"storage\", storageEventCallback);\n      return () => {\n        window.removeEventListener(\"storage\", storageEventCallback);\n      };\n    };\n  }\n  if (subscriber) {\n    storage.subscribe = createHandleSubscribe(subscriber);\n  }\n  return storage;\n}\nconst defaultStorage = createJSONStorage();\nfunction atomWithStorage(key, initialValue, storage = defaultStorage, options) {\n  const getOnInit = options == null ? void 0 : options.getOnInit;\n  const baseAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    getOnInit ? storage.getItem(key, initialValue) : initialValue\n  );\n  if (( false ? 0 : void 0) !== \"production\") {\n    baseAtom.debugPrivate = true;\n  }\n  baseAtom.onMount = (setAtom) => {\n    setAtom(storage.getItem(key, initialValue));\n    let unsub;\n    if (storage.subscribe) {\n      unsub = storage.subscribe(key, setAtom, initialValue);\n    }\n    return unsub;\n  };\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => get(baseAtom),\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(baseAtom)) : update;\n      if (nextValue === RESET) {\n        set(baseAtom, initialValue);\n        return storage.removeItem(key);\n      }\n      if (isPromiseLike$3(nextValue)) {\n        return nextValue.then((resolvedValue) => {\n          set(baseAtom, resolvedValue);\n          return storage.setItem(key, resolvedValue);\n        });\n      }\n      set(baseAtom, nextValue);\n      return storage.setItem(key, nextValue);\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike$2 = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction atomWithObservable(getObservable, options) {\n  const returnResultData = (result) => {\n    if (\"e\" in result) {\n      throw result.e;\n    }\n    return result.d;\n  };\n  const observableResultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n    var _a;\n    let observable = getObservable(get);\n    const itself = (_a = observable[Symbol.observable]) == null ? void 0 : _a.call(observable);\n    if (itself) {\n      observable = itself;\n    }\n    let resolve;\n    const makePending = () => new Promise((r) => {\n      resolve = r;\n    });\n    const initialResult = options && \"initialValue\" in options ? {\n      d: typeof options.initialValue === \"function\" ? options.initialValue() : options.initialValue\n    } : makePending();\n    let setResult;\n    let lastResult;\n    const listener = (result) => {\n      lastResult = result;\n      resolve == null ? void 0 : resolve(result);\n      setResult == null ? void 0 : setResult(result);\n    };\n    let subscription;\n    let timer;\n    const isNotMounted = () => !setResult;\n    const unsubscribe = () => {\n      if (subscription) {\n        subscription.unsubscribe();\n        subscription = void 0;\n      }\n    };\n    const start = () => {\n      if (subscription) {\n        clearTimeout(timer);\n        subscription.unsubscribe();\n      }\n      subscription = observable.subscribe({\n        next: (d) => listener({ d }),\n        error: (e) => listener({ e }),\n        complete: () => {\n        }\n      });\n      if (isNotMounted() && (options == null ? void 0 : options.unstable_timeout)) {\n        timer = setTimeout(unsubscribe, options.unstable_timeout);\n      }\n    };\n    start();\n    const resultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(lastResult || initialResult);\n    if (( false ? 0 : void 0) !== \"production\") {\n      resultAtom.debugPrivate = true;\n    }\n    resultAtom.onMount = (update) => {\n      setResult = update;\n      if (lastResult) {\n        update(lastResult);\n      }\n      if (subscription) {\n        clearTimeout(timer);\n      } else {\n        start();\n      }\n      return () => {\n        setResult = void 0;\n        if (options == null ? void 0 : options.unstable_timeout) {\n          timer = setTimeout(unsubscribe, options.unstable_timeout);\n        } else {\n          unsubscribe();\n        }\n      };\n    };\n    return [resultAtom, observable, makePending, start, isNotMounted];\n  });\n  if (( false ? 0 : void 0) !== \"production\") {\n    observableResultAtom.debugPrivate = true;\n  }\n  const observableAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => {\n      const [resultAtom] = get(observableResultAtom);\n      const result = get(resultAtom);\n      if (isPromiseLike$2(result)) {\n        return result.then(returnResultData);\n      }\n      return returnResultData(result);\n    },\n    (get, set, data) => {\n      const [resultAtom, observable, makePending, start, isNotMounted] = get(observableResultAtom);\n      if (\"next\" in observable) {\n        if (isNotMounted()) {\n          set(resultAtom, makePending());\n          start();\n        }\n        observable.next(data);\n      } else {\n        throw new Error(\"observable is not subject\");\n      }\n    }\n  );\n  return observableAtom;\n}\n\nconst cache1$1 = /* @__PURE__ */ new WeakMap();\nconst memo1 = (create, dep1) => (cache1$1.has(dep1) ? cache1$1 : cache1$1.set(dep1, create())).get(dep1);\nconst isPromiseLike$1 = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst LOADING = { state: \"loading\" };\nfunction loadable(anAtom) {\n  return memo1(() => {\n    const loadableCache = /* @__PURE__ */ new WeakMap();\n    const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n    if (( false ? 0 : void 0) !== \"production\") {\n      refreshAtom.debugPrivate = true;\n    }\n    const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n      (get, { setSelf }) => {\n        get(refreshAtom);\n        let value;\n        try {\n          value = get(anAtom);\n        } catch (error) {\n          return { state: \"hasError\", error };\n        }\n        if (!isPromiseLike$1(value)) {\n          return { state: \"hasData\", data: value };\n        }\n        const promise = value;\n        const cached1 = loadableCache.get(promise);\n        if (cached1) {\n          return cached1;\n        }\n        promise.then(\n          (data) => {\n            loadableCache.set(promise, { state: \"hasData\", data });\n            setSelf();\n          },\n          (error) => {\n            loadableCache.set(promise, { state: \"hasError\", error });\n            setSelf();\n          }\n        );\n        const cached2 = loadableCache.get(promise);\n        if (cached2) {\n          return cached2;\n        }\n        loadableCache.set(promise, LOADING);\n        return LOADING;\n      },\n      (_get, set) => {\n        set(refreshAtom, (c) => c + 1);\n      }\n    );\n    if (( false ? 0 : void 0) !== \"production\") {\n      derivedAtom.debugPrivate = true;\n    }\n    return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(derivedAtom));\n  }, anAtom);\n}\n\nconst getCached = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1 = /* @__PURE__ */ new WeakMap();\nconst memo2 = (create, dep1, dep2) => {\n  const cache2 = getCached(() => /* @__PURE__ */ new WeakMap(), cache1, dep1);\n  return getCached(create, cache2, dep2);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst defaultFallback = () => void 0;\nfunction unwrap(anAtom, fallback = defaultFallback) {\n  return memo2(\n    () => {\n      const promiseErrorCache = /* @__PURE__ */ new WeakMap();\n      const promiseResultCache = /* @__PURE__ */ new WeakMap();\n      const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n      if (( false ? 0 : void 0) !== \"production\") {\n        refreshAtom.debugPrivate = true;\n      }\n      const promiseAndValueAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get, { setSelf }) => {\n          get(refreshAtom);\n          const prev = get(promiseAndValueAtom);\n          const promise = get(anAtom);\n          if (!isPromiseLike(promise)) {\n            return { v: promise };\n          }\n          if (promise !== (prev == null ? void 0 : prev.p)) {\n            promise.then(\n              (v) => {\n                promiseResultCache.set(promise, v);\n                setSelf();\n              },\n              (e) => {\n                promiseErrorCache.set(promise, e);\n                setSelf();\n              }\n            );\n          }\n          if (promiseErrorCache.has(promise)) {\n            throw promiseErrorCache.get(promise);\n          }\n          if (promiseResultCache.has(promise)) {\n            return {\n              p: promise,\n              v: promiseResultCache.get(promise)\n            };\n          }\n          if (prev && \"v\" in prev) {\n            return { p: promise, f: fallback(prev.v), v: prev.v };\n          }\n          return { p: promise, f: fallback() };\n        },\n        (_get, set) => {\n          set(refreshAtom, (c) => c + 1);\n        }\n      );\n      promiseAndValueAtom.init = void 0;\n      if (( false ? 0 : void 0) !== \"production\") {\n        promiseAndValueAtom.debugPrivate = true;\n      }\n      return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => {\n          const state = get(promiseAndValueAtom);\n          if (\"f\" in state) {\n            return state.f;\n          }\n          return state.v;\n        },\n        (_get, set, ...args) => set(anAtom, ...args)\n      );\n    },\n    anAtom,\n    fallback\n  );\n}\n\nfunction atomWithRefresh(read, write) {\n  const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n  if (( false ? 0 : void 0) !== \"production\") {\n    refreshAtom.debugPrivate = true;\n  }\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      get(refreshAtom);\n      return read(get, options);\n    },\n    (get, set, ...args) => {\n      if (args.length === 0) {\n        set(refreshAtom, (c) => c + 1);\n      } else if (write) {\n        return write(get, set, ...args);\n      } else if (( false ? 0 : void 0) !== \"production\") {\n        throw new Error(\"refresh must be called without arguments\");\n      }\n    }\n  );\n}\n\nfunction atomWithLazy(makeInitial) {\n  const a = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(void 0);\n  delete a.init;\n  Object.defineProperty(a, \"init\", {\n    get() {\n      return makeInitial();\n    }\n  });\n  return a;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/jotai/esm/vanilla/utils.mjs\n");

/***/ })

};
;