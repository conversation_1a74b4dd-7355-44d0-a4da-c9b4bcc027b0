"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lingui";
exports.ids = ["vendor-chunks/@lingui"];
exports.modules = {

/***/ "(rsc)/../../node_modules/@lingui/core/dist/index.mjs":
/*!******************************************************!*\
  !*** ../../node_modules/@lingui/core/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18n: () => (/* binding */ I18n),\n/* harmony export */   formats: () => (/* binding */ formats),\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   setupI18n: () => (/* binding */ setupI18n)\n/* harmony export */ });\n/* harmony import */ var _lingui_message_utils_compileMessage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lingui/message-utils/compileMessage */ \"(rsc)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs\");\n\n\nconst isString = (s) => typeof s === \"string\";\nconst isFunction = (f) => typeof f === \"function\";\n\nconst cache = /* @__PURE__ */ new Map();\nconst defaultLocale = \"en\";\nfunction normalizeLocales(locales) {\n  const out = Array.isArray(locales) ? locales : [locales];\n  return [...out, defaultLocale];\n}\nfunction date(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  if (!format) {\n    format = \"default\";\n  }\n  let o;\n  if (typeof format === \"string\") {\n    o = {\n      day: \"numeric\",\n      month: \"short\",\n      year: \"numeric\"\n    };\n    switch (format) {\n      case \"full\":\n        o.weekday = \"long\";\n      case \"long\":\n        o.month = \"long\";\n        break;\n      case \"short\":\n        o.month = \"numeric\";\n        break;\n    }\n  } else {\n    o = format;\n  }\n  const formatter = getMemoized(\n    () => cacheKey(\"date\", _locales, format),\n    () => new Intl.DateTimeFormat(_locales, o)\n  );\n  return formatter.format(isString(value) ? new Date(value) : value);\n}\nfunction time(locales, value, format) {\n  let o;\n  if (!format) {\n    format = \"default\";\n  }\n  if (typeof format === \"string\") {\n    o = {\n      second: \"numeric\",\n      minute: \"numeric\",\n      hour: \"numeric\"\n    };\n    switch (format) {\n      case \"full\":\n      case \"long\":\n        o.timeZoneName = \"short\";\n        break;\n      case \"short\":\n        delete o.second;\n    }\n  } else {\n    o = format;\n  }\n  return date(locales, value, o);\n}\nfunction number(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  const formatter = getMemoized(\n    () => cacheKey(\"number\", _locales, format),\n    () => new Intl.NumberFormat(_locales, format)\n  );\n  return formatter.format(value);\n}\nfunction plural(locales, ordinal, value, { offset = 0, ...rules }) {\n  const _locales = normalizeLocales(locales);\n  const plurals = ordinal ? getMemoized(\n    () => cacheKey(\"plural-ordinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"ordinal\" })\n  ) : getMemoized(\n    () => cacheKey(\"plural-cardinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"cardinal\" })\n  );\n  return rules[value] ?? rules[plurals.select(value - offset)] ?? rules.other;\n}\nfunction getMemoized(getKey, construct) {\n  const key = getKey();\n  let formatter = cache.get(key);\n  if (!formatter) {\n    formatter = construct();\n    cache.set(key, formatter);\n  }\n  return formatter;\n}\nfunction cacheKey(type, locales, options) {\n  const localeKey = locales.join(\"-\");\n  return `${type}-${localeKey}-${JSON.stringify(options)}`;\n}\n\nconst formats = {\n  __proto__: null,\n  date: date,\n  defaultLocale: defaultLocale,\n  number: number,\n  plural: plural,\n  time: time\n};\n\nconst ESCAPE_SEQUENCE_REGEX = /\\\\u[a-fA-F0-9]{4}|\\\\x[a-fA-F0-9]{2}/;\nconst decodeEscapeSequences = (str) => {\n  return str.replace(\n    // Same pattern but with capturing groups for extracting values during replacement\n    /\\\\u([a-fA-F0-9]{4})|\\\\x([a-fA-F0-9]{2})/g,\n    (_, unicode, hex) => {\n      if (unicode) {\n        const codePoint = parseInt(unicode, 16);\n        return String.fromCharCode(codePoint);\n      } else {\n        const codePoint = parseInt(hex, 16);\n        return String.fromCharCode(codePoint);\n      }\n    }\n  );\n};\n\nconst OCTOTHORPE_PH = \"%__lingui_octothorpe__%\";\nconst getDefaultFormats = (locale, passedLocales, formats = {}) => {\n  const locales = passedLocales || locale;\n  const style = (format) => {\n    if (typeof format === \"object\")\n      return format;\n    return formats[format];\n  };\n  const replaceOctothorpe = (value, message) => {\n    const numberFormat = Object.keys(formats).length ? style(\"number\") : void 0;\n    const valueStr = number(locales, value, numberFormat);\n    return message.replace(new RegExp(OCTOTHORPE_PH, \"g\"), valueStr);\n  };\n  return {\n    plural: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, false, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    selectordinal: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, true, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    select: selectFormatter,\n    number: (value, format) => number(\n      locales,\n      value,\n      style(format) || { style: format }\n    ),\n    date: (value, format) => date(locales, value, style(format) || format),\n    time: (value, format) => time(locales, value, style(format) || format)\n  };\n};\nconst selectFormatter = (value, rules) => rules[value] ?? rules.other;\nfunction interpolate(translation, locale, locales) {\n  return (values = {}, formats) => {\n    const formatters = getDefaultFormats(locale, locales, formats);\n    const formatMessage = (tokens, replaceOctothorpe = false) => {\n      if (!Array.isArray(tokens))\n        return tokens;\n      return tokens.reduce((message, token) => {\n        if (token === \"#\" && replaceOctothorpe) {\n          return message + OCTOTHORPE_PH;\n        }\n        if (isString(token)) {\n          return message + token;\n        }\n        const [name, type, format] = token;\n        let interpolatedFormat = {};\n        if (type === \"plural\" || type === \"selectordinal\" || type === \"select\") {\n          Object.entries(format).forEach(\n            ([key, value2]) => {\n              interpolatedFormat[key] = formatMessage(\n                value2,\n                type === \"plural\" || type === \"selectordinal\"\n              );\n            }\n          );\n        } else {\n          interpolatedFormat = format;\n        }\n        let value;\n        if (type) {\n          const formatter = formatters[type];\n          value = formatter(values[name], interpolatedFormat);\n        } else {\n          value = values[name];\n        }\n        if (value == null) {\n          return message;\n        }\n        return message + value;\n      }, \"\");\n    };\n    const result = formatMessage(translation);\n    if (isString(result) && ESCAPE_SEQUENCE_REGEX.test(result)) {\n      return decodeEscapeSequences(result);\n    }\n    if (isString(result))\n      return result;\n    return result ? String(result) : \"\";\n  };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => {\n  __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventEmitter {\n  constructor() {\n    __publicField$1(this, \"_events\", {});\n  }\n  on(event, listener) {\n    var _a;\n    (_a = this._events)[event] ?? (_a[event] = []);\n    this._events[event].push(listener);\n    return () => this.removeListener(event, listener);\n  }\n  removeListener(event, listener) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    const index = maybeListeners.indexOf(listener);\n    if (~index)\n      maybeListeners.splice(index, 1);\n  }\n  emit(event, ...args) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    maybeListeners.map((listener) => listener.apply(this, args));\n  }\n  _getListeners(event) {\n    const maybeListeners = this._events[event];\n    return Array.isArray(maybeListeners) ? maybeListeners : false;\n  }\n}\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass I18n extends EventEmitter {\n  constructor(params) {\n    super();\n    __publicField(this, \"_locale\", \"\");\n    __publicField(this, \"_locales\");\n    __publicField(this, \"_localeData\", {});\n    __publicField(this, \"_messages\", {});\n    __publicField(this, \"_missing\");\n    __publicField(this, \"_messageCompiler\");\n    /**\n     * Alias for {@see I18n._}\n     */\n    __publicField(this, \"t\", this._.bind(this));\n    if (true) {\n      this.setMessagesCompiler(_lingui_message_utils_compileMessage__WEBPACK_IMPORTED_MODULE_0__.compileMessage);\n    }\n    if (params.missing != null)\n      this._missing = params.missing;\n    if (params.messages != null)\n      this.load(params.messages);\n    if (params.localeData != null)\n      this.loadLocaleData(params.localeData);\n    if (typeof params.locale === \"string\" || params.locales) {\n      this.activate(params.locale ?? defaultLocale, params.locales);\n    }\n  }\n  get locale() {\n    return this._locale;\n  }\n  get locales() {\n    return this._locales;\n  }\n  get messages() {\n    return this._messages[this._locale] ?? {};\n  }\n  /**\n   * @deprecated this has no effect. Please remove this from the code. Deprecated in v4\n   */\n  get localeData() {\n    return this._localeData[this._locale] ?? {};\n  }\n  _loadLocaleData(locale, localeData) {\n    const maybeLocaleData = this._localeData[locale];\n    if (!maybeLocaleData) {\n      this._localeData[locale] = localeData;\n    } else {\n      Object.assign(maybeLocaleData, localeData);\n    }\n  }\n  /**\n   * Registers a `MessageCompiler` to enable the use of uncompiled catalogs at runtime.\n   *\n   * In production builds, the `MessageCompiler` is typically excluded to reduce bundle size.\n   * By default, message catalogs should be precompiled during the build process. However,\n   * if you need to compile catalogs at runtime, you can use this method to set a message compiler.\n   *\n   * Example usage:\n   *\n   * ```ts\n   * import { compileMessage } from \"@lingui/message-utils/compileMessage\";\n   *\n   * i18n.setMessagesCompiler(compileMessage);\n   * ```\n   */\n  setMessagesCompiler(compiler) {\n    this._messageCompiler = compiler;\n    return this;\n  }\n  /**\n   * @deprecated Plurals automatically used from Intl.PluralRules you can safely remove this call. Deprecated in v4\n   */\n  loadLocaleData(localeOrAllData, localeData) {\n    if (typeof localeOrAllData === \"string\") {\n      this._loadLocaleData(localeOrAllData, localeData);\n    } else {\n      Object.keys(localeOrAllData).forEach(\n        (locale) => this._loadLocaleData(locale, localeOrAllData[locale])\n      );\n    }\n    this.emit(\"change\");\n  }\n  _load(locale, messages) {\n    const maybeMessages = this._messages[locale];\n    if (!maybeMessages) {\n      this._messages[locale] = messages;\n    } else {\n      Object.assign(maybeMessages, messages);\n    }\n  }\n  load(localeOrMessages, messages) {\n    if (typeof localeOrMessages == \"string\" && typeof messages === \"object\") {\n      this._load(localeOrMessages, messages);\n    } else {\n      Object.entries(localeOrMessages).forEach(\n        ([locale, messages2]) => this._load(locale, messages2)\n      );\n    }\n    this.emit(\"change\");\n  }\n  /**\n   * @param options {@link LoadAndActivateOptions}\n   */\n  loadAndActivate({ locale, locales, messages }) {\n    this._locale = locale;\n    this._locales = locales || void 0;\n    this._messages[this._locale] = messages;\n    this.emit(\"change\");\n  }\n  activate(locale, locales) {\n    if (true) {\n      if (!this._messages[locale]) {\n        console.warn(`Messages for locale \"${locale}\" not loaded.`);\n      }\n    }\n    this._locale = locale;\n    this._locales = locales;\n    this.emit(\"change\");\n  }\n  _(id, values, options) {\n    if (!this.locale) {\n      throw new Error(\n        \"Lingui: Attempted to call a translation function without setting a locale.\\nMake sure to call `i18n.activate(locale)` before using Lingui functions.\\nThis issue may also occur due to a race condition in your initialization logic.\"\n      );\n    }\n    let message = options?.message;\n    if (!id) {\n      id = \"\";\n    }\n    if (!isString(id)) {\n      values = id.values || values;\n      message = id.message;\n      id = id.id;\n    }\n    const messageForId = this.messages[id];\n    const messageMissing = messageForId === void 0;\n    const missing = this._missing;\n    if (missing && messageMissing) {\n      return isFunction(missing) ? missing(this._locale, id) : missing;\n    }\n    if (messageMissing) {\n      this.emit(\"missing\", { id, locale: this._locale });\n    }\n    let translation = messageForId || message || id;\n    if (isString(translation)) {\n      if (this._messageCompiler) {\n        translation = this._messageCompiler(translation);\n      } else {\n        console.warn(`Uncompiled message detected! Message:\n\n> ${translation}\n\nThat means you use raw catalog or your catalog doesn't have a translation for the message and fallback was used.\nICU features such as interpolation and plurals will not work properly for that message. \n\nPlease compile your catalog first. \n`);\n      }\n    }\n    if (isString(translation) && ESCAPE_SEQUENCE_REGEX.test(translation))\n      return decodeEscapeSequences(translation);\n    if (isString(translation))\n      return translation;\n    return interpolate(\n      translation,\n      this._locale,\n      this._locales\n    )(values, options?.formats);\n  }\n  date(value, format) {\n    return date(this._locales || this._locale, value, format);\n  }\n  number(value, format) {\n    return number(this._locales || this._locale, value, format);\n  }\n}\nfunction setupI18n(params = {}) {\n  return new I18n(params);\n}\n\nconst i18n = setupI18n();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BsaW5ndWkvY29yZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzRTs7QUFFdEU7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLHNCQUFzQjtBQUNqRTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsaUJBQWlCO0FBQzVEO0FBQ0E7QUFDQSwyQ0FBMkMsa0JBQWtCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLEtBQUssR0FBRyxVQUFVLEdBQUcsd0JBQXdCO0FBQ3pEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOENBQThDLEVBQUUsZ0JBQWdCLEVBQUU7QUFDbEU7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLEVBQUUsa0JBQWtCLEVBQUU7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsOERBQThEO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxhQUFhO0FBQzNCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxjQUFjLGFBQWE7QUFDM0I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0ZBQWtGLDZEQUE2RDtBQUMvSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDhFQUE4RSw2REFBNkQ7QUFDM0k7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLFFBQVEsSUFBcUM7QUFDN0MsK0JBQStCLGdGQUFjO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsaUJBQWlCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0Esb0JBQW9CLDJCQUEyQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLElBQXFDO0FBQzdDO0FBQ0EsNkNBQTZDLE9BQU87QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsMEJBQTBCO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7O0FBRUEsSUFBSTs7QUFFSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBOztBQUVBOztBQUUwQyIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL0BsaW5ndWkvY29yZS9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb21waWxlTWVzc2FnZSB9IGZyb20gJ0BsaW5ndWkvbWVzc2FnZS11dGlscy9jb21waWxlTWVzc2FnZSc7XG5cbmNvbnN0IGlzU3RyaW5nID0gKHMpID0+IHR5cGVvZiBzID09PSBcInN0cmluZ1wiO1xuY29uc3QgaXNGdW5jdGlvbiA9IChmKSA9PiB0eXBlb2YgZiA9PT0gXCJmdW5jdGlvblwiO1xuXG5jb25zdCBjYWNoZSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG5jb25zdCBkZWZhdWx0TG9jYWxlID0gXCJlblwiO1xuZnVuY3Rpb24gbm9ybWFsaXplTG9jYWxlcyhsb2NhbGVzKSB7XG4gIGNvbnN0IG91dCA9IEFycmF5LmlzQXJyYXkobG9jYWxlcykgPyBsb2NhbGVzIDogW2xvY2FsZXNdO1xuICByZXR1cm4gWy4uLm91dCwgZGVmYXVsdExvY2FsZV07XG59XG5mdW5jdGlvbiBkYXRlKGxvY2FsZXMsIHZhbHVlLCBmb3JtYXQpIHtcbiAgY29uc3QgX2xvY2FsZXMgPSBub3JtYWxpemVMb2NhbGVzKGxvY2FsZXMpO1xuICBpZiAoIWZvcm1hdCkge1xuICAgIGZvcm1hdCA9IFwiZGVmYXVsdFwiO1xuICB9XG4gIGxldCBvO1xuICBpZiAodHlwZW9mIGZvcm1hdCA9PT0gXCJzdHJpbmdcIikge1xuICAgIG8gPSB7XG4gICAgICBkYXk6IFwibnVtZXJpY1wiLFxuICAgICAgbW9udGg6IFwic2hvcnRcIixcbiAgICAgIHllYXI6IFwibnVtZXJpY1wiXG4gICAgfTtcbiAgICBzd2l0Y2ggKGZvcm1hdCkge1xuICAgICAgY2FzZSBcImZ1bGxcIjpcbiAgICAgICAgby53ZWVrZGF5ID0gXCJsb25nXCI7XG4gICAgICBjYXNlIFwibG9uZ1wiOlxuICAgICAgICBvLm1vbnRoID0gXCJsb25nXCI7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSBcInNob3J0XCI6XG4gICAgICAgIG8ubW9udGggPSBcIm51bWVyaWNcIjtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIG8gPSBmb3JtYXQ7XG4gIH1cbiAgY29uc3QgZm9ybWF0dGVyID0gZ2V0TWVtb2l6ZWQoXG4gICAgKCkgPT4gY2FjaGVLZXkoXCJkYXRlXCIsIF9sb2NhbGVzLCBmb3JtYXQpLFxuICAgICgpID0+IG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KF9sb2NhbGVzLCBvKVxuICApO1xuICByZXR1cm4gZm9ybWF0dGVyLmZvcm1hdChpc1N0cmluZyh2YWx1ZSkgPyBuZXcgRGF0ZSh2YWx1ZSkgOiB2YWx1ZSk7XG59XG5mdW5jdGlvbiB0aW1lKGxvY2FsZXMsIHZhbHVlLCBmb3JtYXQpIHtcbiAgbGV0IG87XG4gIGlmICghZm9ybWF0KSB7XG4gICAgZm9ybWF0ID0gXCJkZWZhdWx0XCI7XG4gIH1cbiAgaWYgKHR5cGVvZiBmb3JtYXQgPT09IFwic3RyaW5nXCIpIHtcbiAgICBvID0ge1xuICAgICAgc2Vjb25kOiBcIm51bWVyaWNcIixcbiAgICAgIG1pbnV0ZTogXCJudW1lcmljXCIsXG4gICAgICBob3VyOiBcIm51bWVyaWNcIlxuICAgIH07XG4gICAgc3dpdGNoIChmb3JtYXQpIHtcbiAgICAgIGNhc2UgXCJmdWxsXCI6XG4gICAgICBjYXNlIFwibG9uZ1wiOlxuICAgICAgICBvLnRpbWVab25lTmFtZSA9IFwic2hvcnRcIjtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlIFwic2hvcnRcIjpcbiAgICAgICAgZGVsZXRlIG8uc2Vjb25kO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBvID0gZm9ybWF0O1xuICB9XG4gIHJldHVybiBkYXRlKGxvY2FsZXMsIHZhbHVlLCBvKTtcbn1cbmZ1bmN0aW9uIG51bWJlcihsb2NhbGVzLCB2YWx1ZSwgZm9ybWF0KSB7XG4gIGNvbnN0IF9sb2NhbGVzID0gbm9ybWFsaXplTG9jYWxlcyhsb2NhbGVzKTtcbiAgY29uc3QgZm9ybWF0dGVyID0gZ2V0TWVtb2l6ZWQoXG4gICAgKCkgPT4gY2FjaGVLZXkoXCJudW1iZXJcIiwgX2xvY2FsZXMsIGZvcm1hdCksXG4gICAgKCkgPT4gbmV3IEludGwuTnVtYmVyRm9ybWF0KF9sb2NhbGVzLCBmb3JtYXQpXG4gICk7XG4gIHJldHVybiBmb3JtYXR0ZXIuZm9ybWF0KHZhbHVlKTtcbn1cbmZ1bmN0aW9uIHBsdXJhbChsb2NhbGVzLCBvcmRpbmFsLCB2YWx1ZSwgeyBvZmZzZXQgPSAwLCAuLi5ydWxlcyB9KSB7XG4gIGNvbnN0IF9sb2NhbGVzID0gbm9ybWFsaXplTG9jYWxlcyhsb2NhbGVzKTtcbiAgY29uc3QgcGx1cmFscyA9IG9yZGluYWwgPyBnZXRNZW1vaXplZChcbiAgICAoKSA9PiBjYWNoZUtleShcInBsdXJhbC1vcmRpbmFsXCIsIF9sb2NhbGVzKSxcbiAgICAoKSA9PiBuZXcgSW50bC5QbHVyYWxSdWxlcyhfbG9jYWxlcywgeyB0eXBlOiBcIm9yZGluYWxcIiB9KVxuICApIDogZ2V0TWVtb2l6ZWQoXG4gICAgKCkgPT4gY2FjaGVLZXkoXCJwbHVyYWwtY2FyZGluYWxcIiwgX2xvY2FsZXMpLFxuICAgICgpID0+IG5ldyBJbnRsLlBsdXJhbFJ1bGVzKF9sb2NhbGVzLCB7IHR5cGU6IFwiY2FyZGluYWxcIiB9KVxuICApO1xuICByZXR1cm4gcnVsZXNbdmFsdWVdID8/IHJ1bGVzW3BsdXJhbHMuc2VsZWN0KHZhbHVlIC0gb2Zmc2V0KV0gPz8gcnVsZXMub3RoZXI7XG59XG5mdW5jdGlvbiBnZXRNZW1vaXplZChnZXRLZXksIGNvbnN0cnVjdCkge1xuICBjb25zdCBrZXkgPSBnZXRLZXkoKTtcbiAgbGV0IGZvcm1hdHRlciA9IGNhY2hlLmdldChrZXkpO1xuICBpZiAoIWZvcm1hdHRlcikge1xuICAgIGZvcm1hdHRlciA9IGNvbnN0cnVjdCgpO1xuICAgIGNhY2hlLnNldChrZXksIGZvcm1hdHRlcik7XG4gIH1cbiAgcmV0dXJuIGZvcm1hdHRlcjtcbn1cbmZ1bmN0aW9uIGNhY2hlS2V5KHR5cGUsIGxvY2FsZXMsIG9wdGlvbnMpIHtcbiAgY29uc3QgbG9jYWxlS2V5ID0gbG9jYWxlcy5qb2luKFwiLVwiKTtcbiAgcmV0dXJuIGAke3R5cGV9LSR7bG9jYWxlS2V5fS0ke0pTT04uc3RyaW5naWZ5KG9wdGlvbnMpfWA7XG59XG5cbmNvbnN0IGZvcm1hdHMgPSB7XG4gIF9fcHJvdG9fXzogbnVsbCxcbiAgZGF0ZTogZGF0ZSxcbiAgZGVmYXVsdExvY2FsZTogZGVmYXVsdExvY2FsZSxcbiAgbnVtYmVyOiBudW1iZXIsXG4gIHBsdXJhbDogcGx1cmFsLFxuICB0aW1lOiB0aW1lXG59O1xuXG5jb25zdCBFU0NBUEVfU0VRVUVOQ0VfUkVHRVggPSAvXFxcXHVbYS1mQS1GMC05XXs0fXxcXFxceFthLWZBLUYwLTldezJ9LztcbmNvbnN0IGRlY29kZUVzY2FwZVNlcXVlbmNlcyA9IChzdHIpID0+IHtcbiAgcmV0dXJuIHN0ci5yZXBsYWNlKFxuICAgIC8vIFNhbWUgcGF0dGVybiBidXQgd2l0aCBjYXB0dXJpbmcgZ3JvdXBzIGZvciBleHRyYWN0aW5nIHZhbHVlcyBkdXJpbmcgcmVwbGFjZW1lbnRcbiAgICAvXFxcXHUoW2EtZkEtRjAtOV17NH0pfFxcXFx4KFthLWZBLUYwLTldezJ9KS9nLFxuICAgIChfLCB1bmljb2RlLCBoZXgpID0+IHtcbiAgICAgIGlmICh1bmljb2RlKSB7XG4gICAgICAgIGNvbnN0IGNvZGVQb2ludCA9IHBhcnNlSW50KHVuaWNvZGUsIDE2KTtcbiAgICAgICAgcmV0dXJuIFN0cmluZy5mcm9tQ2hhckNvZGUoY29kZVBvaW50KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGNvZGVQb2ludCA9IHBhcnNlSW50KGhleCwgMTYpO1xuICAgICAgICByZXR1cm4gU3RyaW5nLmZyb21DaGFyQ29kZShjb2RlUG9pbnQpO1xuICAgICAgfVxuICAgIH1cbiAgKTtcbn07XG5cbmNvbnN0IE9DVE9USE9SUEVfUEggPSBcIiVfX2xpbmd1aV9vY3RvdGhvcnBlX18lXCI7XG5jb25zdCBnZXREZWZhdWx0Rm9ybWF0cyA9IChsb2NhbGUsIHBhc3NlZExvY2FsZXMsIGZvcm1hdHMgPSB7fSkgPT4ge1xuICBjb25zdCBsb2NhbGVzID0gcGFzc2VkTG9jYWxlcyB8fCBsb2NhbGU7XG4gIGNvbnN0IHN0eWxlID0gKGZvcm1hdCkgPT4ge1xuICAgIGlmICh0eXBlb2YgZm9ybWF0ID09PSBcIm9iamVjdFwiKVxuICAgICAgcmV0dXJuIGZvcm1hdDtcbiAgICByZXR1cm4gZm9ybWF0c1tmb3JtYXRdO1xuICB9O1xuICBjb25zdCByZXBsYWNlT2N0b3Rob3JwZSA9ICh2YWx1ZSwgbWVzc2FnZSkgPT4ge1xuICAgIGNvbnN0IG51bWJlckZvcm1hdCA9IE9iamVjdC5rZXlzKGZvcm1hdHMpLmxlbmd0aCA/IHN0eWxlKFwibnVtYmVyXCIpIDogdm9pZCAwO1xuICAgIGNvbnN0IHZhbHVlU3RyID0gbnVtYmVyKGxvY2FsZXMsIHZhbHVlLCBudW1iZXJGb3JtYXQpO1xuICAgIHJldHVybiBtZXNzYWdlLnJlcGxhY2UobmV3IFJlZ0V4cChPQ1RPVEhPUlBFX1BILCBcImdcIiksIHZhbHVlU3RyKTtcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBwbHVyYWw6ICh2YWx1ZSwgY2FzZXMpID0+IHtcbiAgICAgIGNvbnN0IHsgb2Zmc2V0ID0gMCB9ID0gY2FzZXM7XG4gICAgICBjb25zdCBtZXNzYWdlID0gcGx1cmFsKGxvY2FsZXMsIGZhbHNlLCB2YWx1ZSwgY2FzZXMpO1xuICAgICAgcmV0dXJuIHJlcGxhY2VPY3RvdGhvcnBlKHZhbHVlIC0gb2Zmc2V0LCBtZXNzYWdlKTtcbiAgICB9LFxuICAgIHNlbGVjdG9yZGluYWw6ICh2YWx1ZSwgY2FzZXMpID0+IHtcbiAgICAgIGNvbnN0IHsgb2Zmc2V0ID0gMCB9ID0gY2FzZXM7XG4gICAgICBjb25zdCBtZXNzYWdlID0gcGx1cmFsKGxvY2FsZXMsIHRydWUsIHZhbHVlLCBjYXNlcyk7XG4gICAgICByZXR1cm4gcmVwbGFjZU9jdG90aG9ycGUodmFsdWUgLSBvZmZzZXQsIG1lc3NhZ2UpO1xuICAgIH0sXG4gICAgc2VsZWN0OiBzZWxlY3RGb3JtYXR0ZXIsXG4gICAgbnVtYmVyOiAodmFsdWUsIGZvcm1hdCkgPT4gbnVtYmVyKFxuICAgICAgbG9jYWxlcyxcbiAgICAgIHZhbHVlLFxuICAgICAgc3R5bGUoZm9ybWF0KSB8fCB7IHN0eWxlOiBmb3JtYXQgfVxuICAgICksXG4gICAgZGF0ZTogKHZhbHVlLCBmb3JtYXQpID0+IGRhdGUobG9jYWxlcywgdmFsdWUsIHN0eWxlKGZvcm1hdCkgfHwgZm9ybWF0KSxcbiAgICB0aW1lOiAodmFsdWUsIGZvcm1hdCkgPT4gdGltZShsb2NhbGVzLCB2YWx1ZSwgc3R5bGUoZm9ybWF0KSB8fCBmb3JtYXQpXG4gIH07XG59O1xuY29uc3Qgc2VsZWN0Rm9ybWF0dGVyID0gKHZhbHVlLCBydWxlcykgPT4gcnVsZXNbdmFsdWVdID8/IHJ1bGVzLm90aGVyO1xuZnVuY3Rpb24gaW50ZXJwb2xhdGUodHJhbnNsYXRpb24sIGxvY2FsZSwgbG9jYWxlcykge1xuICByZXR1cm4gKHZhbHVlcyA9IHt9LCBmb3JtYXRzKSA9PiB7XG4gICAgY29uc3QgZm9ybWF0dGVycyA9IGdldERlZmF1bHRGb3JtYXRzKGxvY2FsZSwgbG9jYWxlcywgZm9ybWF0cyk7XG4gICAgY29uc3QgZm9ybWF0TWVzc2FnZSA9ICh0b2tlbnMsIHJlcGxhY2VPY3RvdGhvcnBlID0gZmFsc2UpID0+IHtcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheSh0b2tlbnMpKVxuICAgICAgICByZXR1cm4gdG9rZW5zO1xuICAgICAgcmV0dXJuIHRva2Vucy5yZWR1Y2UoKG1lc3NhZ2UsIHRva2VuKSA9PiB7XG4gICAgICAgIGlmICh0b2tlbiA9PT0gXCIjXCIgJiYgcmVwbGFjZU9jdG90aG9ycGUpIHtcbiAgICAgICAgICByZXR1cm4gbWVzc2FnZSArIE9DVE9USE9SUEVfUEg7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlzU3RyaW5nKHRva2VuKSkge1xuICAgICAgICAgIHJldHVybiBtZXNzYWdlICsgdG9rZW47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgW25hbWUsIHR5cGUsIGZvcm1hdF0gPSB0b2tlbjtcbiAgICAgICAgbGV0IGludGVycG9sYXRlZEZvcm1hdCA9IHt9O1xuICAgICAgICBpZiAodHlwZSA9PT0gXCJwbHVyYWxcIiB8fCB0eXBlID09PSBcInNlbGVjdG9yZGluYWxcIiB8fCB0eXBlID09PSBcInNlbGVjdFwiKSB7XG4gICAgICAgICAgT2JqZWN0LmVudHJpZXMoZm9ybWF0KS5mb3JFYWNoKFxuICAgICAgICAgICAgKFtrZXksIHZhbHVlMl0pID0+IHtcbiAgICAgICAgICAgICAgaW50ZXJwb2xhdGVkRm9ybWF0W2tleV0gPSBmb3JtYXRNZXNzYWdlKFxuICAgICAgICAgICAgICAgIHZhbHVlMixcbiAgICAgICAgICAgICAgICB0eXBlID09PSBcInBsdXJhbFwiIHx8IHR5cGUgPT09IFwic2VsZWN0b3JkaW5hbFwiXG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBpbnRlcnBvbGF0ZWRGb3JtYXQgPSBmb3JtYXQ7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IHZhbHVlO1xuICAgICAgICBpZiAodHlwZSkge1xuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlciA9IGZvcm1hdHRlcnNbdHlwZV07XG4gICAgICAgICAgdmFsdWUgPSBmb3JtYXR0ZXIodmFsdWVzW25hbWVdLCBpbnRlcnBvbGF0ZWRGb3JtYXQpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHZhbHVlID0gdmFsdWVzW25hbWVdO1xuICAgICAgICB9XG4gICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICAgICAgcmV0dXJuIG1lc3NhZ2U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1lc3NhZ2UgKyB2YWx1ZTtcbiAgICAgIH0sIFwiXCIpO1xuICAgIH07XG4gICAgY29uc3QgcmVzdWx0ID0gZm9ybWF0TWVzc2FnZSh0cmFuc2xhdGlvbik7XG4gICAgaWYgKGlzU3RyaW5nKHJlc3VsdCkgJiYgRVNDQVBFX1NFUVVFTkNFX1JFR0VYLnRlc3QocmVzdWx0KSkge1xuICAgICAgcmV0dXJuIGRlY29kZUVzY2FwZVNlcXVlbmNlcyhyZXN1bHQpO1xuICAgIH1cbiAgICBpZiAoaXNTdHJpbmcocmVzdWx0KSlcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgcmV0dXJuIHJlc3VsdCA/IFN0cmluZyhyZXN1bHQpIDogXCJcIjtcbiAgfTtcbn1cblxudmFyIF9fZGVmUHJvcCQxID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZGVmTm9ybWFsUHJvcCQxID0gKG9iaiwga2V5LCB2YWx1ZSkgPT4ga2V5IGluIG9iaiA/IF9fZGVmUHJvcCQxKG9iaiwga2V5LCB7IGVudW1lcmFibGU6IHRydWUsIGNvbmZpZ3VyYWJsZTogdHJ1ZSwgd3JpdGFibGU6IHRydWUsIHZhbHVlIH0pIDogb2JqW2tleV0gPSB2YWx1ZTtcbnZhciBfX3B1YmxpY0ZpZWxkJDEgPSAob2JqLCBrZXksIHZhbHVlKSA9PiB7XG4gIF9fZGVmTm9ybWFsUHJvcCQxKG9iaiwgdHlwZW9mIGtleSAhPT0gXCJzeW1ib2xcIiA/IGtleSArIFwiXCIgOiBrZXksIHZhbHVlKTtcbiAgcmV0dXJuIHZhbHVlO1xufTtcbmNsYXNzIEV2ZW50RW1pdHRlciB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIF9fcHVibGljRmllbGQkMSh0aGlzLCBcIl9ldmVudHNcIiwge30pO1xuICB9XG4gIG9uKGV2ZW50LCBsaXN0ZW5lcikge1xuICAgIHZhciBfYTtcbiAgICAoX2EgPSB0aGlzLl9ldmVudHMpW2V2ZW50XSA/PyAoX2FbZXZlbnRdID0gW10pO1xuICAgIHRoaXMuX2V2ZW50c1tldmVudF0ucHVzaChsaXN0ZW5lcik7XG4gICAgcmV0dXJuICgpID0+IHRoaXMucmVtb3ZlTGlzdGVuZXIoZXZlbnQsIGxpc3RlbmVyKTtcbiAgfVxuICByZW1vdmVMaXN0ZW5lcihldmVudCwgbGlzdGVuZXIpIHtcbiAgICBjb25zdCBtYXliZUxpc3RlbmVycyA9IHRoaXMuX2dldExpc3RlbmVycyhldmVudCk7XG4gICAgaWYgKCFtYXliZUxpc3RlbmVycylcbiAgICAgIHJldHVybjtcbiAgICBjb25zdCBpbmRleCA9IG1heWJlTGlzdGVuZXJzLmluZGV4T2YobGlzdGVuZXIpO1xuICAgIGlmICh+aW5kZXgpXG4gICAgICBtYXliZUxpc3RlbmVycy5zcGxpY2UoaW5kZXgsIDEpO1xuICB9XG4gIGVtaXQoZXZlbnQsIC4uLmFyZ3MpIHtcbiAgICBjb25zdCBtYXliZUxpc3RlbmVycyA9IHRoaXMuX2dldExpc3RlbmVycyhldmVudCk7XG4gICAgaWYgKCFtYXliZUxpc3RlbmVycylcbiAgICAgIHJldHVybjtcbiAgICBtYXliZUxpc3RlbmVycy5tYXAoKGxpc3RlbmVyKSA9PiBsaXN0ZW5lci5hcHBseSh0aGlzLCBhcmdzKSk7XG4gIH1cbiAgX2dldExpc3RlbmVycyhldmVudCkge1xuICAgIGNvbnN0IG1heWJlTGlzdGVuZXJzID0gdGhpcy5fZXZlbnRzW2V2ZW50XTtcbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheShtYXliZUxpc3RlbmVycykgPyBtYXliZUxpc3RlbmVycyA6IGZhbHNlO1xuICB9XG59XG5cbnZhciBfX2RlZlByb3AgPSBPYmplY3QuZGVmaW5lUHJvcGVydHk7XG52YXIgX19kZWZOb3JtYWxQcm9wID0gKG9iaiwga2V5LCB2YWx1ZSkgPT4ga2V5IGluIG9iaiA/IF9fZGVmUHJvcChvYmosIGtleSwgeyBlbnVtZXJhYmxlOiB0cnVlLCBjb25maWd1cmFibGU6IHRydWUsIHdyaXRhYmxlOiB0cnVlLCB2YWx1ZSB9KSA6IG9ialtrZXldID0gdmFsdWU7XG52YXIgX19wdWJsaWNGaWVsZCA9IChvYmosIGtleSwgdmFsdWUpID0+IHtcbiAgX19kZWZOb3JtYWxQcm9wKG9iaiwgdHlwZW9mIGtleSAhPT0gXCJzeW1ib2xcIiA/IGtleSArIFwiXCIgOiBrZXksIHZhbHVlKTtcbiAgcmV0dXJuIHZhbHVlO1xufTtcbmNsYXNzIEkxOG4gZXh0ZW5kcyBFdmVudEVtaXR0ZXIge1xuICBjb25zdHJ1Y3RvcihwYXJhbXMpIHtcbiAgICBzdXBlcigpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJfbG9jYWxlXCIsIFwiXCIpO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJfbG9jYWxlc1wiKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiX2xvY2FsZURhdGFcIiwge30pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJfbWVzc2FnZXNcIiwge30pO1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJfbWlzc2luZ1wiKTtcbiAgICBfX3B1YmxpY0ZpZWxkKHRoaXMsIFwiX21lc3NhZ2VDb21waWxlclwiKTtcbiAgICAvKipcbiAgICAgKiBBbGlhcyBmb3Ige0BzZWUgSTE4bi5ffVxuICAgICAqL1xuICAgIF9fcHVibGljRmllbGQodGhpcywgXCJ0XCIsIHRoaXMuXy5iaW5kKHRoaXMpKTtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICB0aGlzLnNldE1lc3NhZ2VzQ29tcGlsZXIoY29tcGlsZU1lc3NhZ2UpO1xuICAgIH1cbiAgICBpZiAocGFyYW1zLm1pc3NpbmcgIT0gbnVsbClcbiAgICAgIHRoaXMuX21pc3NpbmcgPSBwYXJhbXMubWlzc2luZztcbiAgICBpZiAocGFyYW1zLm1lc3NhZ2VzICE9IG51bGwpXG4gICAgICB0aGlzLmxvYWQocGFyYW1zLm1lc3NhZ2VzKTtcbiAgICBpZiAocGFyYW1zLmxvY2FsZURhdGEgIT0gbnVsbClcbiAgICAgIHRoaXMubG9hZExvY2FsZURhdGEocGFyYW1zLmxvY2FsZURhdGEpO1xuICAgIGlmICh0eXBlb2YgcGFyYW1zLmxvY2FsZSA9PT0gXCJzdHJpbmdcIiB8fCBwYXJhbXMubG9jYWxlcykge1xuICAgICAgdGhpcy5hY3RpdmF0ZShwYXJhbXMubG9jYWxlID8/IGRlZmF1bHRMb2NhbGUsIHBhcmFtcy5sb2NhbGVzKTtcbiAgICB9XG4gIH1cbiAgZ2V0IGxvY2FsZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fbG9jYWxlO1xuICB9XG4gIGdldCBsb2NhbGVzKCkge1xuICAgIHJldHVybiB0aGlzLl9sb2NhbGVzO1xuICB9XG4gIGdldCBtZXNzYWdlcygpIHtcbiAgICByZXR1cm4gdGhpcy5fbWVzc2FnZXNbdGhpcy5fbG9jYWxlXSA/PyB7fTtcbiAgfVxuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgdGhpcyBoYXMgbm8gZWZmZWN0LiBQbGVhc2UgcmVtb3ZlIHRoaXMgZnJvbSB0aGUgY29kZS4gRGVwcmVjYXRlZCBpbiB2NFxuICAgKi9cbiAgZ2V0IGxvY2FsZURhdGEoKSB7XG4gICAgcmV0dXJuIHRoaXMuX2xvY2FsZURhdGFbdGhpcy5fbG9jYWxlXSA/PyB7fTtcbiAgfVxuICBfbG9hZExvY2FsZURhdGEobG9jYWxlLCBsb2NhbGVEYXRhKSB7XG4gICAgY29uc3QgbWF5YmVMb2NhbGVEYXRhID0gdGhpcy5fbG9jYWxlRGF0YVtsb2NhbGVdO1xuICAgIGlmICghbWF5YmVMb2NhbGVEYXRhKSB7XG4gICAgICB0aGlzLl9sb2NhbGVEYXRhW2xvY2FsZV0gPSBsb2NhbGVEYXRhO1xuICAgIH0gZWxzZSB7XG4gICAgICBPYmplY3QuYXNzaWduKG1heWJlTG9jYWxlRGF0YSwgbG9jYWxlRGF0YSk7XG4gICAgfVxuICB9XG4gIC8qKlxuICAgKiBSZWdpc3RlcnMgYSBgTWVzc2FnZUNvbXBpbGVyYCB0byBlbmFibGUgdGhlIHVzZSBvZiB1bmNvbXBpbGVkIGNhdGFsb2dzIGF0IHJ1bnRpbWUuXG4gICAqXG4gICAqIEluIHByb2R1Y3Rpb24gYnVpbGRzLCB0aGUgYE1lc3NhZ2VDb21waWxlcmAgaXMgdHlwaWNhbGx5IGV4Y2x1ZGVkIHRvIHJlZHVjZSBidW5kbGUgc2l6ZS5cbiAgICogQnkgZGVmYXVsdCwgbWVzc2FnZSBjYXRhbG9ncyBzaG91bGQgYmUgcHJlY29tcGlsZWQgZHVyaW5nIHRoZSBidWlsZCBwcm9jZXNzLiBIb3dldmVyLFxuICAgKiBpZiB5b3UgbmVlZCB0byBjb21waWxlIGNhdGFsb2dzIGF0IHJ1bnRpbWUsIHlvdSBjYW4gdXNlIHRoaXMgbWV0aG9kIHRvIHNldCBhIG1lc3NhZ2UgY29tcGlsZXIuXG4gICAqXG4gICAqIEV4YW1wbGUgdXNhZ2U6XG4gICAqXG4gICAqIGBgYHRzXG4gICAqIGltcG9ydCB7IGNvbXBpbGVNZXNzYWdlIH0gZnJvbSBcIkBsaW5ndWkvbWVzc2FnZS11dGlscy9jb21waWxlTWVzc2FnZVwiO1xuICAgKlxuICAgKiBpMThuLnNldE1lc3NhZ2VzQ29tcGlsZXIoY29tcGlsZU1lc3NhZ2UpO1xuICAgKiBgYGBcbiAgICovXG4gIHNldE1lc3NhZ2VzQ29tcGlsZXIoY29tcGlsZXIpIHtcbiAgICB0aGlzLl9tZXNzYWdlQ29tcGlsZXIgPSBjb21waWxlcjtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuICAvKipcbiAgICogQGRlcHJlY2F0ZWQgUGx1cmFscyBhdXRvbWF0aWNhbGx5IHVzZWQgZnJvbSBJbnRsLlBsdXJhbFJ1bGVzIHlvdSBjYW4gc2FmZWx5IHJlbW92ZSB0aGlzIGNhbGwuIERlcHJlY2F0ZWQgaW4gdjRcbiAgICovXG4gIGxvYWRMb2NhbGVEYXRhKGxvY2FsZU9yQWxsRGF0YSwgbG9jYWxlRGF0YSkge1xuICAgIGlmICh0eXBlb2YgbG9jYWxlT3JBbGxEYXRhID09PSBcInN0cmluZ1wiKSB7XG4gICAgICB0aGlzLl9sb2FkTG9jYWxlRGF0YShsb2NhbGVPckFsbERhdGEsIGxvY2FsZURhdGEpO1xuICAgIH0gZWxzZSB7XG4gICAgICBPYmplY3Qua2V5cyhsb2NhbGVPckFsbERhdGEpLmZvckVhY2goXG4gICAgICAgIChsb2NhbGUpID0+IHRoaXMuX2xvYWRMb2NhbGVEYXRhKGxvY2FsZSwgbG9jYWxlT3JBbGxEYXRhW2xvY2FsZV0pXG4gICAgICApO1xuICAgIH1cbiAgICB0aGlzLmVtaXQoXCJjaGFuZ2VcIik7XG4gIH1cbiAgX2xvYWQobG9jYWxlLCBtZXNzYWdlcykge1xuICAgIGNvbnN0IG1heWJlTWVzc2FnZXMgPSB0aGlzLl9tZXNzYWdlc1tsb2NhbGVdO1xuICAgIGlmICghbWF5YmVNZXNzYWdlcykge1xuICAgICAgdGhpcy5fbWVzc2FnZXNbbG9jYWxlXSA9IG1lc3NhZ2VzO1xuICAgIH0gZWxzZSB7XG4gICAgICBPYmplY3QuYXNzaWduKG1heWJlTWVzc2FnZXMsIG1lc3NhZ2VzKTtcbiAgICB9XG4gIH1cbiAgbG9hZChsb2NhbGVPck1lc3NhZ2VzLCBtZXNzYWdlcykge1xuICAgIGlmICh0eXBlb2YgbG9jYWxlT3JNZXNzYWdlcyA9PSBcInN0cmluZ1wiICYmIHR5cGVvZiBtZXNzYWdlcyA9PT0gXCJvYmplY3RcIikge1xuICAgICAgdGhpcy5fbG9hZChsb2NhbGVPck1lc3NhZ2VzLCBtZXNzYWdlcyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIE9iamVjdC5lbnRyaWVzKGxvY2FsZU9yTWVzc2FnZXMpLmZvckVhY2goXG4gICAgICAgIChbbG9jYWxlLCBtZXNzYWdlczJdKSA9PiB0aGlzLl9sb2FkKGxvY2FsZSwgbWVzc2FnZXMyKVxuICAgICAgKTtcbiAgICB9XG4gICAgdGhpcy5lbWl0KFwiY2hhbmdlXCIpO1xuICB9XG4gIC8qKlxuICAgKiBAcGFyYW0gb3B0aW9ucyB7QGxpbmsgTG9hZEFuZEFjdGl2YXRlT3B0aW9uc31cbiAgICovXG4gIGxvYWRBbmRBY3RpdmF0ZSh7IGxvY2FsZSwgbG9jYWxlcywgbWVzc2FnZXMgfSkge1xuICAgIHRoaXMuX2xvY2FsZSA9IGxvY2FsZTtcbiAgICB0aGlzLl9sb2NhbGVzID0gbG9jYWxlcyB8fCB2b2lkIDA7XG4gICAgdGhpcy5fbWVzc2FnZXNbdGhpcy5fbG9jYWxlXSA9IG1lc3NhZ2VzO1xuICAgIHRoaXMuZW1pdChcImNoYW5nZVwiKTtcbiAgfVxuICBhY3RpdmF0ZShsb2NhbGUsIGxvY2FsZXMpIHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICBpZiAoIXRoaXMuX21lc3NhZ2VzW2xvY2FsZV0pIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBNZXNzYWdlcyBmb3IgbG9jYWxlIFwiJHtsb2NhbGV9XCIgbm90IGxvYWRlZC5gKTtcbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5fbG9jYWxlID0gbG9jYWxlO1xuICAgIHRoaXMuX2xvY2FsZXMgPSBsb2NhbGVzO1xuICAgIHRoaXMuZW1pdChcImNoYW5nZVwiKTtcbiAgfVxuICBfKGlkLCB2YWx1ZXMsIG9wdGlvbnMpIHtcbiAgICBpZiAoIXRoaXMubG9jYWxlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiTGluZ3VpOiBBdHRlbXB0ZWQgdG8gY2FsbCBhIHRyYW5zbGF0aW9uIGZ1bmN0aW9uIHdpdGhvdXQgc2V0dGluZyBhIGxvY2FsZS5cXG5NYWtlIHN1cmUgdG8gY2FsbCBgaTE4bi5hY3RpdmF0ZShsb2NhbGUpYCBiZWZvcmUgdXNpbmcgTGluZ3VpIGZ1bmN0aW9ucy5cXG5UaGlzIGlzc3VlIG1heSBhbHNvIG9jY3VyIGR1ZSB0byBhIHJhY2UgY29uZGl0aW9uIGluIHlvdXIgaW5pdGlhbGl6YXRpb24gbG9naWMuXCJcbiAgICAgICk7XG4gICAgfVxuICAgIGxldCBtZXNzYWdlID0gb3B0aW9ucz8ubWVzc2FnZTtcbiAgICBpZiAoIWlkKSB7XG4gICAgICBpZCA9IFwiXCI7XG4gICAgfVxuICAgIGlmICghaXNTdHJpbmcoaWQpKSB7XG4gICAgICB2YWx1ZXMgPSBpZC52YWx1ZXMgfHwgdmFsdWVzO1xuICAgICAgbWVzc2FnZSA9IGlkLm1lc3NhZ2U7XG4gICAgICBpZCA9IGlkLmlkO1xuICAgIH1cbiAgICBjb25zdCBtZXNzYWdlRm9ySWQgPSB0aGlzLm1lc3NhZ2VzW2lkXTtcbiAgICBjb25zdCBtZXNzYWdlTWlzc2luZyA9IG1lc3NhZ2VGb3JJZCA9PT0gdm9pZCAwO1xuICAgIGNvbnN0IG1pc3NpbmcgPSB0aGlzLl9taXNzaW5nO1xuICAgIGlmIChtaXNzaW5nICYmIG1lc3NhZ2VNaXNzaW5nKSB7XG4gICAgICByZXR1cm4gaXNGdW5jdGlvbihtaXNzaW5nKSA/IG1pc3NpbmcodGhpcy5fbG9jYWxlLCBpZCkgOiBtaXNzaW5nO1xuICAgIH1cbiAgICBpZiAobWVzc2FnZU1pc3NpbmcpIHtcbiAgICAgIHRoaXMuZW1pdChcIm1pc3NpbmdcIiwgeyBpZCwgbG9jYWxlOiB0aGlzLl9sb2NhbGUgfSk7XG4gICAgfVxuICAgIGxldCB0cmFuc2xhdGlvbiA9IG1lc3NhZ2VGb3JJZCB8fCBtZXNzYWdlIHx8IGlkO1xuICAgIGlmIChpc1N0cmluZyh0cmFuc2xhdGlvbikpIHtcbiAgICAgIGlmICh0aGlzLl9tZXNzYWdlQ29tcGlsZXIpIHtcbiAgICAgICAgdHJhbnNsYXRpb24gPSB0aGlzLl9tZXNzYWdlQ29tcGlsZXIodHJhbnNsYXRpb24pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBVbmNvbXBpbGVkIG1lc3NhZ2UgZGV0ZWN0ZWQhIE1lc3NhZ2U6XG5cbj4gJHt0cmFuc2xhdGlvbn1cblxuVGhhdCBtZWFucyB5b3UgdXNlIHJhdyBjYXRhbG9nIG9yIHlvdXIgY2F0YWxvZyBkb2Vzbid0IGhhdmUgYSB0cmFuc2xhdGlvbiBmb3IgdGhlIG1lc3NhZ2UgYW5kIGZhbGxiYWNrIHdhcyB1c2VkLlxuSUNVIGZlYXR1cmVzIHN1Y2ggYXMgaW50ZXJwb2xhdGlvbiBhbmQgcGx1cmFscyB3aWxsIG5vdCB3b3JrIHByb3Blcmx5IGZvciB0aGF0IG1lc3NhZ2UuIFxuXG5QbGVhc2UgY29tcGlsZSB5b3VyIGNhdGFsb2cgZmlyc3QuIFxuYCk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChpc1N0cmluZyh0cmFuc2xhdGlvbikgJiYgRVNDQVBFX1NFUVVFTkNFX1JFR0VYLnRlc3QodHJhbnNsYXRpb24pKVxuICAgICAgcmV0dXJuIGRlY29kZUVzY2FwZVNlcXVlbmNlcyh0cmFuc2xhdGlvbik7XG4gICAgaWYgKGlzU3RyaW5nKHRyYW5zbGF0aW9uKSlcbiAgICAgIHJldHVybiB0cmFuc2xhdGlvbjtcbiAgICByZXR1cm4gaW50ZXJwb2xhdGUoXG4gICAgICB0cmFuc2xhdGlvbixcbiAgICAgIHRoaXMuX2xvY2FsZSxcbiAgICAgIHRoaXMuX2xvY2FsZXNcbiAgICApKHZhbHVlcywgb3B0aW9ucz8uZm9ybWF0cyk7XG4gIH1cbiAgZGF0ZSh2YWx1ZSwgZm9ybWF0KSB7XG4gICAgcmV0dXJuIGRhdGUodGhpcy5fbG9jYWxlcyB8fCB0aGlzLl9sb2NhbGUsIHZhbHVlLCBmb3JtYXQpO1xuICB9XG4gIG51bWJlcih2YWx1ZSwgZm9ybWF0KSB7XG4gICAgcmV0dXJuIG51bWJlcih0aGlzLl9sb2NhbGVzIHx8IHRoaXMuX2xvY2FsZSwgdmFsdWUsIGZvcm1hdCk7XG4gIH1cbn1cbmZ1bmN0aW9uIHNldHVwSTE4bihwYXJhbXMgPSB7fSkge1xuICByZXR1cm4gbmV3IEkxOG4ocGFyYW1zKTtcbn1cblxuY29uc3QgaTE4biA9IHNldHVwSTE4bigpO1xuXG5leHBvcnQgeyBJMThuLCBmb3JtYXRzLCBpMThuLCBzZXR1cEkxOG4gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@lingui/core/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs":
/*!************************************************************************!*\
  !*** ../../node_modules/@lingui/message-utils/dist/compileMessage.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compileMessage: () => (/* binding */ compileMessage),\n/* harmony export */   compileMessageOrThrow: () => (/* binding */ compileMessageOrThrow)\n/* harmony export */ });\n/* harmony import */ var _messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @messageformat/parser */ \"(rsc)/../../node_modules/@messageformat/parser/lib/parser.js\");\n\n\n/**\n * Parent class for errors.\n *\n * @remarks\n * Errors with `type: \"warning\"` do not necessarily indicate that the parser\n * encountered an error. In addition to a human-friendly `message`, may also\n * includes the `token` at which the error was encountered.\n *\n * @public\n */\nclass DateFormatError extends Error {\n    /** @internal */\n    constructor(msg, token, type) {\n        super(msg);\n        this.token = token;\n        this.type = type || 'error';\n    }\n}\nconst alpha = (width) => width < 4 ? 'short' : width === 4 ? 'long' : 'narrow';\nconst numeric = (width) => (width % 2 === 0 ? '2-digit' : 'numeric');\nfunction yearOptions(token, onError) {\n    switch (token.char) {\n        case 'y':\n            return { year: numeric(token.width) };\n        case 'r':\n            return { calendar: 'gregory', year: 'numeric' };\n        case 'u':\n        case 'U':\n        case 'Y':\n        default:\n            onError(`${token.desc} is not supported; falling back to year:numeric`, DateFormatError.WARNING);\n            return { year: 'numeric' };\n    }\n}\nfunction monthStyle(token, onError) {\n    switch (token.width) {\n        case 1:\n            return 'numeric';\n        case 2:\n            return '2-digit';\n        case 3:\n            return 'short';\n        case 4:\n            return 'long';\n        case 5:\n            return 'narrow';\n        default:\n            onError(`${token.desc} is not supported with width ${token.width}`);\n            return undefined;\n    }\n}\nfunction dayStyle(token, onError) {\n    const { char, desc, width } = token;\n    if (char === 'd') {\n        return numeric(width);\n    }\n    else {\n        onError(`${desc} is not supported`);\n        return undefined;\n    }\n}\nfunction weekdayStyle(token, onError) {\n    const { char, desc, width } = token;\n    if ((char === 'c' || char === 'e') && width < 3) {\n        // ignoring stand-alone-ness\n        const msg = `Numeric value is not supported for ${desc}; falling back to weekday:short`;\n        onError(msg, DateFormatError.WARNING);\n    }\n    // merging narrow styles\n    return alpha(width);\n}\nfunction hourOptions(token) {\n    const hour = numeric(token.width);\n    let hourCycle;\n    switch (token.char) {\n        case 'h':\n            hourCycle = 'h12';\n            break;\n        case 'H':\n            hourCycle = 'h23';\n            break;\n        case 'k':\n            hourCycle = 'h24';\n            break;\n        case 'K':\n            hourCycle = 'h11';\n            break;\n    }\n    return hourCycle ? { hour, hourCycle } : { hour };\n}\nfunction timeZoneNameStyle(token, onError) {\n    // so much fallback behaviour here\n    const { char, desc, width } = token;\n    switch (char) {\n        case 'v':\n        case 'z':\n            return width === 4 ? 'long' : 'short';\n        case 'V':\n            if (width === 4)\n                return 'long';\n            onError(`${desc} is not supported with width ${width}`);\n            return undefined;\n        case 'X':\n            onError(`${desc} is not supported`);\n            return undefined;\n    }\n    return 'short';\n}\nfunction compileOptions(token, onError) {\n    switch (token.field) {\n        case 'era':\n            return { era: alpha(token.width) };\n        case 'year':\n            return yearOptions(token, onError);\n        case 'month':\n            return { month: monthStyle(token, onError) };\n        case 'day':\n            return { day: dayStyle(token, onError) };\n        case 'weekday':\n            return { weekday: weekdayStyle(token, onError) };\n        case 'period':\n            return undefined;\n        case 'hour':\n            return hourOptions(token);\n        case 'min':\n            return { minute: numeric(token.width) };\n        case 'sec':\n            return { second: numeric(token.width) };\n        case 'tz':\n            return { timeZoneName: timeZoneNameStyle(token, onError) };\n        case 'quarter':\n        case 'week':\n        case 'sec-frac':\n        case 'ms':\n            onError(`${token.desc} is not supported`);\n    }\n    return undefined;\n}\nfunction getDateFormatOptions(tokens, timeZone, onError = error => {\n    throw error;\n}) {\n    const options = {\n        timeZone\n    };\n    const fields = [];\n    for (const token of tokens) {\n        const { error, field, str } = token;\n        if (error) {\n            const dte = new DateFormatError(error.message, token);\n            dte.stack = error.stack;\n            onError(dte);\n        }\n        if (str) {\n            const msg = `Ignoring string part: ${str}`;\n            onError(new DateFormatError(msg, token, DateFormatError.WARNING));\n        }\n        if (field) {\n            if (fields.indexOf(field) === -1)\n                fields.push(field);\n            else\n                onError(new DateFormatError(`Duplicate ${field} token`, token));\n        }\n        const opt = compileOptions(token, (msg, isWarning) => onError(new DateFormatError(msg, token, isWarning)));\n        if (opt)\n            Object.assign(options, opt);\n    }\n    return options;\n}\n\nconst fields = {\n    G: { field: 'era', desc: 'Era' },\n    y: { field: 'year', desc: 'Year' },\n    Y: { field: 'year', desc: 'Year of \"Week of Year\"' },\n    u: { field: 'year', desc: 'Extended year' },\n    U: { field: 'year', desc: 'Cyclic year name' },\n    r: { field: 'year', desc: 'Related Gregorian year' },\n    Q: { field: 'quarter', desc: 'Quarter' },\n    q: { field: 'quarter', desc: 'Stand-alone quarter' },\n    M: { field: 'month', desc: 'Month in year' },\n    L: { field: 'month', desc: 'Stand-alone month in year' },\n    w: { field: 'week', desc: 'Week of year' },\n    W: { field: 'week', desc: 'Week of month' },\n    d: { field: 'day', desc: 'Day in month' },\n    D: { field: 'day', desc: 'Day of year' },\n    F: { field: 'day', desc: 'Day of week in month' },\n    g: { field: 'day', desc: 'Modified julian day' },\n    E: { field: 'weekday', desc: 'Day of week' },\n    e: { field: 'weekday', desc: 'Local day of week' },\n    c: { field: 'weekday', desc: 'Stand-alone local day of week' },\n    a: { field: 'period', desc: 'AM/PM marker' },\n    b: { field: 'period', desc: 'AM/PM/noon/midnight marker' },\n    B: { field: 'period', desc: 'Flexible day period' },\n    h: { field: 'hour', desc: 'Hour in AM/PM (1~12)' },\n    H: { field: 'hour', desc: 'Hour in day (0~23)' },\n    k: { field: 'hour', desc: 'Hour in day (1~24)' },\n    K: { field: 'hour', desc: 'Hour in AM/PM (0~11)' },\n    j: { field: 'hour', desc: 'Hour in preferred cycle' },\n    J: { field: 'hour', desc: 'Hour in preferred cycle without marker' },\n    C: { field: 'hour', desc: 'Hour in preferred cycle with flexible marker' },\n    m: { field: 'min', desc: 'Minute in hour' },\n    s: { field: 'sec', desc: 'Second in minute' },\n    S: { field: 'sec-frac', desc: 'Fractional second' },\n    A: { field: 'ms', desc: 'Milliseconds in day' },\n    z: { field: 'tz', desc: 'Time Zone: specific non-location' },\n    Z: { field: 'tz', desc: 'Time Zone' },\n    O: { field: 'tz', desc: 'Time Zone: localized' },\n    v: { field: 'tz', desc: 'Time Zone: generic non-location' },\n    V: { field: 'tz', desc: 'Time Zone: ID' },\n    X: { field: 'tz', desc: 'Time Zone: ISO8601 with Z' },\n    x: { field: 'tz', desc: 'Time Zone: ISO8601' }\n};\nconst isLetter = (char) => (char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z');\nfunction readFieldToken(src, pos) {\n    const char = src[pos];\n    let width = 1;\n    while (src[++pos] === char)\n        ++width;\n    const field = fields[char];\n    if (!field) {\n        const msg = `The letter ${char} is not a valid field identifier`;\n        return { char, error: new Error(msg), width };\n    }\n    return { char, field: field.field, desc: field.desc, width };\n}\nfunction readQuotedToken(src, pos) {\n    let str = src[++pos];\n    let width = 2;\n    if (str === \"'\")\n        return { char: \"'\", str, width };\n    while (true) {\n        const next = src[++pos];\n        ++width;\n        if (next === undefined) {\n            const msg = `Unterminated quoted literal in pattern: ${str || src}`;\n            return { char: \"'\", error: new Error(msg), str, width };\n        }\n        else if (next === \"'\") {\n            if (src[++pos] !== \"'\")\n                return { char: \"'\", str, width };\n            else\n                ++width;\n        }\n        str += next;\n    }\n}\nfunction readToken(src, pos) {\n    const char = src[pos];\n    if (!char)\n        return null;\n    if (isLetter(char))\n        return readFieldToken(src, pos);\n    if (char === \"'\")\n        return readQuotedToken(src, pos);\n    let str = char;\n    let width = 1;\n    while (true) {\n        const next = src[++pos];\n        if (!next || isLetter(next) || next === \"'\")\n            return { char, str, width };\n        str += next;\n        width += 1;\n    }\n}\n/**\n * Parse an {@link http://userguide.icu-project.org/formatparse/datetime | ICU\n * DateFormat skeleton} string into a {@link DateToken} array.\n *\n * @remarks\n * Errors will not be thrown, but if encountered are included as the relevant\n * token's `error` value.\n *\n * @public\n * @param src - The skeleton string\n *\n * @example\n * ```js\n * import { parseDateTokens } from '@messageformat/date-skeleton'\n *\n * parseDateTokens('GrMMMdd', console.error)\n * // [\n * //   { char: 'G', field: 'era', desc: 'Era', width: 1 },\n * //   { char: 'r', field: 'year', desc: 'Related Gregorian year', width: 1 },\n * //   { char: 'M', field: 'month', desc: 'Month in year', width: 3 },\n * //   { char: 'd', field: 'day', desc: 'Day in month', width: 2 }\n * // ]\n * ```\n */\nfunction parseDateTokens(src) {\n    const tokens = [];\n    let pos = 0;\n    while (true) {\n        const token = readToken(src, pos);\n        if (!token)\n            return tokens;\n        tokens.push(token);\n        pos += token.width;\n    }\n}\n\nfunction processTokens(tokens, mapText) {\n  if (!tokens.filter((token) => token.type !== \"content\").length) {\n    return tokens.map((token) => mapText(token.value));\n  }\n  return tokens.map((token) => {\n    if (token.type === \"content\") {\n      return mapText(token.value);\n    } else if (token.type === \"octothorpe\") {\n      return \"#\";\n    } else if (token.type === \"argument\") {\n      return [token.arg];\n    } else if (token.type === \"function\") {\n      const _param = token?.param?.[0];\n      if (token.key === \"date\" && _param) {\n        const opts = compileDateExpression(_param.value.trim(), (e) => {\n          throw new Error(`Unable to compile date expression: ${e.message}`);\n        });\n        return [token.arg, token.key, opts];\n      }\n      if (_param) {\n        return [token.arg, token.key, _param.value.trim()];\n      } else {\n        return [token.arg, token.key];\n      }\n    }\n    const offset = token.pluralOffset;\n    const formatProps = {};\n    token.cases.forEach(({ key, tokens: tokens2 }) => {\n      const prop = key[0] === \"=\" ? key.slice(1) : key;\n      formatProps[prop] = processTokens(tokens2, mapText);\n    });\n    return [\n      token.arg,\n      token.type,\n      {\n        offset,\n        ...formatProps\n      }\n    ];\n  });\n}\nfunction compileDateExpression(format, onError) {\n  if (/^::/.test(format)) {\n    const tokens = parseDateTokens(format.substring(2));\n    return getDateFormatOptions(tokens, void 0, onError);\n  }\n  return format;\n}\nfunction compileMessageOrThrow(message, mapText = (v) => v) {\n  return processTokens((0,_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.parse)(message), mapText);\n}\nfunction compileMessage(message, mapText = (v) => v) {\n  try {\n    return compileMessageOrThrow(message, mapText);\n  } catch (e) {\n    console.error(`${e.message} \n\nMessage: ${message}`);\n    return [message];\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@lingui/core/dist/index.mjs":
/*!******************************************************!*\
  !*** ../../node_modules/@lingui/core/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18n: () => (/* binding */ I18n),\n/* harmony export */   formats: () => (/* binding */ formats),\n/* harmony export */   i18n: () => (/* binding */ i18n),\n/* harmony export */   setupI18n: () => (/* binding */ setupI18n)\n/* harmony export */ });\n/* harmony import */ var _lingui_message_utils_compileMessage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lingui/message-utils/compileMessage */ \"(ssr)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs\");\n\n\nconst isString = (s) => typeof s === \"string\";\nconst isFunction = (f) => typeof f === \"function\";\n\nconst cache = /* @__PURE__ */ new Map();\nconst defaultLocale = \"en\";\nfunction normalizeLocales(locales) {\n  const out = Array.isArray(locales) ? locales : [locales];\n  return [...out, defaultLocale];\n}\nfunction date(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  if (!format) {\n    format = \"default\";\n  }\n  let o;\n  if (typeof format === \"string\") {\n    o = {\n      day: \"numeric\",\n      month: \"short\",\n      year: \"numeric\"\n    };\n    switch (format) {\n      case \"full\":\n        o.weekday = \"long\";\n      case \"long\":\n        o.month = \"long\";\n        break;\n      case \"short\":\n        o.month = \"numeric\";\n        break;\n    }\n  } else {\n    o = format;\n  }\n  const formatter = getMemoized(\n    () => cacheKey(\"date\", _locales, format),\n    () => new Intl.DateTimeFormat(_locales, o)\n  );\n  return formatter.format(isString(value) ? new Date(value) : value);\n}\nfunction time(locales, value, format) {\n  let o;\n  if (!format) {\n    format = \"default\";\n  }\n  if (typeof format === \"string\") {\n    o = {\n      second: \"numeric\",\n      minute: \"numeric\",\n      hour: \"numeric\"\n    };\n    switch (format) {\n      case \"full\":\n      case \"long\":\n        o.timeZoneName = \"short\";\n        break;\n      case \"short\":\n        delete o.second;\n    }\n  } else {\n    o = format;\n  }\n  return date(locales, value, o);\n}\nfunction number(locales, value, format) {\n  const _locales = normalizeLocales(locales);\n  const formatter = getMemoized(\n    () => cacheKey(\"number\", _locales, format),\n    () => new Intl.NumberFormat(_locales, format)\n  );\n  return formatter.format(value);\n}\nfunction plural(locales, ordinal, value, { offset = 0, ...rules }) {\n  const _locales = normalizeLocales(locales);\n  const plurals = ordinal ? getMemoized(\n    () => cacheKey(\"plural-ordinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"ordinal\" })\n  ) : getMemoized(\n    () => cacheKey(\"plural-cardinal\", _locales),\n    () => new Intl.PluralRules(_locales, { type: \"cardinal\" })\n  );\n  return rules[value] ?? rules[plurals.select(value - offset)] ?? rules.other;\n}\nfunction getMemoized(getKey, construct) {\n  const key = getKey();\n  let formatter = cache.get(key);\n  if (!formatter) {\n    formatter = construct();\n    cache.set(key, formatter);\n  }\n  return formatter;\n}\nfunction cacheKey(type, locales, options) {\n  const localeKey = locales.join(\"-\");\n  return `${type}-${localeKey}-${JSON.stringify(options)}`;\n}\n\nconst formats = {\n  __proto__: null,\n  date: date,\n  defaultLocale: defaultLocale,\n  number: number,\n  plural: plural,\n  time: time\n};\n\nconst ESCAPE_SEQUENCE_REGEX = /\\\\u[a-fA-F0-9]{4}|\\\\x[a-fA-F0-9]{2}/;\nconst decodeEscapeSequences = (str) => {\n  return str.replace(\n    // Same pattern but with capturing groups for extracting values during replacement\n    /\\\\u([a-fA-F0-9]{4})|\\\\x([a-fA-F0-9]{2})/g,\n    (_, unicode, hex) => {\n      if (unicode) {\n        const codePoint = parseInt(unicode, 16);\n        return String.fromCharCode(codePoint);\n      } else {\n        const codePoint = parseInt(hex, 16);\n        return String.fromCharCode(codePoint);\n      }\n    }\n  );\n};\n\nconst OCTOTHORPE_PH = \"%__lingui_octothorpe__%\";\nconst getDefaultFormats = (locale, passedLocales, formats = {}) => {\n  const locales = passedLocales || locale;\n  const style = (format) => {\n    if (typeof format === \"object\")\n      return format;\n    return formats[format];\n  };\n  const replaceOctothorpe = (value, message) => {\n    const numberFormat = Object.keys(formats).length ? style(\"number\") : void 0;\n    const valueStr = number(locales, value, numberFormat);\n    return message.replace(new RegExp(OCTOTHORPE_PH, \"g\"), valueStr);\n  };\n  return {\n    plural: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, false, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    selectordinal: (value, cases) => {\n      const { offset = 0 } = cases;\n      const message = plural(locales, true, value, cases);\n      return replaceOctothorpe(value - offset, message);\n    },\n    select: selectFormatter,\n    number: (value, format) => number(\n      locales,\n      value,\n      style(format) || { style: format }\n    ),\n    date: (value, format) => date(locales, value, style(format) || format),\n    time: (value, format) => time(locales, value, style(format) || format)\n  };\n};\nconst selectFormatter = (value, rules) => rules[value] ?? rules.other;\nfunction interpolate(translation, locale, locales) {\n  return (values = {}, formats) => {\n    const formatters = getDefaultFormats(locale, locales, formats);\n    const formatMessage = (tokens, replaceOctothorpe = false) => {\n      if (!Array.isArray(tokens))\n        return tokens;\n      return tokens.reduce((message, token) => {\n        if (token === \"#\" && replaceOctothorpe) {\n          return message + OCTOTHORPE_PH;\n        }\n        if (isString(token)) {\n          return message + token;\n        }\n        const [name, type, format] = token;\n        let interpolatedFormat = {};\n        if (type === \"plural\" || type === \"selectordinal\" || type === \"select\") {\n          Object.entries(format).forEach(\n            ([key, value2]) => {\n              interpolatedFormat[key] = formatMessage(\n                value2,\n                type === \"plural\" || type === \"selectordinal\"\n              );\n            }\n          );\n        } else {\n          interpolatedFormat = format;\n        }\n        let value;\n        if (type) {\n          const formatter = formatters[type];\n          value = formatter(values[name], interpolatedFormat);\n        } else {\n          value = values[name];\n        }\n        if (value == null) {\n          return message;\n        }\n        return message + value;\n      }, \"\");\n    };\n    const result = formatMessage(translation);\n    if (isString(result) && ESCAPE_SEQUENCE_REGEX.test(result)) {\n      return decodeEscapeSequences(result);\n    }\n    if (isString(result))\n      return result;\n    return result ? String(result) : \"\";\n  };\n}\n\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => {\n  __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventEmitter {\n  constructor() {\n    __publicField$1(this, \"_events\", {});\n  }\n  on(event, listener) {\n    var _a;\n    (_a = this._events)[event] ?? (_a[event] = []);\n    this._events[event].push(listener);\n    return () => this.removeListener(event, listener);\n  }\n  removeListener(event, listener) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    const index = maybeListeners.indexOf(listener);\n    if (~index)\n      maybeListeners.splice(index, 1);\n  }\n  emit(event, ...args) {\n    const maybeListeners = this._getListeners(event);\n    if (!maybeListeners)\n      return;\n    maybeListeners.map((listener) => listener.apply(this, args));\n  }\n  _getListeners(event) {\n    const maybeListeners = this._events[event];\n    return Array.isArray(maybeListeners) ? maybeListeners : false;\n  }\n}\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass I18n extends EventEmitter {\n  constructor(params) {\n    super();\n    __publicField(this, \"_locale\", \"\");\n    __publicField(this, \"_locales\");\n    __publicField(this, \"_localeData\", {});\n    __publicField(this, \"_messages\", {});\n    __publicField(this, \"_missing\");\n    __publicField(this, \"_messageCompiler\");\n    /**\n     * Alias for {@see I18n._}\n     */\n    __publicField(this, \"t\", this._.bind(this));\n    if (true) {\n      this.setMessagesCompiler(_lingui_message_utils_compileMessage__WEBPACK_IMPORTED_MODULE_0__.compileMessage);\n    }\n    if (params.missing != null)\n      this._missing = params.missing;\n    if (params.messages != null)\n      this.load(params.messages);\n    if (params.localeData != null)\n      this.loadLocaleData(params.localeData);\n    if (typeof params.locale === \"string\" || params.locales) {\n      this.activate(params.locale ?? defaultLocale, params.locales);\n    }\n  }\n  get locale() {\n    return this._locale;\n  }\n  get locales() {\n    return this._locales;\n  }\n  get messages() {\n    return this._messages[this._locale] ?? {};\n  }\n  /**\n   * @deprecated this has no effect. Please remove this from the code. Deprecated in v4\n   */\n  get localeData() {\n    return this._localeData[this._locale] ?? {};\n  }\n  _loadLocaleData(locale, localeData) {\n    const maybeLocaleData = this._localeData[locale];\n    if (!maybeLocaleData) {\n      this._localeData[locale] = localeData;\n    } else {\n      Object.assign(maybeLocaleData, localeData);\n    }\n  }\n  /**\n   * Registers a `MessageCompiler` to enable the use of uncompiled catalogs at runtime.\n   *\n   * In production builds, the `MessageCompiler` is typically excluded to reduce bundle size.\n   * By default, message catalogs should be precompiled during the build process. However,\n   * if you need to compile catalogs at runtime, you can use this method to set a message compiler.\n   *\n   * Example usage:\n   *\n   * ```ts\n   * import { compileMessage } from \"@lingui/message-utils/compileMessage\";\n   *\n   * i18n.setMessagesCompiler(compileMessage);\n   * ```\n   */\n  setMessagesCompiler(compiler) {\n    this._messageCompiler = compiler;\n    return this;\n  }\n  /**\n   * @deprecated Plurals automatically used from Intl.PluralRules you can safely remove this call. Deprecated in v4\n   */\n  loadLocaleData(localeOrAllData, localeData) {\n    if (typeof localeOrAllData === \"string\") {\n      this._loadLocaleData(localeOrAllData, localeData);\n    } else {\n      Object.keys(localeOrAllData).forEach(\n        (locale) => this._loadLocaleData(locale, localeOrAllData[locale])\n      );\n    }\n    this.emit(\"change\");\n  }\n  _load(locale, messages) {\n    const maybeMessages = this._messages[locale];\n    if (!maybeMessages) {\n      this._messages[locale] = messages;\n    } else {\n      Object.assign(maybeMessages, messages);\n    }\n  }\n  load(localeOrMessages, messages) {\n    if (typeof localeOrMessages == \"string\" && typeof messages === \"object\") {\n      this._load(localeOrMessages, messages);\n    } else {\n      Object.entries(localeOrMessages).forEach(\n        ([locale, messages2]) => this._load(locale, messages2)\n      );\n    }\n    this.emit(\"change\");\n  }\n  /**\n   * @param options {@link LoadAndActivateOptions}\n   */\n  loadAndActivate({ locale, locales, messages }) {\n    this._locale = locale;\n    this._locales = locales || void 0;\n    this._messages[this._locale] = messages;\n    this.emit(\"change\");\n  }\n  activate(locale, locales) {\n    if (true) {\n      if (!this._messages[locale]) {\n        console.warn(`Messages for locale \"${locale}\" not loaded.`);\n      }\n    }\n    this._locale = locale;\n    this._locales = locales;\n    this.emit(\"change\");\n  }\n  _(id, values, options) {\n    if (!this.locale) {\n      throw new Error(\n        \"Lingui: Attempted to call a translation function without setting a locale.\\nMake sure to call `i18n.activate(locale)` before using Lingui functions.\\nThis issue may also occur due to a race condition in your initialization logic.\"\n      );\n    }\n    let message = options?.message;\n    if (!id) {\n      id = \"\";\n    }\n    if (!isString(id)) {\n      values = id.values || values;\n      message = id.message;\n      id = id.id;\n    }\n    const messageForId = this.messages[id];\n    const messageMissing = messageForId === void 0;\n    const missing = this._missing;\n    if (missing && messageMissing) {\n      return isFunction(missing) ? missing(this._locale, id) : missing;\n    }\n    if (messageMissing) {\n      this.emit(\"missing\", { id, locale: this._locale });\n    }\n    let translation = messageForId || message || id;\n    if (isString(translation)) {\n      if (this._messageCompiler) {\n        translation = this._messageCompiler(translation);\n      } else {\n        console.warn(`Uncompiled message detected! Message:\n\n> ${translation}\n\nThat means you use raw catalog or your catalog doesn't have a translation for the message and fallback was used.\nICU features such as interpolation and plurals will not work properly for that message. \n\nPlease compile your catalog first. \n`);\n      }\n    }\n    if (isString(translation) && ESCAPE_SEQUENCE_REGEX.test(translation))\n      return decodeEscapeSequences(translation);\n    if (isString(translation))\n      return translation;\n    return interpolate(\n      translation,\n      this._locale,\n      this._locales\n    )(values, options?.formats);\n  }\n  date(value, format) {\n    return date(this._locales || this._locale, value, format);\n  }\n  number(value, format) {\n    return number(this._locales || this._locale, value, format);\n  }\n}\nfunction setupI18n(params = {}) {\n  return new I18n(params);\n}\n\nconst i18n = setupI18n();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@lingui/core/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs":
/*!************************************************************************!*\
  !*** ../../node_modules/@lingui/message-utils/dist/compileMessage.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compileMessage: () => (/* binding */ compileMessage),\n/* harmony export */   compileMessageOrThrow: () => (/* binding */ compileMessageOrThrow)\n/* harmony export */ });\n/* harmony import */ var _messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @messageformat/parser */ \"(ssr)/../../node_modules/@messageformat/parser/lib/parser.js\");\n\n\n/**\n * Parent class for errors.\n *\n * @remarks\n * Errors with `type: \"warning\"` do not necessarily indicate that the parser\n * encountered an error. In addition to a human-friendly `message`, may also\n * includes the `token` at which the error was encountered.\n *\n * @public\n */\nclass DateFormatError extends Error {\n    /** @internal */\n    constructor(msg, token, type) {\n        super(msg);\n        this.token = token;\n        this.type = type || 'error';\n    }\n}\nconst alpha = (width) => width < 4 ? 'short' : width === 4 ? 'long' : 'narrow';\nconst numeric = (width) => (width % 2 === 0 ? '2-digit' : 'numeric');\nfunction yearOptions(token, onError) {\n    switch (token.char) {\n        case 'y':\n            return { year: numeric(token.width) };\n        case 'r':\n            return { calendar: 'gregory', year: 'numeric' };\n        case 'u':\n        case 'U':\n        case 'Y':\n        default:\n            onError(`${token.desc} is not supported; falling back to year:numeric`, DateFormatError.WARNING);\n            return { year: 'numeric' };\n    }\n}\nfunction monthStyle(token, onError) {\n    switch (token.width) {\n        case 1:\n            return 'numeric';\n        case 2:\n            return '2-digit';\n        case 3:\n            return 'short';\n        case 4:\n            return 'long';\n        case 5:\n            return 'narrow';\n        default:\n            onError(`${token.desc} is not supported with width ${token.width}`);\n            return undefined;\n    }\n}\nfunction dayStyle(token, onError) {\n    const { char, desc, width } = token;\n    if (char === 'd') {\n        return numeric(width);\n    }\n    else {\n        onError(`${desc} is not supported`);\n        return undefined;\n    }\n}\nfunction weekdayStyle(token, onError) {\n    const { char, desc, width } = token;\n    if ((char === 'c' || char === 'e') && width < 3) {\n        // ignoring stand-alone-ness\n        const msg = `Numeric value is not supported for ${desc}; falling back to weekday:short`;\n        onError(msg, DateFormatError.WARNING);\n    }\n    // merging narrow styles\n    return alpha(width);\n}\nfunction hourOptions(token) {\n    const hour = numeric(token.width);\n    let hourCycle;\n    switch (token.char) {\n        case 'h':\n            hourCycle = 'h12';\n            break;\n        case 'H':\n            hourCycle = 'h23';\n            break;\n        case 'k':\n            hourCycle = 'h24';\n            break;\n        case 'K':\n            hourCycle = 'h11';\n            break;\n    }\n    return hourCycle ? { hour, hourCycle } : { hour };\n}\nfunction timeZoneNameStyle(token, onError) {\n    // so much fallback behaviour here\n    const { char, desc, width } = token;\n    switch (char) {\n        case 'v':\n        case 'z':\n            return width === 4 ? 'long' : 'short';\n        case 'V':\n            if (width === 4)\n                return 'long';\n            onError(`${desc} is not supported with width ${width}`);\n            return undefined;\n        case 'X':\n            onError(`${desc} is not supported`);\n            return undefined;\n    }\n    return 'short';\n}\nfunction compileOptions(token, onError) {\n    switch (token.field) {\n        case 'era':\n            return { era: alpha(token.width) };\n        case 'year':\n            return yearOptions(token, onError);\n        case 'month':\n            return { month: monthStyle(token, onError) };\n        case 'day':\n            return { day: dayStyle(token, onError) };\n        case 'weekday':\n            return { weekday: weekdayStyle(token, onError) };\n        case 'period':\n            return undefined;\n        case 'hour':\n            return hourOptions(token);\n        case 'min':\n            return { minute: numeric(token.width) };\n        case 'sec':\n            return { second: numeric(token.width) };\n        case 'tz':\n            return { timeZoneName: timeZoneNameStyle(token, onError) };\n        case 'quarter':\n        case 'week':\n        case 'sec-frac':\n        case 'ms':\n            onError(`${token.desc} is not supported`);\n    }\n    return undefined;\n}\nfunction getDateFormatOptions(tokens, timeZone, onError = error => {\n    throw error;\n}) {\n    const options = {\n        timeZone\n    };\n    const fields = [];\n    for (const token of tokens) {\n        const { error, field, str } = token;\n        if (error) {\n            const dte = new DateFormatError(error.message, token);\n            dte.stack = error.stack;\n            onError(dte);\n        }\n        if (str) {\n            const msg = `Ignoring string part: ${str}`;\n            onError(new DateFormatError(msg, token, DateFormatError.WARNING));\n        }\n        if (field) {\n            if (fields.indexOf(field) === -1)\n                fields.push(field);\n            else\n                onError(new DateFormatError(`Duplicate ${field} token`, token));\n        }\n        const opt = compileOptions(token, (msg, isWarning) => onError(new DateFormatError(msg, token, isWarning)));\n        if (opt)\n            Object.assign(options, opt);\n    }\n    return options;\n}\n\nconst fields = {\n    G: { field: 'era', desc: 'Era' },\n    y: { field: 'year', desc: 'Year' },\n    Y: { field: 'year', desc: 'Year of \"Week of Year\"' },\n    u: { field: 'year', desc: 'Extended year' },\n    U: { field: 'year', desc: 'Cyclic year name' },\n    r: { field: 'year', desc: 'Related Gregorian year' },\n    Q: { field: 'quarter', desc: 'Quarter' },\n    q: { field: 'quarter', desc: 'Stand-alone quarter' },\n    M: { field: 'month', desc: 'Month in year' },\n    L: { field: 'month', desc: 'Stand-alone month in year' },\n    w: { field: 'week', desc: 'Week of year' },\n    W: { field: 'week', desc: 'Week of month' },\n    d: { field: 'day', desc: 'Day in month' },\n    D: { field: 'day', desc: 'Day of year' },\n    F: { field: 'day', desc: 'Day of week in month' },\n    g: { field: 'day', desc: 'Modified julian day' },\n    E: { field: 'weekday', desc: 'Day of week' },\n    e: { field: 'weekday', desc: 'Local day of week' },\n    c: { field: 'weekday', desc: 'Stand-alone local day of week' },\n    a: { field: 'period', desc: 'AM/PM marker' },\n    b: { field: 'period', desc: 'AM/PM/noon/midnight marker' },\n    B: { field: 'period', desc: 'Flexible day period' },\n    h: { field: 'hour', desc: 'Hour in AM/PM (1~12)' },\n    H: { field: 'hour', desc: 'Hour in day (0~23)' },\n    k: { field: 'hour', desc: 'Hour in day (1~24)' },\n    K: { field: 'hour', desc: 'Hour in AM/PM (0~11)' },\n    j: { field: 'hour', desc: 'Hour in preferred cycle' },\n    J: { field: 'hour', desc: 'Hour in preferred cycle without marker' },\n    C: { field: 'hour', desc: 'Hour in preferred cycle with flexible marker' },\n    m: { field: 'min', desc: 'Minute in hour' },\n    s: { field: 'sec', desc: 'Second in minute' },\n    S: { field: 'sec-frac', desc: 'Fractional second' },\n    A: { field: 'ms', desc: 'Milliseconds in day' },\n    z: { field: 'tz', desc: 'Time Zone: specific non-location' },\n    Z: { field: 'tz', desc: 'Time Zone' },\n    O: { field: 'tz', desc: 'Time Zone: localized' },\n    v: { field: 'tz', desc: 'Time Zone: generic non-location' },\n    V: { field: 'tz', desc: 'Time Zone: ID' },\n    X: { field: 'tz', desc: 'Time Zone: ISO8601 with Z' },\n    x: { field: 'tz', desc: 'Time Zone: ISO8601' }\n};\nconst isLetter = (char) => (char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z');\nfunction readFieldToken(src, pos) {\n    const char = src[pos];\n    let width = 1;\n    while (src[++pos] === char)\n        ++width;\n    const field = fields[char];\n    if (!field) {\n        const msg = `The letter ${char} is not a valid field identifier`;\n        return { char, error: new Error(msg), width };\n    }\n    return { char, field: field.field, desc: field.desc, width };\n}\nfunction readQuotedToken(src, pos) {\n    let str = src[++pos];\n    let width = 2;\n    if (str === \"'\")\n        return { char: \"'\", str, width };\n    while (true) {\n        const next = src[++pos];\n        ++width;\n        if (next === undefined) {\n            const msg = `Unterminated quoted literal in pattern: ${str || src}`;\n            return { char: \"'\", error: new Error(msg), str, width };\n        }\n        else if (next === \"'\") {\n            if (src[++pos] !== \"'\")\n                return { char: \"'\", str, width };\n            else\n                ++width;\n        }\n        str += next;\n    }\n}\nfunction readToken(src, pos) {\n    const char = src[pos];\n    if (!char)\n        return null;\n    if (isLetter(char))\n        return readFieldToken(src, pos);\n    if (char === \"'\")\n        return readQuotedToken(src, pos);\n    let str = char;\n    let width = 1;\n    while (true) {\n        const next = src[++pos];\n        if (!next || isLetter(next) || next === \"'\")\n            return { char, str, width };\n        str += next;\n        width += 1;\n    }\n}\n/**\n * Parse an {@link http://userguide.icu-project.org/formatparse/datetime | ICU\n * DateFormat skeleton} string into a {@link DateToken} array.\n *\n * @remarks\n * Errors will not be thrown, but if encountered are included as the relevant\n * token's `error` value.\n *\n * @public\n * @param src - The skeleton string\n *\n * @example\n * ```js\n * import { parseDateTokens } from '@messageformat/date-skeleton'\n *\n * parseDateTokens('GrMMMdd', console.error)\n * // [\n * //   { char: 'G', field: 'era', desc: 'Era', width: 1 },\n * //   { char: 'r', field: 'year', desc: 'Related Gregorian year', width: 1 },\n * //   { char: 'M', field: 'month', desc: 'Month in year', width: 3 },\n * //   { char: 'd', field: 'day', desc: 'Day in month', width: 2 }\n * // ]\n * ```\n */\nfunction parseDateTokens(src) {\n    const tokens = [];\n    let pos = 0;\n    while (true) {\n        const token = readToken(src, pos);\n        if (!token)\n            return tokens;\n        tokens.push(token);\n        pos += token.width;\n    }\n}\n\nfunction processTokens(tokens, mapText) {\n  if (!tokens.filter((token) => token.type !== \"content\").length) {\n    return tokens.map((token) => mapText(token.value));\n  }\n  return tokens.map((token) => {\n    if (token.type === \"content\") {\n      return mapText(token.value);\n    } else if (token.type === \"octothorpe\") {\n      return \"#\";\n    } else if (token.type === \"argument\") {\n      return [token.arg];\n    } else if (token.type === \"function\") {\n      const _param = token?.param?.[0];\n      if (token.key === \"date\" && _param) {\n        const opts = compileDateExpression(_param.value.trim(), (e) => {\n          throw new Error(`Unable to compile date expression: ${e.message}`);\n        });\n        return [token.arg, token.key, opts];\n      }\n      if (_param) {\n        return [token.arg, token.key, _param.value.trim()];\n      } else {\n        return [token.arg, token.key];\n      }\n    }\n    const offset = token.pluralOffset;\n    const formatProps = {};\n    token.cases.forEach(({ key, tokens: tokens2 }) => {\n      const prop = key[0] === \"=\" ? key.slice(1) : key;\n      formatProps[prop] = processTokens(tokens2, mapText);\n    });\n    return [\n      token.arg,\n      token.type,\n      {\n        offset,\n        ...formatProps\n      }\n    ];\n  });\n}\nfunction compileDateExpression(format, onError) {\n  if (/^::/.test(format)) {\n    const tokens = parseDateTokens(format.substring(2));\n    return getDateFormatOptions(tokens, void 0, onError);\n  }\n  return format;\n}\nfunction compileMessageOrThrow(message, mapText = (v) => v) {\n  return processTokens((0,_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.parse)(message), mapText);\n}\nfunction compileMessage(message, mapText = (v) => v) {\n  try {\n    return compileMessageOrThrow(message, mapText);\n  } catch (e) {\n    console.error(`${e.message} \n\nMessage: ${message}`);\n    return [message];\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@lingui/message-utils/dist/compileMessage.mjs\n");

/***/ })

};
;