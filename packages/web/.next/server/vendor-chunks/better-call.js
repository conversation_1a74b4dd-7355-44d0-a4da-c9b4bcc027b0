"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/better-call";
exports.ids = ["vendor-chunks/better-call"];
exports.modules = {

/***/ "(rsc)/../../node_modules/better-call/dist/index.js":
/*!****************************************************!*\
  !*** ../../node_modules/better-call/dist/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   _statusCode: () => (/* binding */ _statusCode),\n/* harmony export */   createEndpoint: () => (/* binding */ createEndpoint2),\n/* harmony export */   createInternalContext: () => (/* binding */ createInternalContext),\n/* harmony export */   createMiddleware: () => (/* binding */ createMiddleware),\n/* harmony export */   createRouter: () => (/* binding */ createRouter),\n/* harmony export */   generator: () => (/* binding */ generator),\n/* harmony export */   getCookieKey: () => (/* binding */ getCookieKey),\n/* harmony export */   getHTML: () => (/* binding */ getHTML),\n/* harmony export */   parseCookies: () => (/* binding */ parseCookies),\n/* harmony export */   serializeCookie: () => (/* binding */ serializeCookie),\n/* harmony export */   serializeSignedCookie: () => (/* binding */ serializeSignedCookie),\n/* harmony export */   toResponse: () => (/* binding */ toResponse)\n/* harmony export */ });\n/* harmony import */ var uncrypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uncrypto */ \"(rsc)/../../node_modules/uncrypto/dist/crypto.node.mjs\");\n/* harmony import */ var rou3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rou3 */ \"(rsc)/../../node_modules/rou3/dist/index.mjs\");\n// src/error.ts\nvar _statusCode = {\n  OK: 200,\n  CREATED: 201,\n  ACCEPTED: 202,\n  NO_CONTENT: 204,\n  MULTIPLE_CHOICES: 300,\n  MOVED_PERMANENTLY: 301,\n  FOUND: 302,\n  SEE_OTHER: 303,\n  NOT_MODIFIED: 304,\n  TEMPORARY_REDIRECT: 307,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  PAYMENT_REQUIRED: 402,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  METHOD_NOT_ALLOWED: 405,\n  NOT_ACCEPTABLE: 406,\n  PROXY_AUTHENTICATION_REQUIRED: 407,\n  REQUEST_TIMEOUT: 408,\n  CONFLICT: 409,\n  GONE: 410,\n  LENGTH_REQUIRED: 411,\n  PRECONDITION_FAILED: 412,\n  PAYLOAD_TOO_LARGE: 413,\n  URI_TOO_LONG: 414,\n  UNSUPPORTED_MEDIA_TYPE: 415,\n  RANGE_NOT_SATISFIABLE: 416,\n  EXPECTATION_FAILED: 417,\n  \"I'M_A_TEAPOT\": 418,\n  MISDIRECTED_REQUEST: 421,\n  UNPROCESSABLE_ENTITY: 422,\n  LOCKED: 423,\n  FAILED_DEPENDENCY: 424,\n  TOO_EARLY: 425,\n  UPGRADE_REQUIRED: 426,\n  PRECONDITION_REQUIRED: 428,\n  TOO_MANY_REQUESTS: 429,\n  REQUEST_HEADER_FIELDS_TOO_LARGE: 431,\n  UNAVAILABLE_FOR_LEGAL_REASONS: 451,\n  INTERNAL_SERVER_ERROR: 500,\n  NOT_IMPLEMENTED: 501,\n  BAD_GATEWAY: 502,\n  SERVICE_UNAVAILABLE: 503,\n  GATEWAY_TIMEOUT: 504,\n  HTTP_VERSION_NOT_SUPPORTED: 505,\n  VARIANT_ALSO_NEGOTIATES: 506,\n  INSUFFICIENT_STORAGE: 507,\n  LOOP_DETECTED: 508,\n  NOT_EXTENDED: 510,\n  NETWORK_AUTHENTICATION_REQUIRED: 511\n};\nvar APIError = class extends Error {\n  constructor(status = \"INTERNAL_SERVER_ERROR\", body = void 0, headers = {}, statusCode = typeof status === \"number\" ? status : _statusCode[status]) {\n    super(body?.message);\n    this.status = status;\n    this.body = body;\n    this.headers = headers;\n    this.statusCode = statusCode;\n    this.name = \"APIError\";\n    this.status = status;\n    this.headers = headers;\n    this.statusCode = statusCode;\n    this.body = body ? {\n      code: body?.message?.toUpperCase().replace(/ /g, \"_\").replace(/[^A-Z0-9_]/g, \"\"),\n      ...body\n    } : void 0;\n    this.stack = \"\";\n  }\n};\n\n// src/utils.ts\nasync function getBody(request) {\n  const contentType = request.headers.get(\"content-type\") || \"\";\n  if (!request.body) {\n    return void 0;\n  }\n  if (contentType.includes(\"application/json\")) {\n    return await request.json();\n  }\n  if (contentType.includes(\"application/x-www-form-urlencoded\")) {\n    const formData = await request.formData();\n    const result = {};\n    formData.forEach((value, key) => {\n      result[key] = value.toString();\n    });\n    return result;\n  }\n  if (contentType.includes(\"multipart/form-data\")) {\n    const formData = await request.formData();\n    const result = {};\n    formData.forEach((value, key) => {\n      result[key] = value;\n    });\n    return result;\n  }\n  if (contentType.includes(\"text/plain\")) {\n    return await request.text();\n  }\n  if (contentType.includes(\"application/octet-stream\")) {\n    return await request.arrayBuffer();\n  }\n  if (contentType.includes(\"application/pdf\") || contentType.includes(\"image/\") || contentType.includes(\"video/\")) {\n    const blob = await request.blob();\n    return blob;\n  }\n  if (contentType.includes(\"application/stream\") || request.body instanceof ReadableStream) {\n    return request.body;\n  }\n  return await request.text();\n}\nfunction isAPIError(error) {\n  return error instanceof APIError || error?.name === \"APIError\";\n}\nfunction tryDecode(str) {\n  try {\n    return str.includes(\"%\") ? decodeURIComponent(str) : str;\n  } catch {\n    return str;\n  }\n}\n\n// src/to-response.ts\nfunction isJSONSerializable(value) {\n  if (value === void 0) {\n    return false;\n  }\n  const t = typeof value;\n  if (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n    return true;\n  }\n  if (t !== \"object\") {\n    return false;\n  }\n  if (Array.isArray(value)) {\n    return true;\n  }\n  if (value.buffer) {\n    return false;\n  }\n  return value.constructor && value.constructor.name === \"Object\" || typeof value.toJSON === \"function\";\n}\nfunction toResponse(data, init) {\n  if (data instanceof Response) {\n    if (init?.headers instanceof Headers) {\n      init.headers.forEach((value, key) => {\n        data.headers.set(key, value);\n      });\n    }\n    return data;\n  }\n  if (data?._flag === \"json\") {\n    const routerResponse = data.routerResponse;\n    if (routerResponse instanceof Response) {\n      return routerResponse;\n    }\n    return toResponse(data.body, {\n      headers: data.headers,\n      status: data.status\n    });\n  }\n  if (isAPIError(data)) {\n    return toResponse(data.body, {\n      status: data.statusCode,\n      statusText: data.status.toString(),\n      headers: init?.headers || data.headers\n    });\n  }\n  let body = data;\n  let headers = new Headers(init?.headers);\n  if (!data) {\n    if (data === null) {\n      body = JSON.stringify(null);\n    }\n    headers.set(\"content-type\", \"application/json\");\n  } else if (typeof data === \"string\") {\n    body = data;\n    headers.set(\"Content-Type\", \"text/plain\");\n  } else if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n    body = data;\n    headers.set(\"Content-Type\", \"application/octet-stream\");\n  } else if (data instanceof Blob) {\n    body = data;\n    headers.set(\"Content-Type\", data.type || \"application/octet-stream\");\n  } else if (data instanceof FormData) {\n    body = data;\n  } else if (data instanceof URLSearchParams) {\n    body = data;\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n  } else if (data instanceof ReadableStream) {\n    body = data;\n    headers.set(\"Content-Type\", \"application/octet-stream\");\n  } else if (isJSONSerializable(data)) {\n    body = JSON.stringify(data, (key, value) => {\n      if (typeof value === \"bigint\") {\n        return value.toString();\n      }\n      return value;\n    });\n    headers.set(\"Content-Type\", \"application/json\");\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\n\n// src/validator.ts\nasync function runValidation(options, context = {}) {\n  let request = {\n    body: context.body,\n    query: context.query\n  };\n  if (options.body) {\n    const result = await options.body[\"~standard\"].validate(context.body);\n    if (result.issues) {\n      return {\n        data: null,\n        error: fromError(result.issues, \"body\")\n      };\n    }\n    request.body = result.value;\n  }\n  if (options.query) {\n    const result = await options.query[\"~standard\"].validate(context.query);\n    if (result.issues) {\n      return {\n        data: null,\n        error: fromError(result.issues, \"query\")\n      };\n    }\n    request.query = result.value;\n  }\n  if (options.requireHeaders && !context.headers) {\n    return {\n      data: null,\n      error: { message: \"Headers is required\" }\n    };\n  }\n  if (options.requireRequest && !context.request) {\n    return {\n      data: null,\n      error: { message: \"Request is required\" }\n    };\n  }\n  return {\n    data: request,\n    error: null\n  };\n}\nfunction fromError(error, validating) {\n  const errorMessages = [];\n  for (const issue of error) {\n    const message = issue.message;\n    errorMessages.push(message);\n  }\n  return {\n    message: `Invalid ${validating} parameters`\n  };\n}\n\n// src/crypto.ts\n\nvar algorithm = { name: \"HMAC\", hash: \"SHA-256\" };\nvar getCryptoKey = async (secret) => {\n  const secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n  return await uncrypto__WEBPACK_IMPORTED_MODULE_0__.subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\nvar verifySignature = async (base64Signature, value, secret) => {\n  try {\n    const signatureBinStr = atob(base64Signature);\n    const signature = new Uint8Array(signatureBinStr.length);\n    for (let i = 0, len = signatureBinStr.length; i < len; i++) {\n      signature[i] = signatureBinStr.charCodeAt(i);\n    }\n    return await uncrypto__WEBPACK_IMPORTED_MODULE_0__.subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n  } catch (e) {\n    return false;\n  }\n};\nvar makeSignature = async (value, secret) => {\n  const key = await getCryptoKey(secret);\n  const signature = await uncrypto__WEBPACK_IMPORTED_MODULE_0__.subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n  return btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\nvar signCookieValue = async (value, secret) => {\n  const signature = await makeSignature(value, secret);\n  value = `${value}.${signature}`;\n  value = encodeURIComponent(value);\n  return value;\n};\n\n// src/cookies.ts\nvar getCookieKey = (key, prefix) => {\n  let finalKey = key;\n  if (prefix) {\n    if (prefix === \"secure\") {\n      finalKey = \"__Secure-\" + key;\n    } else if (prefix === \"host\") {\n      finalKey = \"__Host-\" + key;\n    } else {\n      return void 0;\n    }\n  }\n  return finalKey;\n};\nfunction parseCookies(str) {\n  if (typeof str !== \"string\") {\n    throw new TypeError(\"argument str must be a string\");\n  }\n  const cookies = /* @__PURE__ */ new Map();\n  let index = 0;\n  while (index < str.length) {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) {\n      break;\n    }\n    let endIdx = str.indexOf(\";\", index);\n    if (endIdx === -1) {\n      endIdx = str.length;\n    } else if (endIdx < eqIdx) {\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n    const key = str.slice(index, eqIdx).trim();\n    if (!cookies.has(key)) {\n      let val = str.slice(eqIdx + 1, endIdx).trim();\n      if (val.codePointAt(0) === 34) {\n        val = val.slice(1, -1);\n      }\n      cookies.set(key, tryDecode(val));\n    }\n    index = endIdx + 1;\n  }\n  return cookies;\n}\nvar _serialize = (key, value, opt = {}) => {\n  let cookie;\n  if (opt?.prefix === \"secure\") {\n    cookie = `${`__Secure-${key}`}=${value}`;\n  } else if (opt?.prefix === \"host\") {\n    cookie = `${`__Host-${key}`}=${value}`;\n  } else {\n    cookie = `${key}=${value}`;\n  }\n  if (key.startsWith(\"__Secure-\") && !opt.secure) {\n    opt.secure = true;\n  }\n  if (key.startsWith(\"__Host-\")) {\n    if (!opt.secure) {\n      opt.secure = true;\n    }\n    if (opt.path !== \"/\") {\n      opt.path = \"/\";\n    }\n    if (opt.domain) {\n      opt.domain = void 0;\n    }\n  }\n  if (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n    if (opt.maxAge > 3456e4) {\n      throw new Error(\n        \"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\"\n      );\n    }\n    cookie += `; Max-Age=${Math.floor(opt.maxAge)}`;\n  }\n  if (opt.domain && opt.prefix !== \"host\") {\n    cookie += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    cookie += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (opt.expires.getTime() - Date.now() > 3456e7) {\n      throw new Error(\n        \"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\"\n      );\n    }\n    cookie += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) {\n    cookie += \"; HttpOnly\";\n  }\n  if (opt.secure) {\n    cookie += \"; Secure\";\n  }\n  if (opt.sameSite) {\n    cookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n  }\n  if (opt.partitioned) {\n    if (!opt.secure) {\n      opt.secure = true;\n    }\n    cookie += \"; Partitioned\";\n  }\n  return cookie;\n};\nvar serializeCookie = (key, value, opt) => {\n  value = encodeURIComponent(value);\n  return _serialize(key, value, opt);\n};\nvar serializeSignedCookie = async (key, value, secret, opt) => {\n  value = await signCookieValue(value, secret);\n  return _serialize(key, value, opt);\n};\n\n// src/context.ts\nvar createInternalContext = async (context, {\n  options,\n  path\n}) => {\n  const headers = new Headers();\n  const { data, error } = await runValidation(options, context);\n  if (error) {\n    throw new APIError(400, {\n      message: error.message,\n      code: \"VALIDATION_ERROR\"\n    });\n  }\n  const requestHeaders = \"headers\" in context ? context.headers instanceof Headers ? context.headers : new Headers(context.headers) : \"request\" in context && context.request instanceof Request ? context.request.headers : null;\n  const requestCookies = requestHeaders?.get(\"cookie\");\n  const parsedCookies = requestCookies ? parseCookies(requestCookies) : void 0;\n  const internalContext = {\n    ...context,\n    body: data.body,\n    query: data.query,\n    path: context.path || path,\n    context: \"context\" in context && context.context ? context.context : {},\n    returned: void 0,\n    headers: context?.headers,\n    request: context?.request,\n    params: \"params\" in context ? context.params : void 0,\n    method: context.method,\n    setHeader: (key, value) => {\n      headers.set(key, value);\n    },\n    getHeader: (key) => {\n      if (!requestHeaders) return null;\n      return requestHeaders.get(key);\n    },\n    getCookie: (key, prefix) => {\n      const finalKey = getCookieKey(key, prefix);\n      if (!finalKey) {\n        return null;\n      }\n      return parsedCookies?.get(finalKey) || null;\n    },\n    getSignedCookie: async (key, secret, prefix) => {\n      const finalKey = getCookieKey(key, prefix);\n      if (!finalKey) {\n        return null;\n      }\n      const value = parsedCookies?.get(finalKey);\n      if (!value) {\n        return null;\n      }\n      const signatureStartPos = value.lastIndexOf(\".\");\n      if (signatureStartPos < 1) {\n        return null;\n      }\n      const signedValue = value.substring(0, signatureStartPos);\n      const signature = value.substring(signatureStartPos + 1);\n      if (signature.length !== 44 || !signature.endsWith(\"=\")) {\n        return null;\n      }\n      const secretKey = await getCryptoKey(secret);\n      const isVerified = await verifySignature(signature, signedValue, secretKey);\n      return isVerified ? signedValue : false;\n    },\n    setCookie: (key, value, options2) => {\n      const cookie = serializeCookie(key, value, options2);\n      headers.append(\"set-cookie\", cookie);\n      return cookie;\n    },\n    setSignedCookie: async (key, value, secret, options2) => {\n      const cookie = await serializeSignedCookie(key, value, secret, options2);\n      headers.append(\"set-cookie\", cookie);\n      return cookie;\n    },\n    redirect: (url) => {\n      headers.set(\"location\", url);\n      return new APIError(\"FOUND\", void 0, headers);\n    },\n    error: (status, body, headers2) => {\n      return new APIError(status, body, headers2);\n    },\n    json: (json, routerResponse) => {\n      if (!context.asResponse) {\n        return json;\n      }\n      return {\n        body: routerResponse?.body || json,\n        routerResponse,\n        _flag: \"json\"\n      };\n    },\n    responseHeaders: headers\n  };\n  for (const middleware of options.use || []) {\n    const response = await middleware({\n      ...internalContext,\n      returnHeaders: true,\n      asResponse: false\n    });\n    if (response.response) {\n      Object.assign(internalContext.context, response.response);\n    }\n    if (response.headers) {\n      response.headers.forEach((value, key) => {\n        internalContext.responseHeaders.set(key, value);\n      });\n    }\n  }\n  return internalContext;\n};\n\n// src/middleware.ts\nfunction createMiddleware(optionsOrHandler, handler) {\n  const internalHandler = async (inputCtx) => {\n    const context = inputCtx;\n    const _handler = typeof optionsOrHandler === \"function\" ? optionsOrHandler : handler;\n    const options = typeof optionsOrHandler === \"function\" ? {} : optionsOrHandler;\n    const internalContext = await createInternalContext(context, {\n      options,\n      path: \"/\"\n    });\n    if (!_handler) {\n      throw new Error(\"handler must be defined\");\n    }\n    const response = await _handler(internalContext);\n    const headers = internalContext.responseHeaders;\n    return context.returnHeaders ? {\n      headers,\n      response\n    } : response;\n  };\n  internalHandler.options = typeof optionsOrHandler === \"function\" ? {} : optionsOrHandler;\n  return internalHandler;\n}\ncreateMiddleware.create = (opts) => {\n  function fn(optionsOrHandler, handler) {\n    if (typeof optionsOrHandler === \"function\") {\n      return createMiddleware(\n        {\n          use: opts?.use\n        },\n        optionsOrHandler\n      );\n    }\n    if (!handler) {\n      throw new Error(\"Middleware handler is required\");\n    }\n    const middleware = createMiddleware(\n      {\n        ...optionsOrHandler,\n        method: \"*\",\n        use: [...opts?.use || [], ...optionsOrHandler.use || []]\n      },\n      handler\n    );\n    return middleware;\n  }\n  return fn;\n};\n\n// src/endpoint.ts\nvar createEndpoint2 = (path, options, handler) => {\n  const internalHandler = async (...inputCtx) => {\n    const context = inputCtx[0] || {};\n    const internalContext = await createInternalContext(context, {\n      options,\n      path\n    });\n    const response = await handler(internalContext).catch(async (e) => {\n      if (isAPIError(e)) {\n        const onAPIError = options.onAPIError;\n        if (onAPIError) {\n          await onAPIError(e);\n        }\n        if (context.asResponse) {\n          return e;\n        }\n      }\n      throw e;\n    });\n    const headers = internalContext.responseHeaders;\n    return context.asResponse ? toResponse(response, {\n      headers\n    }) : context.returnHeaders ? {\n      headers,\n      response\n    } : response;\n  };\n  internalHandler.options = options;\n  internalHandler.path = path;\n  return internalHandler;\n};\ncreateEndpoint2.create = (opts) => {\n  return (path, options, handler) => {\n    return createEndpoint2(\n      path,\n      {\n        ...options,\n        use: [...options?.use || [], ...opts?.use || []]\n      },\n      handler\n    );\n  };\n};\n\n// src/router.ts\n\n\n// node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\nvar util;\n(function(util2) {\n  util2.assertEqual = (val) => val;\n  function assertIs(_arg) {\n  }\n  util2.assertIs = assertIs;\n  function assertNever(_x) {\n    throw new Error();\n  }\n  util2.assertNever = assertNever;\n  util2.arrayToEnum = (items) => {\n    const obj = {};\n    for (const item of items) {\n      obj[item] = item;\n    }\n    return obj;\n  };\n  util2.getValidEnumValues = (obj) => {\n    const validKeys = util2.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== \"number\");\n    const filtered = {};\n    for (const k of validKeys) {\n      filtered[k] = obj[k];\n    }\n    return util2.objectValues(filtered);\n  };\n  util2.objectValues = (obj) => {\n    return util2.objectKeys(obj).map(function(e) {\n      return obj[e];\n    });\n  };\n  util2.objectKeys = typeof Object.keys === \"function\" ? (obj) => Object.keys(obj) : (object) => {\n    const keys = [];\n    for (const key in object) {\n      if (Object.prototype.hasOwnProperty.call(object, key)) {\n        keys.push(key);\n      }\n    }\n    return keys;\n  };\n  util2.find = (arr, checker) => {\n    for (const item of arr) {\n      if (checker(item))\n        return item;\n    }\n    return void 0;\n  };\n  util2.isInteger = typeof Number.isInteger === \"function\" ? (val) => Number.isInteger(val) : (val) => typeof val === \"number\" && isFinite(val) && Math.floor(val) === val;\n  function joinValues(array, separator = \" | \") {\n    return array.map((val) => typeof val === \"string\" ? `'${val}'` : val).join(separator);\n  }\n  util2.joinValues = joinValues;\n  util2.jsonStringifyReplacer = (_, value) => {\n    if (typeof value === \"bigint\") {\n      return value.toString();\n    }\n    return value;\n  };\n})(util || (util = {}));\nvar objectUtil;\n(function(objectUtil2) {\n  objectUtil2.mergeShapes = (first, second) => {\n    return {\n      ...first,\n      ...second\n      // second overwrites first\n    };\n  };\n})(objectUtil || (objectUtil = {}));\nvar ZodParsedType = util.arrayToEnum([\n  \"string\",\n  \"nan\",\n  \"number\",\n  \"integer\",\n  \"float\",\n  \"boolean\",\n  \"date\",\n  \"bigint\",\n  \"symbol\",\n  \"function\",\n  \"undefined\",\n  \"null\",\n  \"array\",\n  \"object\",\n  \"unknown\",\n  \"promise\",\n  \"void\",\n  \"never\",\n  \"map\",\n  \"set\"\n]);\nvar getParsedType = (data) => {\n  const t = typeof data;\n  switch (t) {\n    case \"undefined\":\n      return ZodParsedType.undefined;\n    case \"string\":\n      return ZodParsedType.string;\n    case \"number\":\n      return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;\n    case \"boolean\":\n      return ZodParsedType.boolean;\n    case \"function\":\n      return ZodParsedType.function;\n    case \"bigint\":\n      return ZodParsedType.bigint;\n    case \"symbol\":\n      return ZodParsedType.symbol;\n    case \"object\":\n      if (Array.isArray(data)) {\n        return ZodParsedType.array;\n      }\n      if (data === null) {\n        return ZodParsedType.null;\n      }\n      if (data.then && typeof data.then === \"function\" && data.catch && typeof data.catch === \"function\") {\n        return ZodParsedType.promise;\n      }\n      if (typeof Map !== \"undefined\" && data instanceof Map) {\n        return ZodParsedType.map;\n      }\n      if (typeof Set !== \"undefined\" && data instanceof Set) {\n        return ZodParsedType.set;\n      }\n      if (typeof Date !== \"undefined\" && data instanceof Date) {\n        return ZodParsedType.date;\n      }\n      return ZodParsedType.object;\n    default:\n      return ZodParsedType.unknown;\n  }\n};\nvar ZodIssueCode = util.arrayToEnum([\n  \"invalid_type\",\n  \"invalid_literal\",\n  \"custom\",\n  \"invalid_union\",\n  \"invalid_union_discriminator\",\n  \"invalid_enum_value\",\n  \"unrecognized_keys\",\n  \"invalid_arguments\",\n  \"invalid_return_type\",\n  \"invalid_date\",\n  \"invalid_string\",\n  \"too_small\",\n  \"too_big\",\n  \"invalid_intersection_types\",\n  \"not_multiple_of\",\n  \"not_finite\"\n]);\nvar ZodError = class _ZodError extends Error {\n  get errors() {\n    return this.issues;\n  }\n  constructor(issues) {\n    super();\n    this.issues = [];\n    this.addIssue = (sub) => {\n      this.issues = [...this.issues, sub];\n    };\n    this.addIssues = (subs = []) => {\n      this.issues = [...this.issues, ...subs];\n    };\n    const actualProto = new.target.prototype;\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(this, actualProto);\n    } else {\n      this.__proto__ = actualProto;\n    }\n    this.name = \"ZodError\";\n    this.issues = issues;\n  }\n  format(_mapper) {\n    const mapper = _mapper || function(issue) {\n      return issue.message;\n    };\n    const fieldErrors = { _errors: [] };\n    const processError = (error) => {\n      for (const issue of error.issues) {\n        if (issue.code === \"invalid_union\") {\n          issue.unionErrors.map(processError);\n        } else if (issue.code === \"invalid_return_type\") {\n          processError(issue.returnTypeError);\n        } else if (issue.code === \"invalid_arguments\") {\n          processError(issue.argumentsError);\n        } else if (issue.path.length === 0) {\n          fieldErrors._errors.push(mapper(issue));\n        } else {\n          let curr = fieldErrors;\n          let i = 0;\n          while (i < issue.path.length) {\n            const el = issue.path[i];\n            const terminal = i === issue.path.length - 1;\n            if (!terminal) {\n              curr[el] = curr[el] || { _errors: [] };\n            } else {\n              curr[el] = curr[el] || { _errors: [] };\n              curr[el]._errors.push(mapper(issue));\n            }\n            curr = curr[el];\n            i++;\n          }\n        }\n      }\n    };\n    processError(this);\n    return fieldErrors;\n  }\n  static assert(value) {\n    if (!(value instanceof _ZodError)) {\n      throw new Error(`Not a ZodError: ${value}`);\n    }\n  }\n  toString() {\n    return this.message;\n  }\n  get message() {\n    return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);\n  }\n  get isEmpty() {\n    return this.issues.length === 0;\n  }\n  flatten(mapper = (issue) => issue.message) {\n    const fieldErrors = {};\n    const formErrors = [];\n    for (const sub of this.issues) {\n      if (sub.path.length > 0) {\n        fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n        fieldErrors[sub.path[0]].push(mapper(sub));\n      } else {\n        formErrors.push(mapper(sub));\n      }\n    }\n    return { formErrors, fieldErrors };\n  }\n  get formErrors() {\n    return this.flatten();\n  }\n};\nZodError.create = (issues) => {\n  const error = new ZodError(issues);\n  return error;\n};\nvar errorMap = (issue, _ctx) => {\n  let message;\n  switch (issue.code) {\n    case ZodIssueCode.invalid_type:\n      if (issue.received === ZodParsedType.undefined) {\n        message = \"Required\";\n      } else {\n        message = `Expected ${issue.expected}, received ${issue.received}`;\n      }\n      break;\n    case ZodIssueCode.invalid_literal:\n      message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;\n      break;\n    case ZodIssueCode.unrecognized_keys:\n      message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, \", \")}`;\n      break;\n    case ZodIssueCode.invalid_union:\n      message = `Invalid input`;\n      break;\n    case ZodIssueCode.invalid_union_discriminator:\n      message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;\n      break;\n    case ZodIssueCode.invalid_enum_value:\n      message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;\n      break;\n    case ZodIssueCode.invalid_arguments:\n      message = `Invalid function arguments`;\n      break;\n    case ZodIssueCode.invalid_return_type:\n      message = `Invalid function return type`;\n      break;\n    case ZodIssueCode.invalid_date:\n      message = `Invalid date`;\n      break;\n    case ZodIssueCode.invalid_string:\n      if (typeof issue.validation === \"object\") {\n        if (\"includes\" in issue.validation) {\n          message = `Invalid input: must include \"${issue.validation.includes}\"`;\n          if (typeof issue.validation.position === \"number\") {\n            message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;\n          }\n        } else if (\"startsWith\" in issue.validation) {\n          message = `Invalid input: must start with \"${issue.validation.startsWith}\"`;\n        } else if (\"endsWith\" in issue.validation) {\n          message = `Invalid input: must end with \"${issue.validation.endsWith}\"`;\n        } else {\n          util.assertNever(issue.validation);\n        }\n      } else if (issue.validation !== \"regex\") {\n        message = `Invalid ${issue.validation}`;\n      } else {\n        message = \"Invalid\";\n      }\n      break;\n    case ZodIssueCode.too_small:\n      if (issue.type === \"array\")\n        message = `Array must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;\n      else if (issue.type === \"string\")\n        message = `String must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;\n      else if (issue.type === \"number\")\n        message = `Number must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${issue.minimum}`;\n      else if (issue.type === \"date\")\n        message = `Date must be ${issue.exact ? `exactly equal to ` : issue.inclusive ? `greater than or equal to ` : `greater than `}${new Date(Number(issue.minimum))}`;\n      else\n        message = \"Invalid input\";\n      break;\n    case ZodIssueCode.too_big:\n      if (issue.type === \"array\")\n        message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;\n      else if (issue.type === \"string\")\n        message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;\n      else if (issue.type === \"number\")\n        message = `Number must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;\n      else if (issue.type === \"bigint\")\n        message = `BigInt must be ${issue.exact ? `exactly` : issue.inclusive ? `less than or equal to` : `less than`} ${issue.maximum}`;\n      else if (issue.type === \"date\")\n        message = `Date must be ${issue.exact ? `exactly` : issue.inclusive ? `smaller than or equal to` : `smaller than`} ${new Date(Number(issue.maximum))}`;\n      else\n        message = \"Invalid input\";\n      break;\n    case ZodIssueCode.custom:\n      message = `Invalid input`;\n      break;\n    case ZodIssueCode.invalid_intersection_types:\n      message = `Intersection results could not be merged`;\n      break;\n    case ZodIssueCode.not_multiple_of:\n      message = `Number must be a multiple of ${issue.multipleOf}`;\n      break;\n    case ZodIssueCode.not_finite:\n      message = \"Number must be finite\";\n      break;\n    default:\n      message = _ctx.defaultError;\n      util.assertNever(issue);\n  }\n  return { message };\n};\nvar overrideErrorMap = errorMap;\nfunction getErrorMap() {\n  return overrideErrorMap;\n}\nvar makeIssue = (params) => {\n  const { data, path, errorMaps, issueData } = params;\n  const fullPath = [...path, ...issueData.path || []];\n  const fullIssue = {\n    ...issueData,\n    path: fullPath\n  };\n  if (issueData.message !== void 0) {\n    return {\n      ...issueData,\n      path: fullPath,\n      message: issueData.message\n    };\n  }\n  let errorMessage = \"\";\n  const maps = errorMaps.filter((m) => !!m).slice().reverse();\n  for (const map of maps) {\n    errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;\n  }\n  return {\n    ...issueData,\n    path: fullPath,\n    message: errorMessage\n  };\n};\nfunction addIssueToContext(ctx, issueData) {\n  const overrideMap = getErrorMap();\n  const issue = makeIssue({\n    issueData,\n    data: ctx.data,\n    path: ctx.path,\n    errorMaps: [\n      ctx.common.contextualErrorMap,\n      // contextual error map is first priority\n      ctx.schemaErrorMap,\n      // then schema-bound map if available\n      overrideMap,\n      // then global override map\n      overrideMap === errorMap ? void 0 : errorMap\n      // then global default map\n    ].filter((x) => !!x)\n  });\n  ctx.common.issues.push(issue);\n}\nvar ParseStatus = class _ParseStatus {\n  constructor() {\n    this.value = \"valid\";\n  }\n  dirty() {\n    if (this.value === \"valid\")\n      this.value = \"dirty\";\n  }\n  abort() {\n    if (this.value !== \"aborted\")\n      this.value = \"aborted\";\n  }\n  static mergeArray(status, results) {\n    const arrayValue = [];\n    for (const s of results) {\n      if (s.status === \"aborted\")\n        return INVALID;\n      if (s.status === \"dirty\")\n        status.dirty();\n      arrayValue.push(s.value);\n    }\n    return { status: status.value, value: arrayValue };\n  }\n  static async mergeObjectAsync(status, pairs) {\n    const syncPairs = [];\n    for (const pair of pairs) {\n      const key = await pair.key;\n      const value = await pair.value;\n      syncPairs.push({\n        key,\n        value\n      });\n    }\n    return _ParseStatus.mergeObjectSync(status, syncPairs);\n  }\n  static mergeObjectSync(status, pairs) {\n    const finalObject = {};\n    for (const pair of pairs) {\n      const { key, value } = pair;\n      if (key.status === \"aborted\")\n        return INVALID;\n      if (value.status === \"aborted\")\n        return INVALID;\n      if (key.status === \"dirty\")\n        status.dirty();\n      if (value.status === \"dirty\")\n        status.dirty();\n      if (key.value !== \"__proto__\" && (typeof value.value !== \"undefined\" || pair.alwaysSet)) {\n        finalObject[key.value] = value.value;\n      }\n    }\n    return { status: status.value, value: finalObject };\n  }\n};\nvar INVALID = Object.freeze({\n  status: \"aborted\"\n});\nvar DIRTY = (value) => ({ status: \"dirty\", value });\nvar OK = (value) => ({ status: \"valid\", value });\nvar isAborted = (x) => x.status === \"aborted\";\nvar isDirty = (x) => x.status === \"dirty\";\nvar isValid = (x) => x.status === \"valid\";\nvar isAsync = (x) => typeof Promise !== \"undefined\" && x instanceof Promise;\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n}\nvar errorUtil;\n(function(errorUtil2) {\n  errorUtil2.errToObj = (message) => typeof message === \"string\" ? { message } : message || {};\n  errorUtil2.toString = (message) => typeof message === \"string\" ? message : message === null || message === void 0 ? void 0 : message.message;\n})(errorUtil || (errorUtil = {}));\nvar _ZodEnum_cache;\nvar _ZodNativeEnum_cache;\nvar ParseInputLazyPath = class {\n  constructor(parent, value, path, key) {\n    this._cachedPath = [];\n    this.parent = parent;\n    this.data = value;\n    this._path = path;\n    this._key = key;\n  }\n  get path() {\n    if (!this._cachedPath.length) {\n      if (this._key instanceof Array) {\n        this._cachedPath.push(...this._path, ...this._key);\n      } else {\n        this._cachedPath.push(...this._path, this._key);\n      }\n    }\n    return this._cachedPath;\n  }\n};\nvar handleResult = (ctx, result) => {\n  if (isValid(result)) {\n    return { success: true, data: result.value };\n  } else {\n    if (!ctx.common.issues.length) {\n      throw new Error(\"Validation failed but no issues detected.\");\n    }\n    return {\n      success: false,\n      get error() {\n        if (this._error)\n          return this._error;\n        const error = new ZodError(ctx.common.issues);\n        this._error = error;\n        return this._error;\n      }\n    };\n  }\n};\nfunction processCreateParams(params) {\n  if (!params)\n    return {};\n  const { errorMap: errorMap2, invalid_type_error, required_error, description } = params;\n  if (errorMap2 && (invalid_type_error || required_error)) {\n    throw new Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`);\n  }\n  if (errorMap2)\n    return { errorMap: errorMap2, description };\n  const customMap = (iss, ctx) => {\n    var _a, _b;\n    const { message } = params;\n    if (iss.code === \"invalid_enum_value\") {\n      return { message: message !== null && message !== void 0 ? message : ctx.defaultError };\n    }\n    if (typeof ctx.data === \"undefined\") {\n      return { message: (_a = message !== null && message !== void 0 ? message : required_error) !== null && _a !== void 0 ? _a : ctx.defaultError };\n    }\n    if (iss.code !== \"invalid_type\")\n      return { message: ctx.defaultError };\n    return { message: (_b = message !== null && message !== void 0 ? message : invalid_type_error) !== null && _b !== void 0 ? _b : ctx.defaultError };\n  };\n  return { errorMap: customMap, description };\n}\nvar ZodType = class {\n  get description() {\n    return this._def.description;\n  }\n  _getType(input) {\n    return getParsedType(input.data);\n  }\n  _getOrReturnCtx(input, ctx) {\n    return ctx || {\n      common: input.parent.common,\n      data: input.data,\n      parsedType: getParsedType(input.data),\n      schemaErrorMap: this._def.errorMap,\n      path: input.path,\n      parent: input.parent\n    };\n  }\n  _processInputParams(input) {\n    return {\n      status: new ParseStatus(),\n      ctx: {\n        common: input.parent.common,\n        data: input.data,\n        parsedType: getParsedType(input.data),\n        schemaErrorMap: this._def.errorMap,\n        path: input.path,\n        parent: input.parent\n      }\n    };\n  }\n  _parseSync(input) {\n    const result = this._parse(input);\n    if (isAsync(result)) {\n      throw new Error(\"Synchronous parse encountered promise.\");\n    }\n    return result;\n  }\n  _parseAsync(input) {\n    const result = this._parse(input);\n    return Promise.resolve(result);\n  }\n  parse(data, params) {\n    const result = this.safeParse(data, params);\n    if (result.success)\n      return result.data;\n    throw result.error;\n  }\n  safeParse(data, params) {\n    var _a;\n    const ctx = {\n      common: {\n        issues: [],\n        async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,\n        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap\n      },\n      path: (params === null || params === void 0 ? void 0 : params.path) || [],\n      schemaErrorMap: this._def.errorMap,\n      parent: null,\n      data,\n      parsedType: getParsedType(data)\n    };\n    const result = this._parseSync({ data, path: ctx.path, parent: ctx });\n    return handleResult(ctx, result);\n  }\n  \"~validate\"(data) {\n    var _a, _b;\n    const ctx = {\n      common: {\n        issues: [],\n        async: !!this[\"~standard\"].async\n      },\n      path: [],\n      schemaErrorMap: this._def.errorMap,\n      parent: null,\n      data,\n      parsedType: getParsedType(data)\n    };\n    if (!this[\"~standard\"].async) {\n      try {\n        const result = this._parseSync({ data, path: [], parent: ctx });\n        return isValid(result) ? {\n          value: result.value\n        } : {\n          issues: ctx.common.issues\n        };\n      } catch (err) {\n        if ((_b = (_a = err === null || err === void 0 ? void 0 : err.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.includes(\"encountered\")) {\n          this[\"~standard\"].async = true;\n        }\n        ctx.common = {\n          issues: [],\n          async: true\n        };\n      }\n    }\n    return this._parseAsync({ data, path: [], parent: ctx }).then((result) => isValid(result) ? {\n      value: result.value\n    } : {\n      issues: ctx.common.issues\n    });\n  }\n  async parseAsync(data, params) {\n    const result = await this.safeParseAsync(data, params);\n    if (result.success)\n      return result.data;\n    throw result.error;\n  }\n  async safeParseAsync(data, params) {\n    const ctx = {\n      common: {\n        issues: [],\n        contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n        async: true\n      },\n      path: (params === null || params === void 0 ? void 0 : params.path) || [],\n      schemaErrorMap: this._def.errorMap,\n      parent: null,\n      data,\n      parsedType: getParsedType(data)\n    };\n    const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });\n    const result = await (isAsync(maybeAsyncResult) ? maybeAsyncResult : Promise.resolve(maybeAsyncResult));\n    return handleResult(ctx, result);\n  }\n  refine(check, message) {\n    const getIssueProperties = (val) => {\n      if (typeof message === \"string\" || typeof message === \"undefined\") {\n        return { message };\n      } else if (typeof message === \"function\") {\n        return message(val);\n      } else {\n        return message;\n      }\n    };\n    return this._refinement((val, ctx) => {\n      const result = check(val);\n      const setError = () => ctx.addIssue({\n        code: ZodIssueCode.custom,\n        ...getIssueProperties(val)\n      });\n      if (typeof Promise !== \"undefined\" && result instanceof Promise) {\n        return result.then((data) => {\n          if (!data) {\n            setError();\n            return false;\n          } else {\n            return true;\n          }\n        });\n      }\n      if (!result) {\n        setError();\n        return false;\n      } else {\n        return true;\n      }\n    });\n  }\n  refinement(check, refinementData) {\n    return this._refinement((val, ctx) => {\n      if (!check(val)) {\n        ctx.addIssue(typeof refinementData === \"function\" ? refinementData(val, ctx) : refinementData);\n        return false;\n      } else {\n        return true;\n      }\n    });\n  }\n  _refinement(refinement) {\n    return new ZodEffects({\n      schema: this,\n      typeName: ZodFirstPartyTypeKind.ZodEffects,\n      effect: { type: \"refinement\", refinement }\n    });\n  }\n  superRefine(refinement) {\n    return this._refinement(refinement);\n  }\n  constructor(def) {\n    this.spa = this.safeParseAsync;\n    this._def = def;\n    this.parse = this.parse.bind(this);\n    this.safeParse = this.safeParse.bind(this);\n    this.parseAsync = this.parseAsync.bind(this);\n    this.safeParseAsync = this.safeParseAsync.bind(this);\n    this.spa = this.spa.bind(this);\n    this.refine = this.refine.bind(this);\n    this.refinement = this.refinement.bind(this);\n    this.superRefine = this.superRefine.bind(this);\n    this.optional = this.optional.bind(this);\n    this.nullable = this.nullable.bind(this);\n    this.nullish = this.nullish.bind(this);\n    this.array = this.array.bind(this);\n    this.promise = this.promise.bind(this);\n    this.or = this.or.bind(this);\n    this.and = this.and.bind(this);\n    this.transform = this.transform.bind(this);\n    this.brand = this.brand.bind(this);\n    this.default = this.default.bind(this);\n    this.catch = this.catch.bind(this);\n    this.describe = this.describe.bind(this);\n    this.pipe = this.pipe.bind(this);\n    this.readonly = this.readonly.bind(this);\n    this.isNullable = this.isNullable.bind(this);\n    this.isOptional = this.isOptional.bind(this);\n    this[\"~standard\"] = {\n      version: 1,\n      vendor: \"zod\",\n      validate: (data) => this[\"~validate\"](data)\n    };\n  }\n  optional() {\n    return ZodOptional.create(this, this._def);\n  }\n  nullable() {\n    return ZodNullable.create(this, this._def);\n  }\n  nullish() {\n    return this.nullable().optional();\n  }\n  array() {\n    return ZodArray.create(this);\n  }\n  promise() {\n    return ZodPromise.create(this, this._def);\n  }\n  or(option) {\n    return ZodUnion.create([this, option], this._def);\n  }\n  and(incoming) {\n    return ZodIntersection.create(this, incoming, this._def);\n  }\n  transform(transform) {\n    return new ZodEffects({\n      ...processCreateParams(this._def),\n      schema: this,\n      typeName: ZodFirstPartyTypeKind.ZodEffects,\n      effect: { type: \"transform\", transform }\n    });\n  }\n  default(def) {\n    const defaultValueFunc = typeof def === \"function\" ? def : () => def;\n    return new ZodDefault({\n      ...processCreateParams(this._def),\n      innerType: this,\n      defaultValue: defaultValueFunc,\n      typeName: ZodFirstPartyTypeKind.ZodDefault\n    });\n  }\n  brand() {\n    return new ZodBranded({\n      typeName: ZodFirstPartyTypeKind.ZodBranded,\n      type: this,\n      ...processCreateParams(this._def)\n    });\n  }\n  catch(def) {\n    const catchValueFunc = typeof def === \"function\" ? def : () => def;\n    return new ZodCatch({\n      ...processCreateParams(this._def),\n      innerType: this,\n      catchValue: catchValueFunc,\n      typeName: ZodFirstPartyTypeKind.ZodCatch\n    });\n  }\n  describe(description) {\n    const This = this.constructor;\n    return new This({\n      ...this._def,\n      description\n    });\n  }\n  pipe(target) {\n    return ZodPipeline.create(this, target);\n  }\n  readonly() {\n    return ZodReadonly.create(this);\n  }\n  isOptional() {\n    return this.safeParse(void 0).success;\n  }\n  isNullable() {\n    return this.safeParse(null).success;\n  }\n};\nvar cuidRegex = /^c[^\\s-]{8,}$/i;\nvar cuid2Regex = /^[0-9a-z]+$/;\nvar ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;\nvar uuidRegex = /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/i;\nvar nanoidRegex = /^[a-z0-9_-]{21}$/i;\nvar jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\nvar durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\\d+Y)|(?:[-+]?\\d+[.,]\\d+Y$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:(?:[-+]?\\d+W)|(?:[-+]?\\d+[.,]\\d+W$))?(?:(?:[-+]?\\d+D)|(?:[-+]?\\d+[.,]\\d+D$))?(?:T(?=[\\d+-])(?:(?:[-+]?\\d+H)|(?:[-+]?\\d+[.,]\\d+H$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:[-+]?\\d+(?:[.,]\\d+)?S)?)??$/;\nvar emailRegex = /^(?!\\.)(?!.*\\.\\.)([A-Z0-9_'+\\-\\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\\-]*\\.)+[A-Z]{2,}$/i;\nvar _emojiRegex = `^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$`;\nvar emojiRegex;\nvar ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;\nvar ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/;\nvar ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;\nvar ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;\nvar base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;\nvar base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;\nvar dateRegexSource = `((\\\\d\\\\d[2468][048]|\\\\d\\\\d[13579][26]|\\\\d\\\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\\\d|30)|(02)-(0[1-9]|1\\\\d|2[0-8])))`;\nvar dateRegex = new RegExp(`^${dateRegexSource}$`);\nfunction timeRegexSource(args) {\n  let regex = `([01]\\\\d|2[0-3]):[0-5]\\\\d:[0-5]\\\\d`;\n  if (args.precision) {\n    regex = `${regex}\\\\.\\\\d{${args.precision}}`;\n  } else if (args.precision == null) {\n    regex = `${regex}(\\\\.\\\\d+)?`;\n  }\n  return regex;\n}\nfunction timeRegex(args) {\n  return new RegExp(`^${timeRegexSource(args)}$`);\n}\nfunction datetimeRegex(args) {\n  let regex = `${dateRegexSource}T${timeRegexSource(args)}`;\n  const opts = [];\n  opts.push(args.local ? `Z?` : `Z`);\n  if (args.offset)\n    opts.push(`([+-]\\\\d{2}:?\\\\d{2})`);\n  regex = `${regex}(${opts.join(\"|\")})`;\n  return new RegExp(`^${regex}$`);\n}\nfunction isValidIP(ip, version) {\n  if ((version === \"v4\" || !version) && ipv4Regex.test(ip)) {\n    return true;\n  }\n  if ((version === \"v6\" || !version) && ipv6Regex.test(ip)) {\n    return true;\n  }\n  return false;\n}\nfunction isValidJWT(jwt, alg) {\n  if (!jwtRegex.test(jwt))\n    return false;\n  try {\n    const [header] = jwt.split(\".\");\n    const base64 = header.replace(/-/g, \"+\").replace(/_/g, \"/\").padEnd(header.length + (4 - header.length % 4) % 4, \"=\");\n    const decoded = JSON.parse(atob(base64));\n    if (typeof decoded !== \"object\" || decoded === null)\n      return false;\n    if (!decoded.typ || !decoded.alg)\n      return false;\n    if (alg && decoded.alg !== alg)\n      return false;\n    return true;\n  } catch (_a) {\n    return false;\n  }\n}\nfunction isValidCidr(ip, version) {\n  if ((version === \"v4\" || !version) && ipv4CidrRegex.test(ip)) {\n    return true;\n  }\n  if ((version === \"v6\" || !version) && ipv6CidrRegex.test(ip)) {\n    return true;\n  }\n  return false;\n}\nvar ZodString = class _ZodString extends ZodType {\n  _parse(input) {\n    if (this._def.coerce) {\n      input.data = String(input.data);\n    }\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.string) {\n      const ctx2 = this._getOrReturnCtx(input);\n      addIssueToContext(ctx2, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.string,\n        received: ctx2.parsedType\n      });\n      return INVALID;\n    }\n    const status = new ParseStatus();\n    let ctx = void 0;\n    for (const check of this._def.checks) {\n      if (check.kind === \"min\") {\n        if (input.data.length < check.value) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_small,\n            minimum: check.value,\n            type: \"string\",\n            inclusive: true,\n            exact: false,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"max\") {\n        if (input.data.length > check.value) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_big,\n            maximum: check.value,\n            type: \"string\",\n            inclusive: true,\n            exact: false,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"length\") {\n        const tooBig = input.data.length > check.value;\n        const tooSmall = input.data.length < check.value;\n        if (tooBig || tooSmall) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          if (tooBig) {\n            addIssueToContext(ctx, {\n              code: ZodIssueCode.too_big,\n              maximum: check.value,\n              type: \"string\",\n              inclusive: true,\n              exact: true,\n              message: check.message\n            });\n          } else if (tooSmall) {\n            addIssueToContext(ctx, {\n              code: ZodIssueCode.too_small,\n              minimum: check.value,\n              type: \"string\",\n              inclusive: true,\n              exact: true,\n              message: check.message\n            });\n          }\n          status.dirty();\n        }\n      } else if (check.kind === \"email\") {\n        if (!emailRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"email\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"emoji\") {\n        if (!emojiRegex) {\n          emojiRegex = new RegExp(_emojiRegex, \"u\");\n        }\n        if (!emojiRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"emoji\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"uuid\") {\n        if (!uuidRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"uuid\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"nanoid\") {\n        if (!nanoidRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"nanoid\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"cuid\") {\n        if (!cuidRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"cuid\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"cuid2\") {\n        if (!cuid2Regex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"cuid2\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"ulid\") {\n        if (!ulidRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"ulid\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"url\") {\n        try {\n          new URL(input.data);\n        } catch (_a) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"url\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"regex\") {\n        check.regex.lastIndex = 0;\n        const testResult = check.regex.test(input.data);\n        if (!testResult) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"regex\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"trim\") {\n        input.data = input.data.trim();\n      } else if (check.kind === \"includes\") {\n        if (!input.data.includes(check.value, check.position)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: { includes: check.value, position: check.position },\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"toLowerCase\") {\n        input.data = input.data.toLowerCase();\n      } else if (check.kind === \"toUpperCase\") {\n        input.data = input.data.toUpperCase();\n      } else if (check.kind === \"startsWith\") {\n        if (!input.data.startsWith(check.value)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: { startsWith: check.value },\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"endsWith\") {\n        if (!input.data.endsWith(check.value)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: { endsWith: check.value },\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"datetime\") {\n        const regex = datetimeRegex(check);\n        if (!regex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: \"datetime\",\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"date\") {\n        const regex = dateRegex;\n        if (!regex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: \"date\",\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"time\") {\n        const regex = timeRegex(check);\n        if (!regex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_string,\n            validation: \"time\",\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"duration\") {\n        if (!durationRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"duration\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"ip\") {\n        if (!isValidIP(input.data, check.version)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"ip\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"jwt\") {\n        if (!isValidJWT(input.data, check.alg)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"jwt\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"cidr\") {\n        if (!isValidCidr(input.data, check.version)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"cidr\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"base64\") {\n        if (!base64Regex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"base64\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"base64url\") {\n        if (!base64urlRegex.test(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            validation: \"base64url\",\n            code: ZodIssueCode.invalid_string,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else {\n        util.assertNever(check);\n      }\n    }\n    return { status: status.value, value: input.data };\n  }\n  _regex(regex, validation, message) {\n    return this.refinement((data) => regex.test(data), {\n      validation,\n      code: ZodIssueCode.invalid_string,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  _addCheck(check) {\n    return new _ZodString({\n      ...this._def,\n      checks: [...this._def.checks, check]\n    });\n  }\n  email(message) {\n    return this._addCheck({ kind: \"email\", ...errorUtil.errToObj(message) });\n  }\n  url(message) {\n    return this._addCheck({ kind: \"url\", ...errorUtil.errToObj(message) });\n  }\n  emoji(message) {\n    return this._addCheck({ kind: \"emoji\", ...errorUtil.errToObj(message) });\n  }\n  uuid(message) {\n    return this._addCheck({ kind: \"uuid\", ...errorUtil.errToObj(message) });\n  }\n  nanoid(message) {\n    return this._addCheck({ kind: \"nanoid\", ...errorUtil.errToObj(message) });\n  }\n  cuid(message) {\n    return this._addCheck({ kind: \"cuid\", ...errorUtil.errToObj(message) });\n  }\n  cuid2(message) {\n    return this._addCheck({ kind: \"cuid2\", ...errorUtil.errToObj(message) });\n  }\n  ulid(message) {\n    return this._addCheck({ kind: \"ulid\", ...errorUtil.errToObj(message) });\n  }\n  base64(message) {\n    return this._addCheck({ kind: \"base64\", ...errorUtil.errToObj(message) });\n  }\n  base64url(message) {\n    return this._addCheck({\n      kind: \"base64url\",\n      ...errorUtil.errToObj(message)\n    });\n  }\n  jwt(options) {\n    return this._addCheck({ kind: \"jwt\", ...errorUtil.errToObj(options) });\n  }\n  ip(options) {\n    return this._addCheck({ kind: \"ip\", ...errorUtil.errToObj(options) });\n  }\n  cidr(options) {\n    return this._addCheck({ kind: \"cidr\", ...errorUtil.errToObj(options) });\n  }\n  datetime(options) {\n    var _a, _b;\n    if (typeof options === \"string\") {\n      return this._addCheck({\n        kind: \"datetime\",\n        precision: null,\n        offset: false,\n        local: false,\n        message: options\n      });\n    }\n    return this._addCheck({\n      kind: \"datetime\",\n      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n      offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,\n      local: (_b = options === null || options === void 0 ? void 0 : options.local) !== null && _b !== void 0 ? _b : false,\n      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)\n    });\n  }\n  date(message) {\n    return this._addCheck({ kind: \"date\", message });\n  }\n  time(options) {\n    if (typeof options === \"string\") {\n      return this._addCheck({\n        kind: \"time\",\n        precision: null,\n        message: options\n      });\n    }\n    return this._addCheck({\n      kind: \"time\",\n      precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)\n    });\n  }\n  duration(message) {\n    return this._addCheck({ kind: \"duration\", ...errorUtil.errToObj(message) });\n  }\n  regex(regex, message) {\n    return this._addCheck({\n      kind: \"regex\",\n      regex,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  includes(value, options) {\n    return this._addCheck({\n      kind: \"includes\",\n      value,\n      position: options === null || options === void 0 ? void 0 : options.position,\n      ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message)\n    });\n  }\n  startsWith(value, message) {\n    return this._addCheck({\n      kind: \"startsWith\",\n      value,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  endsWith(value, message) {\n    return this._addCheck({\n      kind: \"endsWith\",\n      value,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  min(minLength, message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: minLength,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  max(maxLength, message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: maxLength,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  length(len, message) {\n    return this._addCheck({\n      kind: \"length\",\n      value: len,\n      ...errorUtil.errToObj(message)\n    });\n  }\n  /**\n   * Equivalent to `.min(1)`\n   */\n  nonempty(message) {\n    return this.min(1, errorUtil.errToObj(message));\n  }\n  trim() {\n    return new _ZodString({\n      ...this._def,\n      checks: [...this._def.checks, { kind: \"trim\" }]\n    });\n  }\n  toLowerCase() {\n    return new _ZodString({\n      ...this._def,\n      checks: [...this._def.checks, { kind: \"toLowerCase\" }]\n    });\n  }\n  toUpperCase() {\n    return new _ZodString({\n      ...this._def,\n      checks: [...this._def.checks, { kind: \"toUpperCase\" }]\n    });\n  }\n  get isDatetime() {\n    return !!this._def.checks.find((ch) => ch.kind === \"datetime\");\n  }\n  get isDate() {\n    return !!this._def.checks.find((ch) => ch.kind === \"date\");\n  }\n  get isTime() {\n    return !!this._def.checks.find((ch) => ch.kind === \"time\");\n  }\n  get isDuration() {\n    return !!this._def.checks.find((ch) => ch.kind === \"duration\");\n  }\n  get isEmail() {\n    return !!this._def.checks.find((ch) => ch.kind === \"email\");\n  }\n  get isURL() {\n    return !!this._def.checks.find((ch) => ch.kind === \"url\");\n  }\n  get isEmoji() {\n    return !!this._def.checks.find((ch) => ch.kind === \"emoji\");\n  }\n  get isUUID() {\n    return !!this._def.checks.find((ch) => ch.kind === \"uuid\");\n  }\n  get isNANOID() {\n    return !!this._def.checks.find((ch) => ch.kind === \"nanoid\");\n  }\n  get isCUID() {\n    return !!this._def.checks.find((ch) => ch.kind === \"cuid\");\n  }\n  get isCUID2() {\n    return !!this._def.checks.find((ch) => ch.kind === \"cuid2\");\n  }\n  get isULID() {\n    return !!this._def.checks.find((ch) => ch.kind === \"ulid\");\n  }\n  get isIP() {\n    return !!this._def.checks.find((ch) => ch.kind === \"ip\");\n  }\n  get isCIDR() {\n    return !!this._def.checks.find((ch) => ch.kind === \"cidr\");\n  }\n  get isBase64() {\n    return !!this._def.checks.find((ch) => ch.kind === \"base64\");\n  }\n  get isBase64url() {\n    return !!this._def.checks.find((ch) => ch.kind === \"base64url\");\n  }\n  get minLength() {\n    let min = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"min\") {\n        if (min === null || ch.value > min)\n          min = ch.value;\n      }\n    }\n    return min;\n  }\n  get maxLength() {\n    let max = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"max\") {\n        if (max === null || ch.value < max)\n          max = ch.value;\n      }\n    }\n    return max;\n  }\n};\nZodString.create = (params) => {\n  var _a;\n  return new ZodString({\n    checks: [],\n    typeName: ZodFirstPartyTypeKind.ZodString,\n    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n    ...processCreateParams(params)\n  });\n};\nfunction floatSafeRemainder(val, step) {\n  const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n  const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n  const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n  const valInt = parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n  const stepInt = parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n  return valInt % stepInt / Math.pow(10, decCount);\n}\nvar ZodNumber = class _ZodNumber extends ZodType {\n  constructor() {\n    super(...arguments);\n    this.min = this.gte;\n    this.max = this.lte;\n    this.step = this.multipleOf;\n  }\n  _parse(input) {\n    if (this._def.coerce) {\n      input.data = Number(input.data);\n    }\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.number) {\n      const ctx2 = this._getOrReturnCtx(input);\n      addIssueToContext(ctx2, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.number,\n        received: ctx2.parsedType\n      });\n      return INVALID;\n    }\n    let ctx = void 0;\n    const status = new ParseStatus();\n    for (const check of this._def.checks) {\n      if (check.kind === \"int\") {\n        if (!util.isInteger(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_type,\n            expected: \"integer\",\n            received: \"float\",\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"min\") {\n        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;\n        if (tooSmall) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_small,\n            minimum: check.value,\n            type: \"number\",\n            inclusive: check.inclusive,\n            exact: false,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"max\") {\n        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;\n        if (tooBig) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_big,\n            maximum: check.value,\n            type: \"number\",\n            inclusive: check.inclusive,\n            exact: false,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"multipleOf\") {\n        if (floatSafeRemainder(input.data, check.value) !== 0) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.not_multiple_of,\n            multipleOf: check.value,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"finite\") {\n        if (!Number.isFinite(input.data)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.not_finite,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else {\n        util.assertNever(check);\n      }\n    }\n    return { status: status.value, value: input.data };\n  }\n  gte(value, message) {\n    return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n  }\n  gt(value, message) {\n    return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n  }\n  lte(value, message) {\n    return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n  }\n  lt(value, message) {\n    return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n  }\n  setLimit(kind, value, inclusive, message) {\n    return new _ZodNumber({\n      ...this._def,\n      checks: [\n        ...this._def.checks,\n        {\n          kind,\n          value,\n          inclusive,\n          message: errorUtil.toString(message)\n        }\n      ]\n    });\n  }\n  _addCheck(check) {\n    return new _ZodNumber({\n      ...this._def,\n      checks: [...this._def.checks, check]\n    });\n  }\n  int(message) {\n    return this._addCheck({\n      kind: \"int\",\n      message: errorUtil.toString(message)\n    });\n  }\n  positive(message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: 0,\n      inclusive: false,\n      message: errorUtil.toString(message)\n    });\n  }\n  negative(message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: 0,\n      inclusive: false,\n      message: errorUtil.toString(message)\n    });\n  }\n  nonpositive(message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: 0,\n      inclusive: true,\n      message: errorUtil.toString(message)\n    });\n  }\n  nonnegative(message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: 0,\n      inclusive: true,\n      message: errorUtil.toString(message)\n    });\n  }\n  multipleOf(value, message) {\n    return this._addCheck({\n      kind: \"multipleOf\",\n      value,\n      message: errorUtil.toString(message)\n    });\n  }\n  finite(message) {\n    return this._addCheck({\n      kind: \"finite\",\n      message: errorUtil.toString(message)\n    });\n  }\n  safe(message) {\n    return this._addCheck({\n      kind: \"min\",\n      inclusive: true,\n      value: Number.MIN_SAFE_INTEGER,\n      message: errorUtil.toString(message)\n    })._addCheck({\n      kind: \"max\",\n      inclusive: true,\n      value: Number.MAX_SAFE_INTEGER,\n      message: errorUtil.toString(message)\n    });\n  }\n  get minValue() {\n    let min = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"min\") {\n        if (min === null || ch.value > min)\n          min = ch.value;\n      }\n    }\n    return min;\n  }\n  get maxValue() {\n    let max = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"max\") {\n        if (max === null || ch.value < max)\n          max = ch.value;\n      }\n    }\n    return max;\n  }\n  get isInt() {\n    return !!this._def.checks.find((ch) => ch.kind === \"int\" || ch.kind === \"multipleOf\" && util.isInteger(ch.value));\n  }\n  get isFinite() {\n    let max = null, min = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"finite\" || ch.kind === \"int\" || ch.kind === \"multipleOf\") {\n        return true;\n      } else if (ch.kind === \"min\") {\n        if (min === null || ch.value > min)\n          min = ch.value;\n      } else if (ch.kind === \"max\") {\n        if (max === null || ch.value < max)\n          max = ch.value;\n      }\n    }\n    return Number.isFinite(min) && Number.isFinite(max);\n  }\n};\nZodNumber.create = (params) => {\n  return new ZodNumber({\n    checks: [],\n    typeName: ZodFirstPartyTypeKind.ZodNumber,\n    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n    ...processCreateParams(params)\n  });\n};\nvar ZodBigInt = class _ZodBigInt extends ZodType {\n  constructor() {\n    super(...arguments);\n    this.min = this.gte;\n    this.max = this.lte;\n  }\n  _parse(input) {\n    if (this._def.coerce) {\n      try {\n        input.data = BigInt(input.data);\n      } catch (_a) {\n        return this._getInvalidInput(input);\n      }\n    }\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.bigint) {\n      return this._getInvalidInput(input);\n    }\n    let ctx = void 0;\n    const status = new ParseStatus();\n    for (const check of this._def.checks) {\n      if (check.kind === \"min\") {\n        const tooSmall = check.inclusive ? input.data < check.value : input.data <= check.value;\n        if (tooSmall) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_small,\n            type: \"bigint\",\n            minimum: check.value,\n            inclusive: check.inclusive,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"max\") {\n        const tooBig = check.inclusive ? input.data > check.value : input.data >= check.value;\n        if (tooBig) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_big,\n            type: \"bigint\",\n            maximum: check.value,\n            inclusive: check.inclusive,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"multipleOf\") {\n        if (input.data % check.value !== BigInt(0)) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.not_multiple_of,\n            multipleOf: check.value,\n            message: check.message\n          });\n          status.dirty();\n        }\n      } else {\n        util.assertNever(check);\n      }\n    }\n    return { status: status.value, value: input.data };\n  }\n  _getInvalidInput(input) {\n    const ctx = this._getOrReturnCtx(input);\n    addIssueToContext(ctx, {\n      code: ZodIssueCode.invalid_type,\n      expected: ZodParsedType.bigint,\n      received: ctx.parsedType\n    });\n    return INVALID;\n  }\n  gte(value, message) {\n    return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n  }\n  gt(value, message) {\n    return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n  }\n  lte(value, message) {\n    return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n  }\n  lt(value, message) {\n    return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n  }\n  setLimit(kind, value, inclusive, message) {\n    return new _ZodBigInt({\n      ...this._def,\n      checks: [\n        ...this._def.checks,\n        {\n          kind,\n          value,\n          inclusive,\n          message: errorUtil.toString(message)\n        }\n      ]\n    });\n  }\n  _addCheck(check) {\n    return new _ZodBigInt({\n      ...this._def,\n      checks: [...this._def.checks, check]\n    });\n  }\n  positive(message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: BigInt(0),\n      inclusive: false,\n      message: errorUtil.toString(message)\n    });\n  }\n  negative(message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: BigInt(0),\n      inclusive: false,\n      message: errorUtil.toString(message)\n    });\n  }\n  nonpositive(message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: BigInt(0),\n      inclusive: true,\n      message: errorUtil.toString(message)\n    });\n  }\n  nonnegative(message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: BigInt(0),\n      inclusive: true,\n      message: errorUtil.toString(message)\n    });\n  }\n  multipleOf(value, message) {\n    return this._addCheck({\n      kind: \"multipleOf\",\n      value,\n      message: errorUtil.toString(message)\n    });\n  }\n  get minValue() {\n    let min = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"min\") {\n        if (min === null || ch.value > min)\n          min = ch.value;\n      }\n    }\n    return min;\n  }\n  get maxValue() {\n    let max = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"max\") {\n        if (max === null || ch.value < max)\n          max = ch.value;\n      }\n    }\n    return max;\n  }\n};\nZodBigInt.create = (params) => {\n  var _a;\n  return new ZodBigInt({\n    checks: [],\n    typeName: ZodFirstPartyTypeKind.ZodBigInt,\n    coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n    ...processCreateParams(params)\n  });\n};\nvar ZodBoolean = class extends ZodType {\n  _parse(input) {\n    if (this._def.coerce) {\n      input.data = Boolean(input.data);\n    }\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.boolean) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.boolean,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n};\nZodBoolean.create = (params) => {\n  return new ZodBoolean({\n    typeName: ZodFirstPartyTypeKind.ZodBoolean,\n    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n    ...processCreateParams(params)\n  });\n};\nvar ZodDate = class _ZodDate extends ZodType {\n  _parse(input) {\n    if (this._def.coerce) {\n      input.data = new Date(input.data);\n    }\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.date) {\n      const ctx2 = this._getOrReturnCtx(input);\n      addIssueToContext(ctx2, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.date,\n        received: ctx2.parsedType\n      });\n      return INVALID;\n    }\n    if (isNaN(input.data.getTime())) {\n      const ctx2 = this._getOrReturnCtx(input);\n      addIssueToContext(ctx2, {\n        code: ZodIssueCode.invalid_date\n      });\n      return INVALID;\n    }\n    const status = new ParseStatus();\n    let ctx = void 0;\n    for (const check of this._def.checks) {\n      if (check.kind === \"min\") {\n        if (input.data.getTime() < check.value) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_small,\n            message: check.message,\n            inclusive: true,\n            exact: false,\n            minimum: check.value,\n            type: \"date\"\n          });\n          status.dirty();\n        }\n      } else if (check.kind === \"max\") {\n        if (input.data.getTime() > check.value) {\n          ctx = this._getOrReturnCtx(input, ctx);\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.too_big,\n            message: check.message,\n            inclusive: true,\n            exact: false,\n            maximum: check.value,\n            type: \"date\"\n          });\n          status.dirty();\n        }\n      } else {\n        util.assertNever(check);\n      }\n    }\n    return {\n      status: status.value,\n      value: new Date(input.data.getTime())\n    };\n  }\n  _addCheck(check) {\n    return new _ZodDate({\n      ...this._def,\n      checks: [...this._def.checks, check]\n    });\n  }\n  min(minDate, message) {\n    return this._addCheck({\n      kind: \"min\",\n      value: minDate.getTime(),\n      message: errorUtil.toString(message)\n    });\n  }\n  max(maxDate, message) {\n    return this._addCheck({\n      kind: \"max\",\n      value: maxDate.getTime(),\n      message: errorUtil.toString(message)\n    });\n  }\n  get minDate() {\n    let min = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"min\") {\n        if (min === null || ch.value > min)\n          min = ch.value;\n      }\n    }\n    return min != null ? new Date(min) : null;\n  }\n  get maxDate() {\n    let max = null;\n    for (const ch of this._def.checks) {\n      if (ch.kind === \"max\") {\n        if (max === null || ch.value < max)\n          max = ch.value;\n      }\n    }\n    return max != null ? new Date(max) : null;\n  }\n};\nZodDate.create = (params) => {\n  return new ZodDate({\n    checks: [],\n    coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n    typeName: ZodFirstPartyTypeKind.ZodDate,\n    ...processCreateParams(params)\n  });\n};\nvar ZodSymbol = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.symbol) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.symbol,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n};\nZodSymbol.create = (params) => {\n  return new ZodSymbol({\n    typeName: ZodFirstPartyTypeKind.ZodSymbol,\n    ...processCreateParams(params)\n  });\n};\nvar ZodUndefined = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.undefined) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.undefined,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n};\nZodUndefined.create = (params) => {\n  return new ZodUndefined({\n    typeName: ZodFirstPartyTypeKind.ZodUndefined,\n    ...processCreateParams(params)\n  });\n};\nvar ZodNull = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.null) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.null,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n};\nZodNull.create = (params) => {\n  return new ZodNull({\n    typeName: ZodFirstPartyTypeKind.ZodNull,\n    ...processCreateParams(params)\n  });\n};\nvar ZodAny = class extends ZodType {\n  constructor() {\n    super(...arguments);\n    this._any = true;\n  }\n  _parse(input) {\n    return OK(input.data);\n  }\n};\nZodAny.create = (params) => {\n  return new ZodAny({\n    typeName: ZodFirstPartyTypeKind.ZodAny,\n    ...processCreateParams(params)\n  });\n};\nvar ZodUnknown = class extends ZodType {\n  constructor() {\n    super(...arguments);\n    this._unknown = true;\n  }\n  _parse(input) {\n    return OK(input.data);\n  }\n};\nZodUnknown.create = (params) => {\n  return new ZodUnknown({\n    typeName: ZodFirstPartyTypeKind.ZodUnknown,\n    ...processCreateParams(params)\n  });\n};\nvar ZodNever = class extends ZodType {\n  _parse(input) {\n    const ctx = this._getOrReturnCtx(input);\n    addIssueToContext(ctx, {\n      code: ZodIssueCode.invalid_type,\n      expected: ZodParsedType.never,\n      received: ctx.parsedType\n    });\n    return INVALID;\n  }\n};\nZodNever.create = (params) => {\n  return new ZodNever({\n    typeName: ZodFirstPartyTypeKind.ZodNever,\n    ...processCreateParams(params)\n  });\n};\nvar ZodVoid = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.undefined) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.void,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n};\nZodVoid.create = (params) => {\n  return new ZodVoid({\n    typeName: ZodFirstPartyTypeKind.ZodVoid,\n    ...processCreateParams(params)\n  });\n};\nvar ZodArray = class _ZodArray extends ZodType {\n  _parse(input) {\n    const { ctx, status } = this._processInputParams(input);\n    const def = this._def;\n    if (ctx.parsedType !== ZodParsedType.array) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.array,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    if (def.exactLength !== null) {\n      const tooBig = ctx.data.length > def.exactLength.value;\n      const tooSmall = ctx.data.length < def.exactLength.value;\n      if (tooBig || tooSmall) {\n        addIssueToContext(ctx, {\n          code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,\n          minimum: tooSmall ? def.exactLength.value : void 0,\n          maximum: tooBig ? def.exactLength.value : void 0,\n          type: \"array\",\n          inclusive: true,\n          exact: true,\n          message: def.exactLength.message\n        });\n        status.dirty();\n      }\n    }\n    if (def.minLength !== null) {\n      if (ctx.data.length < def.minLength.value) {\n        addIssueToContext(ctx, {\n          code: ZodIssueCode.too_small,\n          minimum: def.minLength.value,\n          type: \"array\",\n          inclusive: true,\n          exact: false,\n          message: def.minLength.message\n        });\n        status.dirty();\n      }\n    }\n    if (def.maxLength !== null) {\n      if (ctx.data.length > def.maxLength.value) {\n        addIssueToContext(ctx, {\n          code: ZodIssueCode.too_big,\n          maximum: def.maxLength.value,\n          type: \"array\",\n          inclusive: true,\n          exact: false,\n          message: def.maxLength.message\n        });\n        status.dirty();\n      }\n    }\n    if (ctx.common.async) {\n      return Promise.all([...ctx.data].map((item, i) => {\n        return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n      })).then((result2) => {\n        return ParseStatus.mergeArray(status, result2);\n      });\n    }\n    const result = [...ctx.data].map((item, i) => {\n      return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n    });\n    return ParseStatus.mergeArray(status, result);\n  }\n  get element() {\n    return this._def.type;\n  }\n  min(minLength, message) {\n    return new _ZodArray({\n      ...this._def,\n      minLength: { value: minLength, message: errorUtil.toString(message) }\n    });\n  }\n  max(maxLength, message) {\n    return new _ZodArray({\n      ...this._def,\n      maxLength: { value: maxLength, message: errorUtil.toString(message) }\n    });\n  }\n  length(len, message) {\n    return new _ZodArray({\n      ...this._def,\n      exactLength: { value: len, message: errorUtil.toString(message) }\n    });\n  }\n  nonempty(message) {\n    return this.min(1, message);\n  }\n};\nZodArray.create = (schema, params) => {\n  return new ZodArray({\n    type: schema,\n    minLength: null,\n    maxLength: null,\n    exactLength: null,\n    typeName: ZodFirstPartyTypeKind.ZodArray,\n    ...processCreateParams(params)\n  });\n};\nfunction deepPartialify(schema) {\n  if (schema instanceof ZodObject) {\n    const newShape = {};\n    for (const key in schema.shape) {\n      const fieldSchema = schema.shape[key];\n      newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));\n    }\n    return new ZodObject({\n      ...schema._def,\n      shape: () => newShape\n    });\n  } else if (schema instanceof ZodArray) {\n    return new ZodArray({\n      ...schema._def,\n      type: deepPartialify(schema.element)\n    });\n  } else if (schema instanceof ZodOptional) {\n    return ZodOptional.create(deepPartialify(schema.unwrap()));\n  } else if (schema instanceof ZodNullable) {\n    return ZodNullable.create(deepPartialify(schema.unwrap()));\n  } else if (schema instanceof ZodTuple) {\n    return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));\n  } else {\n    return schema;\n  }\n}\nvar ZodObject = class _ZodObject extends ZodType {\n  constructor() {\n    super(...arguments);\n    this._cached = null;\n    this.nonstrict = this.passthrough;\n    this.augment = this.extend;\n  }\n  _getCached() {\n    if (this._cached !== null)\n      return this._cached;\n    const shape = this._def.shape();\n    const keys = util.objectKeys(shape);\n    return this._cached = { shape, keys };\n  }\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.object) {\n      const ctx2 = this._getOrReturnCtx(input);\n      addIssueToContext(ctx2, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.object,\n        received: ctx2.parsedType\n      });\n      return INVALID;\n    }\n    const { status, ctx } = this._processInputParams(input);\n    const { shape, keys: shapeKeys } = this._getCached();\n    const extraKeys = [];\n    if (!(this._def.catchall instanceof ZodNever && this._def.unknownKeys === \"strip\")) {\n      for (const key in ctx.data) {\n        if (!shapeKeys.includes(key)) {\n          extraKeys.push(key);\n        }\n      }\n    }\n    const pairs = [];\n    for (const key of shapeKeys) {\n      const keyValidator = shape[key];\n      const value = ctx.data[key];\n      pairs.push({\n        key: { status: \"valid\", value: key },\n        value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n        alwaysSet: key in ctx.data\n      });\n    }\n    if (this._def.catchall instanceof ZodNever) {\n      const unknownKeys = this._def.unknownKeys;\n      if (unknownKeys === \"passthrough\") {\n        for (const key of extraKeys) {\n          pairs.push({\n            key: { status: \"valid\", value: key },\n            value: { status: \"valid\", value: ctx.data[key] }\n          });\n        }\n      } else if (unknownKeys === \"strict\") {\n        if (extraKeys.length > 0) {\n          addIssueToContext(ctx, {\n            code: ZodIssueCode.unrecognized_keys,\n            keys: extraKeys\n          });\n          status.dirty();\n        }\n      } else if (unknownKeys === \"strip\") ;\n      else {\n        throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);\n      }\n    } else {\n      const catchall = this._def.catchall;\n      for (const key of extraKeys) {\n        const value = ctx.data[key];\n        pairs.push({\n          key: { status: \"valid\", value: key },\n          value: catchall._parse(\n            new ParseInputLazyPath(ctx, value, ctx.path, key)\n            //, ctx.child(key), value, getParsedType(value)\n          ),\n          alwaysSet: key in ctx.data\n        });\n      }\n    }\n    if (ctx.common.async) {\n      return Promise.resolve().then(async () => {\n        const syncPairs = [];\n        for (const pair of pairs) {\n          const key = await pair.key;\n          const value = await pair.value;\n          syncPairs.push({\n            key,\n            value,\n            alwaysSet: pair.alwaysSet\n          });\n        }\n        return syncPairs;\n      }).then((syncPairs) => {\n        return ParseStatus.mergeObjectSync(status, syncPairs);\n      });\n    } else {\n      return ParseStatus.mergeObjectSync(status, pairs);\n    }\n  }\n  get shape() {\n    return this._def.shape();\n  }\n  strict(message) {\n    errorUtil.errToObj;\n    return new _ZodObject({\n      ...this._def,\n      unknownKeys: \"strict\",\n      ...message !== void 0 ? {\n        errorMap: (issue, ctx) => {\n          var _a, _b, _c, _d;\n          const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;\n          if (issue.code === \"unrecognized_keys\")\n            return {\n              message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError\n            };\n          return {\n            message: defaultError\n          };\n        }\n      } : {}\n    });\n  }\n  strip() {\n    return new _ZodObject({\n      ...this._def,\n      unknownKeys: \"strip\"\n    });\n  }\n  passthrough() {\n    return new _ZodObject({\n      ...this._def,\n      unknownKeys: \"passthrough\"\n    });\n  }\n  // const AugmentFactory =\n  //   <Def extends ZodObjectDef>(def: Def) =>\n  //   <Augmentation extends ZodRawShape>(\n  //     augmentation: Augmentation\n  //   ): ZodObject<\n  //     extendShape<ReturnType<Def[\"shape\"]>, Augmentation>,\n  //     Def[\"unknownKeys\"],\n  //     Def[\"catchall\"]\n  //   > => {\n  //     return new ZodObject({\n  //       ...def,\n  //       shape: () => ({\n  //         ...def.shape(),\n  //         ...augmentation,\n  //       }),\n  //     }) as any;\n  //   };\n  extend(augmentation) {\n    return new _ZodObject({\n      ...this._def,\n      shape: () => ({\n        ...this._def.shape(),\n        ...augmentation\n      })\n    });\n  }\n  /**\n   * Prior to zod@1.0.12 there was a bug in the\n   * inferred type of merged objects. Please\n   * upgrade if you are experiencing issues.\n   */\n  merge(merging) {\n    const merged = new _ZodObject({\n      unknownKeys: merging._def.unknownKeys,\n      catchall: merging._def.catchall,\n      shape: () => ({\n        ...this._def.shape(),\n        ...merging._def.shape()\n      }),\n      typeName: ZodFirstPartyTypeKind.ZodObject\n    });\n    return merged;\n  }\n  // merge<\n  //   Incoming extends AnyZodObject,\n  //   Augmentation extends Incoming[\"shape\"],\n  //   NewOutput extends {\n  //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n  //       ? Augmentation[k][\"_output\"]\n  //       : k extends keyof Output\n  //       ? Output[k]\n  //       : never;\n  //   },\n  //   NewInput extends {\n  //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n  //       ? Augmentation[k][\"_input\"]\n  //       : k extends keyof Input\n  //       ? Input[k]\n  //       : never;\n  //   }\n  // >(\n  //   merging: Incoming\n  // ): ZodObject<\n  //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n  //   Incoming[\"_def\"][\"unknownKeys\"],\n  //   Incoming[\"_def\"][\"catchall\"],\n  //   NewOutput,\n  //   NewInput\n  // > {\n  //   const merged: any = new ZodObject({\n  //     unknownKeys: merging._def.unknownKeys,\n  //     catchall: merging._def.catchall,\n  //     shape: () =>\n  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n  //     typeName: ZodFirstPartyTypeKind.ZodObject,\n  //   }) as any;\n  //   return merged;\n  // }\n  setKey(key, schema) {\n    return this.augment({ [key]: schema });\n  }\n  // merge<Incoming extends AnyZodObject>(\n  //   merging: Incoming\n  // ): //ZodObject<T & Incoming[\"_shape\"], UnknownKeys, Catchall> = (merging) => {\n  // ZodObject<\n  //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n  //   Incoming[\"_def\"][\"unknownKeys\"],\n  //   Incoming[\"_def\"][\"catchall\"]\n  // > {\n  //   // const mergedShape = objectUtil.mergeShapes(\n  //   //   this._def.shape(),\n  //   //   merging._def.shape()\n  //   // );\n  //   const merged: any = new ZodObject({\n  //     unknownKeys: merging._def.unknownKeys,\n  //     catchall: merging._def.catchall,\n  //     shape: () =>\n  //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n  //     typeName: ZodFirstPartyTypeKind.ZodObject,\n  //   }) as any;\n  //   return merged;\n  // }\n  catchall(index) {\n    return new _ZodObject({\n      ...this._def,\n      catchall: index\n    });\n  }\n  pick(mask) {\n    const shape = {};\n    util.objectKeys(mask).forEach((key) => {\n      if (mask[key] && this.shape[key]) {\n        shape[key] = this.shape[key];\n      }\n    });\n    return new _ZodObject({\n      ...this._def,\n      shape: () => shape\n    });\n  }\n  omit(mask) {\n    const shape = {};\n    util.objectKeys(this.shape).forEach((key) => {\n      if (!mask[key]) {\n        shape[key] = this.shape[key];\n      }\n    });\n    return new _ZodObject({\n      ...this._def,\n      shape: () => shape\n    });\n  }\n  /**\n   * @deprecated\n   */\n  deepPartial() {\n    return deepPartialify(this);\n  }\n  partial(mask) {\n    const newShape = {};\n    util.objectKeys(this.shape).forEach((key) => {\n      const fieldSchema = this.shape[key];\n      if (mask && !mask[key]) {\n        newShape[key] = fieldSchema;\n      } else {\n        newShape[key] = fieldSchema.optional();\n      }\n    });\n    return new _ZodObject({\n      ...this._def,\n      shape: () => newShape\n    });\n  }\n  required(mask) {\n    const newShape = {};\n    util.objectKeys(this.shape).forEach((key) => {\n      if (mask && !mask[key]) {\n        newShape[key] = this.shape[key];\n      } else {\n        const fieldSchema = this.shape[key];\n        let newField = fieldSchema;\n        while (newField instanceof ZodOptional) {\n          newField = newField._def.innerType;\n        }\n        newShape[key] = newField;\n      }\n    });\n    return new _ZodObject({\n      ...this._def,\n      shape: () => newShape\n    });\n  }\n  keyof() {\n    return createZodEnum(util.objectKeys(this.shape));\n  }\n};\nZodObject.create = (shape, params) => {\n  return new ZodObject({\n    shape: () => shape,\n    unknownKeys: \"strip\",\n    catchall: ZodNever.create(),\n    typeName: ZodFirstPartyTypeKind.ZodObject,\n    ...processCreateParams(params)\n  });\n};\nZodObject.strictCreate = (shape, params) => {\n  return new ZodObject({\n    shape: () => shape,\n    unknownKeys: \"strict\",\n    catchall: ZodNever.create(),\n    typeName: ZodFirstPartyTypeKind.ZodObject,\n    ...processCreateParams(params)\n  });\n};\nZodObject.lazycreate = (shape, params) => {\n  return new ZodObject({\n    shape,\n    unknownKeys: \"strip\",\n    catchall: ZodNever.create(),\n    typeName: ZodFirstPartyTypeKind.ZodObject,\n    ...processCreateParams(params)\n  });\n};\nvar ZodUnion = class extends ZodType {\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    const options = this._def.options;\n    function handleResults(results) {\n      for (const result of results) {\n        if (result.result.status === \"valid\") {\n          return result.result;\n        }\n      }\n      for (const result of results) {\n        if (result.result.status === \"dirty\") {\n          ctx.common.issues.push(...result.ctx.common.issues);\n          return result.result;\n        }\n      }\n      const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_union,\n        unionErrors\n      });\n      return INVALID;\n    }\n    if (ctx.common.async) {\n      return Promise.all(options.map(async (option) => {\n        const childCtx = {\n          ...ctx,\n          common: {\n            ...ctx.common,\n            issues: []\n          },\n          parent: null\n        };\n        return {\n          result: await option._parseAsync({\n            data: ctx.data,\n            path: ctx.path,\n            parent: childCtx\n          }),\n          ctx: childCtx\n        };\n      })).then(handleResults);\n    } else {\n      let dirty = void 0;\n      const issues = [];\n      for (const option of options) {\n        const childCtx = {\n          ...ctx,\n          common: {\n            ...ctx.common,\n            issues: []\n          },\n          parent: null\n        };\n        const result = option._parseSync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: childCtx\n        });\n        if (result.status === \"valid\") {\n          return result;\n        } else if (result.status === \"dirty\" && !dirty) {\n          dirty = { result, ctx: childCtx };\n        }\n        if (childCtx.common.issues.length) {\n          issues.push(childCtx.common.issues);\n        }\n      }\n      if (dirty) {\n        ctx.common.issues.push(...dirty.ctx.common.issues);\n        return dirty.result;\n      }\n      const unionErrors = issues.map((issues2) => new ZodError(issues2));\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_union,\n        unionErrors\n      });\n      return INVALID;\n    }\n  }\n  get options() {\n    return this._def.options;\n  }\n};\nZodUnion.create = (types, params) => {\n  return new ZodUnion({\n    options: types,\n    typeName: ZodFirstPartyTypeKind.ZodUnion,\n    ...processCreateParams(params)\n  });\n};\nvar getDiscriminator = (type) => {\n  if (type instanceof ZodLazy) {\n    return getDiscriminator(type.schema);\n  } else if (type instanceof ZodEffects) {\n    return getDiscriminator(type.innerType());\n  } else if (type instanceof ZodLiteral) {\n    return [type.value];\n  } else if (type instanceof ZodEnum) {\n    return type.options;\n  } else if (type instanceof ZodNativeEnum) {\n    return util.objectValues(type.enum);\n  } else if (type instanceof ZodDefault) {\n    return getDiscriminator(type._def.innerType);\n  } else if (type instanceof ZodUndefined) {\n    return [void 0];\n  } else if (type instanceof ZodNull) {\n    return [null];\n  } else if (type instanceof ZodOptional) {\n    return [void 0, ...getDiscriminator(type.unwrap())];\n  } else if (type instanceof ZodNullable) {\n    return [null, ...getDiscriminator(type.unwrap())];\n  } else if (type instanceof ZodBranded) {\n    return getDiscriminator(type.unwrap());\n  } else if (type instanceof ZodReadonly) {\n    return getDiscriminator(type.unwrap());\n  } else if (type instanceof ZodCatch) {\n    return getDiscriminator(type._def.innerType);\n  } else {\n    return [];\n  }\n};\nvar ZodDiscriminatedUnion = class _ZodDiscriminatedUnion extends ZodType {\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.object) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.object,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    const discriminator = this.discriminator;\n    const discriminatorValue = ctx.data[discriminator];\n    const option = this.optionsMap.get(discriminatorValue);\n    if (!option) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_union_discriminator,\n        options: Array.from(this.optionsMap.keys()),\n        path: [discriminator]\n      });\n      return INVALID;\n    }\n    if (ctx.common.async) {\n      return option._parseAsync({\n        data: ctx.data,\n        path: ctx.path,\n        parent: ctx\n      });\n    } else {\n      return option._parseSync({\n        data: ctx.data,\n        path: ctx.path,\n        parent: ctx\n      });\n    }\n  }\n  get discriminator() {\n    return this._def.discriminator;\n  }\n  get options() {\n    return this._def.options;\n  }\n  get optionsMap() {\n    return this._def.optionsMap;\n  }\n  /**\n   * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.\n   * However, it only allows a union of objects, all of which need to share a discriminator property. This property must\n   * have a different value for each object in the union.\n   * @param discriminator the name of the discriminator property\n   * @param types an array of object schemas\n   * @param params\n   */\n  static create(discriminator, options, params) {\n    const optionsMap = /* @__PURE__ */ new Map();\n    for (const type of options) {\n      const discriminatorValues = getDiscriminator(type.shape[discriminator]);\n      if (!discriminatorValues.length) {\n        throw new Error(`A discriminator value for key \\`${discriminator}\\` could not be extracted from all schema options`);\n      }\n      for (const value of discriminatorValues) {\n        if (optionsMap.has(value)) {\n          throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);\n        }\n        optionsMap.set(value, type);\n      }\n    }\n    return new _ZodDiscriminatedUnion({\n      typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,\n      discriminator,\n      options,\n      optionsMap,\n      ...processCreateParams(params)\n    });\n  }\n};\nfunction mergeValues(a, b) {\n  const aType = getParsedType(a);\n  const bType = getParsedType(b);\n  if (a === b) {\n    return { valid: true, data: a };\n  } else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {\n    const bKeys = util.objectKeys(b);\n    const sharedKeys = util.objectKeys(a).filter((key) => bKeys.indexOf(key) !== -1);\n    const newObj = { ...a, ...b };\n    for (const key of sharedKeys) {\n      const sharedValue = mergeValues(a[key], b[key]);\n      if (!sharedValue.valid) {\n        return { valid: false };\n      }\n      newObj[key] = sharedValue.data;\n    }\n    return { valid: true, data: newObj };\n  } else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {\n    if (a.length !== b.length) {\n      return { valid: false };\n    }\n    const newArray = [];\n    for (let index = 0; index < a.length; index++) {\n      const itemA = a[index];\n      const itemB = b[index];\n      const sharedValue = mergeValues(itemA, itemB);\n      if (!sharedValue.valid) {\n        return { valid: false };\n      }\n      newArray.push(sharedValue.data);\n    }\n    return { valid: true, data: newArray };\n  } else if (aType === ZodParsedType.date && bType === ZodParsedType.date && +a === +b) {\n    return { valid: true, data: a };\n  } else {\n    return { valid: false };\n  }\n}\nvar ZodIntersection = class extends ZodType {\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    const handleParsed = (parsedLeft, parsedRight) => {\n      if (isAborted(parsedLeft) || isAborted(parsedRight)) {\n        return INVALID;\n      }\n      const merged = mergeValues(parsedLeft.value, parsedRight.value);\n      if (!merged.valid) {\n        addIssueToContext(ctx, {\n          code: ZodIssueCode.invalid_intersection_types\n        });\n        return INVALID;\n      }\n      if (isDirty(parsedLeft) || isDirty(parsedRight)) {\n        status.dirty();\n      }\n      return { status: status.value, value: merged.data };\n    };\n    if (ctx.common.async) {\n      return Promise.all([\n        this._def.left._parseAsync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: ctx\n        }),\n        this._def.right._parseAsync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: ctx\n        })\n      ]).then(([left, right]) => handleParsed(left, right));\n    } else {\n      return handleParsed(this._def.left._parseSync({\n        data: ctx.data,\n        path: ctx.path,\n        parent: ctx\n      }), this._def.right._parseSync({\n        data: ctx.data,\n        path: ctx.path,\n        parent: ctx\n      }));\n    }\n  }\n};\nZodIntersection.create = (left, right, params) => {\n  return new ZodIntersection({\n    left,\n    right,\n    typeName: ZodFirstPartyTypeKind.ZodIntersection,\n    ...processCreateParams(params)\n  });\n};\nvar ZodTuple = class _ZodTuple extends ZodType {\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.array) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.array,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    if (ctx.data.length < this._def.items.length) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.too_small,\n        minimum: this._def.items.length,\n        inclusive: true,\n        exact: false,\n        type: \"array\"\n      });\n      return INVALID;\n    }\n    const rest = this._def.rest;\n    if (!rest && ctx.data.length > this._def.items.length) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.too_big,\n        maximum: this._def.items.length,\n        inclusive: true,\n        exact: false,\n        type: \"array\"\n      });\n      status.dirty();\n    }\n    const items = [...ctx.data].map((item, itemIndex) => {\n      const schema = this._def.items[itemIndex] || this._def.rest;\n      if (!schema)\n        return null;\n      return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));\n    }).filter((x) => !!x);\n    if (ctx.common.async) {\n      return Promise.all(items).then((results) => {\n        return ParseStatus.mergeArray(status, results);\n      });\n    } else {\n      return ParseStatus.mergeArray(status, items);\n    }\n  }\n  get items() {\n    return this._def.items;\n  }\n  rest(rest) {\n    return new _ZodTuple({\n      ...this._def,\n      rest\n    });\n  }\n};\nZodTuple.create = (schemas, params) => {\n  if (!Array.isArray(schemas)) {\n    throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");\n  }\n  return new ZodTuple({\n    items: schemas,\n    typeName: ZodFirstPartyTypeKind.ZodTuple,\n    rest: null,\n    ...processCreateParams(params)\n  });\n};\nvar ZodRecord = class _ZodRecord extends ZodType {\n  get keySchema() {\n    return this._def.keyType;\n  }\n  get valueSchema() {\n    return this._def.valueType;\n  }\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.object) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.object,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    const pairs = [];\n    const keyType = this._def.keyType;\n    const valueType = this._def.valueType;\n    for (const key in ctx.data) {\n      pairs.push({\n        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),\n        value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),\n        alwaysSet: key in ctx.data\n      });\n    }\n    if (ctx.common.async) {\n      return ParseStatus.mergeObjectAsync(status, pairs);\n    } else {\n      return ParseStatus.mergeObjectSync(status, pairs);\n    }\n  }\n  get element() {\n    return this._def.valueType;\n  }\n  static create(first, second, third) {\n    if (second instanceof ZodType) {\n      return new _ZodRecord({\n        keyType: first,\n        valueType: second,\n        typeName: ZodFirstPartyTypeKind.ZodRecord,\n        ...processCreateParams(third)\n      });\n    }\n    return new _ZodRecord({\n      keyType: ZodString.create(),\n      valueType: first,\n      typeName: ZodFirstPartyTypeKind.ZodRecord,\n      ...processCreateParams(second)\n    });\n  }\n};\nvar ZodMap = class extends ZodType {\n  get keySchema() {\n    return this._def.keyType;\n  }\n  get valueSchema() {\n    return this._def.valueType;\n  }\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.map) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.map,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    const keyType = this._def.keyType;\n    const valueType = this._def.valueType;\n    const pairs = [...ctx.data.entries()].map(([key, value], index) => {\n      return {\n        key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, \"key\"])),\n        value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, \"value\"]))\n      };\n    });\n    if (ctx.common.async) {\n      const finalMap = /* @__PURE__ */ new Map();\n      return Promise.resolve().then(async () => {\n        for (const pair of pairs) {\n          const key = await pair.key;\n          const value = await pair.value;\n          if (key.status === \"aborted\" || value.status === \"aborted\") {\n            return INVALID;\n          }\n          if (key.status === \"dirty\" || value.status === \"dirty\") {\n            status.dirty();\n          }\n          finalMap.set(key.value, value.value);\n        }\n        return { status: status.value, value: finalMap };\n      });\n    } else {\n      const finalMap = /* @__PURE__ */ new Map();\n      for (const pair of pairs) {\n        const key = pair.key;\n        const value = pair.value;\n        if (key.status === \"aborted\" || value.status === \"aborted\") {\n          return INVALID;\n        }\n        if (key.status === \"dirty\" || value.status === \"dirty\") {\n          status.dirty();\n        }\n        finalMap.set(key.value, value.value);\n      }\n      return { status: status.value, value: finalMap };\n    }\n  }\n};\nZodMap.create = (keyType, valueType, params) => {\n  return new ZodMap({\n    valueType,\n    keyType,\n    typeName: ZodFirstPartyTypeKind.ZodMap,\n    ...processCreateParams(params)\n  });\n};\nvar ZodSet = class _ZodSet extends ZodType {\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.set) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.set,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    const def = this._def;\n    if (def.minSize !== null) {\n      if (ctx.data.size < def.minSize.value) {\n        addIssueToContext(ctx, {\n          code: ZodIssueCode.too_small,\n          minimum: def.minSize.value,\n          type: \"set\",\n          inclusive: true,\n          exact: false,\n          message: def.minSize.message\n        });\n        status.dirty();\n      }\n    }\n    if (def.maxSize !== null) {\n      if (ctx.data.size > def.maxSize.value) {\n        addIssueToContext(ctx, {\n          code: ZodIssueCode.too_big,\n          maximum: def.maxSize.value,\n          type: \"set\",\n          inclusive: true,\n          exact: false,\n          message: def.maxSize.message\n        });\n        status.dirty();\n      }\n    }\n    const valueType = this._def.valueType;\n    function finalizeSet(elements2) {\n      const parsedSet = /* @__PURE__ */ new Set();\n      for (const element of elements2) {\n        if (element.status === \"aborted\")\n          return INVALID;\n        if (element.status === \"dirty\")\n          status.dirty();\n        parsedSet.add(element.value);\n      }\n      return { status: status.value, value: parsedSet };\n    }\n    const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));\n    if (ctx.common.async) {\n      return Promise.all(elements).then((elements2) => finalizeSet(elements2));\n    } else {\n      return finalizeSet(elements);\n    }\n  }\n  min(minSize, message) {\n    return new _ZodSet({\n      ...this._def,\n      minSize: { value: minSize, message: errorUtil.toString(message) }\n    });\n  }\n  max(maxSize, message) {\n    return new _ZodSet({\n      ...this._def,\n      maxSize: { value: maxSize, message: errorUtil.toString(message) }\n    });\n  }\n  size(size, message) {\n    return this.min(size, message).max(size, message);\n  }\n  nonempty(message) {\n    return this.min(1, message);\n  }\n};\nZodSet.create = (valueType, params) => {\n  return new ZodSet({\n    valueType,\n    minSize: null,\n    maxSize: null,\n    typeName: ZodFirstPartyTypeKind.ZodSet,\n    ...processCreateParams(params)\n  });\n};\nvar ZodFunction = class _ZodFunction extends ZodType {\n  constructor() {\n    super(...arguments);\n    this.validate = this.implement;\n  }\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.function) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.function,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    function makeArgsIssue(args, error) {\n      return makeIssue({\n        data: args,\n        path: ctx.path,\n        errorMaps: [\n          ctx.common.contextualErrorMap,\n          ctx.schemaErrorMap,\n          getErrorMap(),\n          errorMap\n        ].filter((x) => !!x),\n        issueData: {\n          code: ZodIssueCode.invalid_arguments,\n          argumentsError: error\n        }\n      });\n    }\n    function makeReturnsIssue(returns, error) {\n      return makeIssue({\n        data: returns,\n        path: ctx.path,\n        errorMaps: [\n          ctx.common.contextualErrorMap,\n          ctx.schemaErrorMap,\n          getErrorMap(),\n          errorMap\n        ].filter((x) => !!x),\n        issueData: {\n          code: ZodIssueCode.invalid_return_type,\n          returnTypeError: error\n        }\n      });\n    }\n    const params = { errorMap: ctx.common.contextualErrorMap };\n    const fn = ctx.data;\n    if (this._def.returns instanceof ZodPromise) {\n      const me = this;\n      return OK(async function(...args) {\n        const error = new ZodError([]);\n        const parsedArgs = await me._def.args.parseAsync(args, params).catch((e) => {\n          error.addIssue(makeArgsIssue(args, e));\n          throw error;\n        });\n        const result = await Reflect.apply(fn, this, parsedArgs);\n        const parsedReturns = await me._def.returns._def.type.parseAsync(result, params).catch((e) => {\n          error.addIssue(makeReturnsIssue(result, e));\n          throw error;\n        });\n        return parsedReturns;\n      });\n    } else {\n      const me = this;\n      return OK(function(...args) {\n        const parsedArgs = me._def.args.safeParse(args, params);\n        if (!parsedArgs.success) {\n          throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);\n        }\n        const result = Reflect.apply(fn, this, parsedArgs.data);\n        const parsedReturns = me._def.returns.safeParse(result, params);\n        if (!parsedReturns.success) {\n          throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);\n        }\n        return parsedReturns.data;\n      });\n    }\n  }\n  parameters() {\n    return this._def.args;\n  }\n  returnType() {\n    return this._def.returns;\n  }\n  args(...items) {\n    return new _ZodFunction({\n      ...this._def,\n      args: ZodTuple.create(items).rest(ZodUnknown.create())\n    });\n  }\n  returns(returnType) {\n    return new _ZodFunction({\n      ...this._def,\n      returns: returnType\n    });\n  }\n  implement(func) {\n    const validatedFunc = this.parse(func);\n    return validatedFunc;\n  }\n  strictImplement(func) {\n    const validatedFunc = this.parse(func);\n    return validatedFunc;\n  }\n  static create(args, returns, params) {\n    return new _ZodFunction({\n      args: args ? args : ZodTuple.create([]).rest(ZodUnknown.create()),\n      returns: returns || ZodUnknown.create(),\n      typeName: ZodFirstPartyTypeKind.ZodFunction,\n      ...processCreateParams(params)\n    });\n  }\n};\nvar ZodLazy = class extends ZodType {\n  get schema() {\n    return this._def.getter();\n  }\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    const lazySchema = this._def.getter();\n    return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });\n  }\n};\nZodLazy.create = (getter, params) => {\n  return new ZodLazy({\n    getter,\n    typeName: ZodFirstPartyTypeKind.ZodLazy,\n    ...processCreateParams(params)\n  });\n};\nvar ZodLiteral = class extends ZodType {\n  _parse(input) {\n    if (input.data !== this._def.value) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        received: ctx.data,\n        code: ZodIssueCode.invalid_literal,\n        expected: this._def.value\n      });\n      return INVALID;\n    }\n    return { status: \"valid\", value: input.data };\n  }\n  get value() {\n    return this._def.value;\n  }\n};\nZodLiteral.create = (value, params) => {\n  return new ZodLiteral({\n    value,\n    typeName: ZodFirstPartyTypeKind.ZodLiteral,\n    ...processCreateParams(params)\n  });\n};\nfunction createZodEnum(values, params) {\n  return new ZodEnum({\n    values,\n    typeName: ZodFirstPartyTypeKind.ZodEnum,\n    ...processCreateParams(params)\n  });\n}\nvar ZodEnum = class _ZodEnum extends ZodType {\n  constructor() {\n    super(...arguments);\n    _ZodEnum_cache.set(this, void 0);\n  }\n  _parse(input) {\n    if (typeof input.data !== \"string\") {\n      const ctx = this._getOrReturnCtx(input);\n      const expectedValues = this._def.values;\n      addIssueToContext(ctx, {\n        expected: util.joinValues(expectedValues),\n        received: ctx.parsedType,\n        code: ZodIssueCode.invalid_type\n      });\n      return INVALID;\n    }\n    if (!__classPrivateFieldGet(this, _ZodEnum_cache, \"f\")) {\n      __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values), \"f\");\n    }\n    if (!__classPrivateFieldGet(this, _ZodEnum_cache, \"f\").has(input.data)) {\n      const ctx = this._getOrReturnCtx(input);\n      const expectedValues = this._def.values;\n      addIssueToContext(ctx, {\n        received: ctx.data,\n        code: ZodIssueCode.invalid_enum_value,\n        options: expectedValues\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n  get options() {\n    return this._def.values;\n  }\n  get enum() {\n    const enumValues = {};\n    for (const val of this._def.values) {\n      enumValues[val] = val;\n    }\n    return enumValues;\n  }\n  get Values() {\n    const enumValues = {};\n    for (const val of this._def.values) {\n      enumValues[val] = val;\n    }\n    return enumValues;\n  }\n  get Enum() {\n    const enumValues = {};\n    for (const val of this._def.values) {\n      enumValues[val] = val;\n    }\n    return enumValues;\n  }\n  extract(values, newDef = this._def) {\n    return _ZodEnum.create(values, {\n      ...this._def,\n      ...newDef\n    });\n  }\n  exclude(values, newDef = this._def) {\n    return _ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {\n      ...this._def,\n      ...newDef\n    });\n  }\n};\n_ZodEnum_cache = /* @__PURE__ */ new WeakMap();\nZodEnum.create = createZodEnum;\nvar ZodNativeEnum = class extends ZodType {\n  constructor() {\n    super(...arguments);\n    _ZodNativeEnum_cache.set(this, void 0);\n  }\n  _parse(input) {\n    const nativeEnumValues = util.getValidEnumValues(this._def.values);\n    const ctx = this._getOrReturnCtx(input);\n    if (ctx.parsedType !== ZodParsedType.string && ctx.parsedType !== ZodParsedType.number) {\n      const expectedValues = util.objectValues(nativeEnumValues);\n      addIssueToContext(ctx, {\n        expected: util.joinValues(expectedValues),\n        received: ctx.parsedType,\n        code: ZodIssueCode.invalid_type\n      });\n      return INVALID;\n    }\n    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, \"f\")) {\n      __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(util.getValidEnumValues(this._def.values)), \"f\");\n    }\n    if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, \"f\").has(input.data)) {\n      const expectedValues = util.objectValues(nativeEnumValues);\n      addIssueToContext(ctx, {\n        received: ctx.data,\n        code: ZodIssueCode.invalid_enum_value,\n        options: expectedValues\n      });\n      return INVALID;\n    }\n    return OK(input.data);\n  }\n  get enum() {\n    return this._def.values;\n  }\n};\n_ZodNativeEnum_cache = /* @__PURE__ */ new WeakMap();\nZodNativeEnum.create = (values, params) => {\n  return new ZodNativeEnum({\n    values,\n    typeName: ZodFirstPartyTypeKind.ZodNativeEnum,\n    ...processCreateParams(params)\n  });\n};\nvar ZodPromise = class extends ZodType {\n  unwrap() {\n    return this._def.type;\n  }\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    if (ctx.parsedType !== ZodParsedType.promise && ctx.common.async === false) {\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.promise,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    const promisified = ctx.parsedType === ZodParsedType.promise ? ctx.data : Promise.resolve(ctx.data);\n    return OK(promisified.then((data) => {\n      return this._def.type.parseAsync(data, {\n        path: ctx.path,\n        errorMap: ctx.common.contextualErrorMap\n      });\n    }));\n  }\n};\nZodPromise.create = (schema, params) => {\n  return new ZodPromise({\n    type: schema,\n    typeName: ZodFirstPartyTypeKind.ZodPromise,\n    ...processCreateParams(params)\n  });\n};\nvar ZodEffects = class extends ZodType {\n  innerType() {\n    return this._def.schema;\n  }\n  sourceType() {\n    return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects ? this._def.schema.sourceType() : this._def.schema;\n  }\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    const effect = this._def.effect || null;\n    const checkCtx = {\n      addIssue: (arg) => {\n        addIssueToContext(ctx, arg);\n        if (arg.fatal) {\n          status.abort();\n        } else {\n          status.dirty();\n        }\n      },\n      get path() {\n        return ctx.path;\n      }\n    };\n    checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);\n    if (effect.type === \"preprocess\") {\n      const processed = effect.transform(ctx.data, checkCtx);\n      if (ctx.common.async) {\n        return Promise.resolve(processed).then(async (processed2) => {\n          if (status.value === \"aborted\")\n            return INVALID;\n          const result = await this._def.schema._parseAsync({\n            data: processed2,\n            path: ctx.path,\n            parent: ctx\n          });\n          if (result.status === \"aborted\")\n            return INVALID;\n          if (result.status === \"dirty\")\n            return DIRTY(result.value);\n          if (status.value === \"dirty\")\n            return DIRTY(result.value);\n          return result;\n        });\n      } else {\n        if (status.value === \"aborted\")\n          return INVALID;\n        const result = this._def.schema._parseSync({\n          data: processed,\n          path: ctx.path,\n          parent: ctx\n        });\n        if (result.status === \"aborted\")\n          return INVALID;\n        if (result.status === \"dirty\")\n          return DIRTY(result.value);\n        if (status.value === \"dirty\")\n          return DIRTY(result.value);\n        return result;\n      }\n    }\n    if (effect.type === \"refinement\") {\n      const executeRefinement = (acc) => {\n        const result = effect.refinement(acc, checkCtx);\n        if (ctx.common.async) {\n          return Promise.resolve(result);\n        }\n        if (result instanceof Promise) {\n          throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");\n        }\n        return acc;\n      };\n      if (ctx.common.async === false) {\n        const inner = this._def.schema._parseSync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: ctx\n        });\n        if (inner.status === \"aborted\")\n          return INVALID;\n        if (inner.status === \"dirty\")\n          status.dirty();\n        executeRefinement(inner.value);\n        return { status: status.value, value: inner.value };\n      } else {\n        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((inner) => {\n          if (inner.status === \"aborted\")\n            return INVALID;\n          if (inner.status === \"dirty\")\n            status.dirty();\n          return executeRefinement(inner.value).then(() => {\n            return { status: status.value, value: inner.value };\n          });\n        });\n      }\n    }\n    if (effect.type === \"transform\") {\n      if (ctx.common.async === false) {\n        const base = this._def.schema._parseSync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: ctx\n        });\n        if (!isValid(base))\n          return base;\n        const result = effect.transform(base.value, checkCtx);\n        if (result instanceof Promise) {\n          throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);\n        }\n        return { status: status.value, value: result };\n      } else {\n        return this._def.schema._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx }).then((base) => {\n          if (!isValid(base))\n            return base;\n          return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));\n        });\n      }\n    }\n    util.assertNever(effect);\n  }\n};\nZodEffects.create = (schema, effect, params) => {\n  return new ZodEffects({\n    schema,\n    typeName: ZodFirstPartyTypeKind.ZodEffects,\n    effect,\n    ...processCreateParams(params)\n  });\n};\nZodEffects.createWithPreprocess = (preprocess, schema, params) => {\n  return new ZodEffects({\n    schema,\n    effect: { type: \"preprocess\", transform: preprocess },\n    typeName: ZodFirstPartyTypeKind.ZodEffects,\n    ...processCreateParams(params)\n  });\n};\nvar ZodOptional = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType === ZodParsedType.undefined) {\n      return OK(void 0);\n    }\n    return this._def.innerType._parse(input);\n  }\n  unwrap() {\n    return this._def.innerType;\n  }\n};\nZodOptional.create = (type, params) => {\n  return new ZodOptional({\n    innerType: type,\n    typeName: ZodFirstPartyTypeKind.ZodOptional,\n    ...processCreateParams(params)\n  });\n};\nvar ZodNullable = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType === ZodParsedType.null) {\n      return OK(null);\n    }\n    return this._def.innerType._parse(input);\n  }\n  unwrap() {\n    return this._def.innerType;\n  }\n};\nZodNullable.create = (type, params) => {\n  return new ZodNullable({\n    innerType: type,\n    typeName: ZodFirstPartyTypeKind.ZodNullable,\n    ...processCreateParams(params)\n  });\n};\nvar ZodDefault = class extends ZodType {\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    let data = ctx.data;\n    if (ctx.parsedType === ZodParsedType.undefined) {\n      data = this._def.defaultValue();\n    }\n    return this._def.innerType._parse({\n      data,\n      path: ctx.path,\n      parent: ctx\n    });\n  }\n  removeDefault() {\n    return this._def.innerType;\n  }\n};\nZodDefault.create = (type, params) => {\n  return new ZodDefault({\n    innerType: type,\n    typeName: ZodFirstPartyTypeKind.ZodDefault,\n    defaultValue: typeof params.default === \"function\" ? params.default : () => params.default,\n    ...processCreateParams(params)\n  });\n};\nvar ZodCatch = class extends ZodType {\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    const newCtx = {\n      ...ctx,\n      common: {\n        ...ctx.common,\n        issues: []\n      }\n    };\n    const result = this._def.innerType._parse({\n      data: newCtx.data,\n      path: newCtx.path,\n      parent: {\n        ...newCtx\n      }\n    });\n    if (isAsync(result)) {\n      return result.then((result2) => {\n        return {\n          status: \"valid\",\n          value: result2.status === \"valid\" ? result2.value : this._def.catchValue({\n            get error() {\n              return new ZodError(newCtx.common.issues);\n            },\n            input: newCtx.data\n          })\n        };\n      });\n    } else {\n      return {\n        status: \"valid\",\n        value: result.status === \"valid\" ? result.value : this._def.catchValue({\n          get error() {\n            return new ZodError(newCtx.common.issues);\n          },\n          input: newCtx.data\n        })\n      };\n    }\n  }\n  removeCatch() {\n    return this._def.innerType;\n  }\n};\nZodCatch.create = (type, params) => {\n  return new ZodCatch({\n    innerType: type,\n    typeName: ZodFirstPartyTypeKind.ZodCatch,\n    catchValue: typeof params.catch === \"function\" ? params.catch : () => params.catch,\n    ...processCreateParams(params)\n  });\n};\nvar ZodNaN = class extends ZodType {\n  _parse(input) {\n    const parsedType = this._getType(input);\n    if (parsedType !== ZodParsedType.nan) {\n      const ctx = this._getOrReturnCtx(input);\n      addIssueToContext(ctx, {\n        code: ZodIssueCode.invalid_type,\n        expected: ZodParsedType.nan,\n        received: ctx.parsedType\n      });\n      return INVALID;\n    }\n    return { status: \"valid\", value: input.data };\n  }\n};\nZodNaN.create = (params) => {\n  return new ZodNaN({\n    typeName: ZodFirstPartyTypeKind.ZodNaN,\n    ...processCreateParams(params)\n  });\n};\nvar BRAND = Symbol(\"zod_brand\");\nvar ZodBranded = class extends ZodType {\n  _parse(input) {\n    const { ctx } = this._processInputParams(input);\n    const data = ctx.data;\n    return this._def.type._parse({\n      data,\n      path: ctx.path,\n      parent: ctx\n    });\n  }\n  unwrap() {\n    return this._def.type;\n  }\n};\nvar ZodPipeline = class _ZodPipeline extends ZodType {\n  _parse(input) {\n    const { status, ctx } = this._processInputParams(input);\n    if (ctx.common.async) {\n      const handleAsync = async () => {\n        const inResult = await this._def.in._parseAsync({\n          data: ctx.data,\n          path: ctx.path,\n          parent: ctx\n        });\n        if (inResult.status === \"aborted\")\n          return INVALID;\n        if (inResult.status === \"dirty\") {\n          status.dirty();\n          return DIRTY(inResult.value);\n        } else {\n          return this._def.out._parseAsync({\n            data: inResult.value,\n            path: ctx.path,\n            parent: ctx\n          });\n        }\n      };\n      return handleAsync();\n    } else {\n      const inResult = this._def.in._parseSync({\n        data: ctx.data,\n        path: ctx.path,\n        parent: ctx\n      });\n      if (inResult.status === \"aborted\")\n        return INVALID;\n      if (inResult.status === \"dirty\") {\n        status.dirty();\n        return {\n          status: \"dirty\",\n          value: inResult.value\n        };\n      } else {\n        return this._def.out._parseSync({\n          data: inResult.value,\n          path: ctx.path,\n          parent: ctx\n        });\n      }\n    }\n  }\n  static create(a, b) {\n    return new _ZodPipeline({\n      in: a,\n      out: b,\n      typeName: ZodFirstPartyTypeKind.ZodPipeline\n    });\n  }\n};\nvar ZodReadonly = class extends ZodType {\n  _parse(input) {\n    const result = this._def.innerType._parse(input);\n    const freeze = (data) => {\n      if (isValid(data)) {\n        data.value = Object.freeze(data.value);\n      }\n      return data;\n    };\n    return isAsync(result) ? result.then((data) => freeze(data)) : freeze(result);\n  }\n  unwrap() {\n    return this._def.innerType;\n  }\n};\nZodReadonly.create = (type, params) => {\n  return new ZodReadonly({\n    innerType: type,\n    typeName: ZodFirstPartyTypeKind.ZodReadonly,\n    ...processCreateParams(params)\n  });\n};\nvar late = {\n  object: ZodObject.lazycreate\n};\nvar ZodFirstPartyTypeKind;\n(function(ZodFirstPartyTypeKind2) {\n  ZodFirstPartyTypeKind2[\"ZodString\"] = \"ZodString\";\n  ZodFirstPartyTypeKind2[\"ZodNumber\"] = \"ZodNumber\";\n  ZodFirstPartyTypeKind2[\"ZodNaN\"] = \"ZodNaN\";\n  ZodFirstPartyTypeKind2[\"ZodBigInt\"] = \"ZodBigInt\";\n  ZodFirstPartyTypeKind2[\"ZodBoolean\"] = \"ZodBoolean\";\n  ZodFirstPartyTypeKind2[\"ZodDate\"] = \"ZodDate\";\n  ZodFirstPartyTypeKind2[\"ZodSymbol\"] = \"ZodSymbol\";\n  ZodFirstPartyTypeKind2[\"ZodUndefined\"] = \"ZodUndefined\";\n  ZodFirstPartyTypeKind2[\"ZodNull\"] = \"ZodNull\";\n  ZodFirstPartyTypeKind2[\"ZodAny\"] = \"ZodAny\";\n  ZodFirstPartyTypeKind2[\"ZodUnknown\"] = \"ZodUnknown\";\n  ZodFirstPartyTypeKind2[\"ZodNever\"] = \"ZodNever\";\n  ZodFirstPartyTypeKind2[\"ZodVoid\"] = \"ZodVoid\";\n  ZodFirstPartyTypeKind2[\"ZodArray\"] = \"ZodArray\";\n  ZodFirstPartyTypeKind2[\"ZodObject\"] = \"ZodObject\";\n  ZodFirstPartyTypeKind2[\"ZodUnion\"] = \"ZodUnion\";\n  ZodFirstPartyTypeKind2[\"ZodDiscriminatedUnion\"] = \"ZodDiscriminatedUnion\";\n  ZodFirstPartyTypeKind2[\"ZodIntersection\"] = \"ZodIntersection\";\n  ZodFirstPartyTypeKind2[\"ZodTuple\"] = \"ZodTuple\";\n  ZodFirstPartyTypeKind2[\"ZodRecord\"] = \"ZodRecord\";\n  ZodFirstPartyTypeKind2[\"ZodMap\"] = \"ZodMap\";\n  ZodFirstPartyTypeKind2[\"ZodSet\"] = \"ZodSet\";\n  ZodFirstPartyTypeKind2[\"ZodFunction\"] = \"ZodFunction\";\n  ZodFirstPartyTypeKind2[\"ZodLazy\"] = \"ZodLazy\";\n  ZodFirstPartyTypeKind2[\"ZodLiteral\"] = \"ZodLiteral\";\n  ZodFirstPartyTypeKind2[\"ZodEnum\"] = \"ZodEnum\";\n  ZodFirstPartyTypeKind2[\"ZodEffects\"] = \"ZodEffects\";\n  ZodFirstPartyTypeKind2[\"ZodNativeEnum\"] = \"ZodNativeEnum\";\n  ZodFirstPartyTypeKind2[\"ZodOptional\"] = \"ZodOptional\";\n  ZodFirstPartyTypeKind2[\"ZodNullable\"] = \"ZodNullable\";\n  ZodFirstPartyTypeKind2[\"ZodDefault\"] = \"ZodDefault\";\n  ZodFirstPartyTypeKind2[\"ZodCatch\"] = \"ZodCatch\";\n  ZodFirstPartyTypeKind2[\"ZodPromise\"] = \"ZodPromise\";\n  ZodFirstPartyTypeKind2[\"ZodBranded\"] = \"ZodBranded\";\n  ZodFirstPartyTypeKind2[\"ZodPipeline\"] = \"ZodPipeline\";\n  ZodFirstPartyTypeKind2[\"ZodReadonly\"] = \"ZodReadonly\";\n})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));\nvar stringType = ZodString.create;\nvar numberType = ZodNumber.create;\nvar nanType = ZodNaN.create;\nvar bigIntType = ZodBigInt.create;\nvar booleanType = ZodBoolean.create;\nvar dateType = ZodDate.create;\nvar symbolType = ZodSymbol.create;\nvar undefinedType = ZodUndefined.create;\nvar nullType = ZodNull.create;\nvar anyType = ZodAny.create;\nvar unknownType = ZodUnknown.create;\nvar neverType = ZodNever.create;\nvar voidType = ZodVoid.create;\nvar arrayType = ZodArray.create;\nvar objectType = ZodObject.create;\nvar strictObjectType = ZodObject.strictCreate;\nvar unionType = ZodUnion.create;\nvar discriminatedUnionType = ZodDiscriminatedUnion.create;\nvar intersectionType = ZodIntersection.create;\nvar tupleType = ZodTuple.create;\nvar recordType = ZodRecord.create;\nvar mapType = ZodMap.create;\nvar setType = ZodSet.create;\nvar functionType = ZodFunction.create;\nvar lazyType = ZodLazy.create;\nvar literalType = ZodLiteral.create;\nvar enumType = ZodEnum.create;\nvar nativeEnumType = ZodNativeEnum.create;\nvar promiseType = ZodPromise.create;\nvar effectsType = ZodEffects.create;\nvar optionalType = ZodOptional.create;\nvar nullableType = ZodNullable.create;\nvar preprocessType = ZodEffects.createWithPreprocess;\nvar pipelineType = ZodPipeline.create;\n\n// src/openapi.ts\nvar paths = {};\nfunction getTypeFromZodType(zodType) {\n  switch (zodType.constructor.name) {\n    case \"ZodString\":\n      return \"string\";\n    case \"ZodNumber\":\n      return \"number\";\n    case \"ZodBoolean\":\n      return \"boolean\";\n    case \"ZodObject\":\n      return \"object\";\n    case \"ZodArray\":\n      return \"array\";\n    default:\n      return \"string\";\n  }\n}\nfunction getParameters(options) {\n  const parameters = [];\n  if (options.metadata?.openapi?.parameters) {\n    parameters.push(...options.metadata.openapi.parameters);\n    return parameters;\n  }\n  if (options.query instanceof ZodObject) {\n    Object.entries(options.query.shape).forEach(([key, value]) => {\n      if (value instanceof ZodType) {\n        parameters.push({\n          name: key,\n          in: \"query\",\n          schema: {\n            type: getTypeFromZodType(value),\n            ...\"minLength\" in value && value.minLength ? {\n              minLength: value.minLength\n            } : {},\n            description: value.description\n          }\n        });\n      }\n    });\n  }\n  return parameters;\n}\nfunction getRequestBody(options) {\n  if (options.metadata?.openapi?.requestBody) {\n    return options.metadata.openapi.requestBody;\n  }\n  if (!options.body) return void 0;\n  if (options.body instanceof ZodObject || options.body instanceof ZodOptional) {\n    const shape = options.body.shape;\n    if (!shape) return void 0;\n    const properties = {};\n    const required = [];\n    Object.entries(shape).forEach(([key, value]) => {\n      if (value instanceof ZodType) {\n        properties[key] = {\n          type: getTypeFromZodType(value),\n          description: value.description\n        };\n        if (!(value instanceof ZodOptional)) {\n          required.push(key);\n        }\n      }\n    });\n    return {\n      required: options.body instanceof ZodOptional ? false : options.body ? true : false,\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties,\n            required\n          }\n        }\n      }\n    };\n  }\n  return void 0;\n}\nfunction getResponse(responses) {\n  return {\n    \"400\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            },\n            required: [\"message\"]\n          }\n        }\n      },\n      description: \"Bad Request. Usually due to missing parameters, or invalid parameters.\"\n    },\n    \"401\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            },\n            required: [\"message\"]\n          }\n        }\n      },\n      description: \"Unauthorized. Due to missing or invalid authentication.\"\n    },\n    \"403\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Forbidden. You do not have permission to access this resource or to perform this action.\"\n    },\n    \"404\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Not Found. The requested resource was not found.\"\n    },\n    \"429\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Too Many Requests. You have exceeded the rate limit. Try again later.\"\n    },\n    \"500\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Internal Server Error. This is a problem with the server that you cannot fix.\"\n    },\n    ...responses\n  };\n}\nasync function generator(endpoints, config) {\n  const components = {\n    schemas: {}\n  };\n  Object.entries(endpoints).forEach(([_, value]) => {\n    const options = value.options;\n    if (options.metadata?.SERVER_ONLY) return;\n    if (options.method === \"GET\") {\n      paths[value.path] = {\n        get: {\n          tags: [\"Default\", ...options.metadata?.openapi?.tags || []],\n          description: options.metadata?.openapi?.description,\n          operationId: options.metadata?.openapi?.operationId,\n          security: [\n            {\n              bearerAuth: []\n            }\n          ],\n          parameters: getParameters(options),\n          responses: getResponse(options.metadata?.openapi?.responses)\n        }\n      };\n    }\n    if (options.method === \"POST\") {\n      const body = getRequestBody(options);\n      paths[value.path] = {\n        post: {\n          tags: [\"Default\", ...options.metadata?.openapi?.tags || []],\n          description: options.metadata?.openapi?.description,\n          operationId: options.metadata?.openapi?.operationId,\n          security: [\n            {\n              bearerAuth: []\n            }\n          ],\n          parameters: getParameters(options),\n          ...body ? { requestBody: body } : {\n            requestBody: {\n              //set body none\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {}\n                  }\n                }\n              }\n            }\n          },\n          responses: getResponse(options.metadata?.openapi?.responses)\n        }\n      };\n    }\n  });\n  const res = {\n    openapi: \"3.1.1\",\n    info: {\n      title: \"Better Auth\",\n      description: \"API Reference for your Better Auth Instance\",\n      version: \"1.1.0\"\n    },\n    components,\n    security: [\n      {\n        apiKeyCookie: []\n      }\n    ],\n    servers: [\n      {\n        url: config?.url\n      }\n    ],\n    tags: [\n      {\n        name: \"Default\",\n        description: \"Default endpoints that are included with Better Auth by default. These endpoints are not part of any plugin.\"\n      }\n    ],\n    paths\n  };\n  return res;\n}\nvar getHTML = (apiReference, config) => `<!doctype html>\n<html>\n  <head>\n    <title>Scalar API Reference</title>\n    <meta charset=\"utf-8\" />\n    <meta\n      name=\"viewport\"\n      content=\"width=device-width, initial-scale=1\" />\n  </head>\n  <body>\n    <script\n      id=\"api-reference\"\n      type=\"application/json\">\n    ${JSON.stringify(apiReference)}\n    </script>\n\t <script>\n      var configuration = {\n\t  \tfavicon: ${config?.logo ? `data:image/svg+xml;utf8,${encodeURIComponent(config.logo)}` : void 0} ,\n\t   \ttheme: ${config?.theme || \"saturn\"},\n        metaData: {\n\t\t\ttitle: ${config?.title || \"Open API Reference\"},\n\t\t\tdescription: ${config?.description || \"Better Call Open API\"},\n\t\t}\n      }\n      document.getElementById('api-reference').dataset.configuration =\n        JSON.stringify(configuration)\n    </script>\n\t  <script src=\"https://cdn.jsdelivr.net/npm/@scalar/api-reference\"></script>\n  </body>\n</html>`;\n\n// src/router.ts\nvar createRouter = (endpoints, config) => {\n  if (!config?.openapi?.disabled) {\n    const openapi = {\n      path: \"/api/reference\",\n      ...config?.openapi\n    };\n    endpoints[\"openapi\"] = createEndpoint2(\n      openapi.path,\n      {\n        method: \"GET\"\n      },\n      async (c) => {\n        const schema = await generator(endpoints);\n        return new Response(getHTML(schema, openapi.scalar), {\n          headers: {\n            \"Content-Type\": \"text/html\"\n          }\n        });\n      }\n    );\n  }\n  const router = (0,rou3__WEBPACK_IMPORTED_MODULE_1__.createRouter)();\n  const middlewareRouter = (0,rou3__WEBPACK_IMPORTED_MODULE_1__.createRouter)();\n  for (const endpoint of Object.values(endpoints)) {\n    if (!endpoint.options) {\n      continue;\n    }\n    if (endpoint.options?.metadata?.SERVER_ONLY) continue;\n    const methods = Array.isArray(endpoint.options?.method) ? endpoint.options.method : [endpoint.options?.method];\n    for (const method of methods) {\n      (0,rou3__WEBPACK_IMPORTED_MODULE_1__.addRoute)(router, method, endpoint.path, endpoint);\n    }\n  }\n  if (config?.routerMiddleware?.length) {\n    for (const { path, middleware } of config.routerMiddleware) {\n      (0,rou3__WEBPACK_IMPORTED_MODULE_1__.addRoute)(middlewareRouter, \"*\", path, middleware);\n    }\n  }\n  const processRequest = async (request) => {\n    const url = new URL(request.url);\n    const path = config?.basePath ? url.pathname.split(config.basePath).reduce((acc, curr, index) => {\n      if (index !== 0) {\n        if (index > 1) {\n          acc.push(`${config.basePath}${curr}`);\n        } else {\n          acc.push(curr);\n        }\n      }\n      return acc;\n    }, []).join(\"\") : url.pathname;\n    if (!path?.length) {\n      return new Response(null, { status: 404, statusText: \"Not Found\" });\n    }\n    const route = (0,rou3__WEBPACK_IMPORTED_MODULE_1__.findRoute)(router, request.method, path);\n    if (!route?.data) {\n      return new Response(null, { status: 404, statusText: \"Not Found\" });\n    }\n    const query = {};\n    url.searchParams.forEach((value, key) => {\n      if (key in query) {\n        if (Array.isArray(query[key])) {\n          query[key].push(value);\n        } else {\n          query[key] = [query[key], value];\n        }\n      } else {\n        query[key] = value;\n      }\n    });\n    const handler = route.data;\n    const context = {\n      path,\n      method: request.method,\n      headers: request.headers,\n      params: route.params ? JSON.parse(JSON.stringify(route.params)) : {},\n      request,\n      body: handler.options.disableBody ? void 0 : await getBody(handler.options.cloneRequest ? request.clone() : request),\n      query,\n      _flag: \"router\",\n      asResponse: true,\n      context: config?.routerContext\n    };\n    try {\n      const middlewareRoutes = (0,rou3__WEBPACK_IMPORTED_MODULE_1__.findAllRoutes)(middlewareRouter, \"*\", path);\n      if (middlewareRoutes?.length) {\n        for (const { data: middleware, params } of middlewareRoutes) {\n          const res = await middleware({\n            ...context,\n            params,\n            asResponse: false\n          });\n          if (res instanceof Response) return res;\n        }\n      }\n      const response = await handler(context);\n      return response;\n    } catch (error) {\n      if (isAPIError(error)) {\n        return toResponse(error);\n      }\n      console.error(`# SERVER_ERROR: `, error);\n      return new Response(null, {\n        status: 500,\n        statusText: \"Internal Server Error\"\n      });\n    }\n  };\n  return {\n    handler: async (request) => {\n      const onReq = await config?.onRequest?.(request);\n      if (onReq instanceof Response) {\n        return onReq;\n      }\n      const req = onReq instanceof Request ? onReq : request;\n      const res = await processRequest(req);\n      const onRes = await config?.onResponse?.(res);\n      if (onRes instanceof Response) {\n        return onRes;\n      }\n      return res;\n    },\n    endpoints\n  };\n};\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/better-call/dist/index.js\n");

/***/ })

};
;