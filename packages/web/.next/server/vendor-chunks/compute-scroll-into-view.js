"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compute-scroll-into-view";
exports.ids = ["vendor-chunks/compute-scroll-into-view"];
exports.modules = {

/***/ "(ssr)/../../node_modules/compute-scroll-into-view/dist/index.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/compute-scroll-into-view/dist/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compute: () => (/* binding */ r)\n/* harmony export */ });\nconst t=t=>\"object\"==typeof t&&null!=t&&1===t.nodeType,e=(t,e)=>(!e||\"hidden\"!==t)&&(\"visible\"!==t&&\"clip\"!==t),n=(t,n)=>{if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){const o=getComputedStyle(t,null);return e(o.overflowY,n)||e(o.overflowX,n)||(t=>{const e=(t=>{if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch(t){return null}})(t);return!!e&&(e.clientHeight<t.scrollHeight||e.clientWidth<t.scrollWidth)})(t)}return!1},o=(t,e,n,o,l,r,i,s)=>r<t&&i>e||r>t&&i<e?0:r<=t&&s<=n||i>=e&&s>=n?r-t-o:i>e&&s<n||r<t&&s>n?i-e+l:0,l=t=>{const e=t.parentElement;return null==e?t.getRootNode().host||null:e},r=(e,r)=>{var i,s,d,h;if(\"undefined\"==typeof document)return[];const{scrollMode:c,block:f,inline:u,boundary:a,skipOverflowHiddenElements:g}=r,p=\"function\"==typeof a?a:t=>t!==a;if(!t(e))throw new TypeError(\"Invalid target\");const m=document.scrollingElement||document.documentElement,w=[];let W=e;for(;t(W)&&p(W);){if(W=l(W),W===m){w.push(W);break}null!=W&&W===document.body&&n(W)&&!n(document.documentElement)||null!=W&&n(W,g)&&w.push(W)}const b=null!=(s=null==(i=window.visualViewport)?void 0:i.width)?s:innerWidth,H=null!=(h=null==(d=window.visualViewport)?void 0:d.height)?h:innerHeight,{scrollX:y,scrollY:M}=window,{height:v,width:E,top:x,right:C,bottom:I,left:R}=e.getBoundingClientRect(),{top:T,right:B,bottom:F,left:V}=(t=>{const e=window.getComputedStyle(t);return{top:parseFloat(e.scrollMarginTop)||0,right:parseFloat(e.scrollMarginRight)||0,bottom:parseFloat(e.scrollMarginBottom)||0,left:parseFloat(e.scrollMarginLeft)||0}})(e);let k=\"start\"===f||\"nearest\"===f?x-T:\"end\"===f?I+F:x+v/2-T+F,D=\"center\"===u?R+E/2-V+B:\"end\"===u?C+B:R-V;const L=[];for(let t=0;t<w.length;t++){const e=w[t],{height:l,width:r,top:i,right:s,bottom:d,left:h}=e.getBoundingClientRect();if(\"if-needed\"===c&&x>=0&&R>=0&&I<=H&&C<=b&&(e===m&&!n(e)||x>=i&&I<=d&&R>=h&&C<=s))return L;const a=getComputedStyle(e),g=parseInt(a.borderLeftWidth,10),p=parseInt(a.borderTopWidth,10),W=parseInt(a.borderRightWidth,10),T=parseInt(a.borderBottomWidth,10);let B=0,F=0;const V=\"offsetWidth\"in e?e.offsetWidth-e.clientWidth-g-W:0,S=\"offsetHeight\"in e?e.offsetHeight-e.clientHeight-p-T:0,X=\"offsetWidth\"in e?0===e.offsetWidth?0:r/e.offsetWidth:0,Y=\"offsetHeight\"in e?0===e.offsetHeight?0:l/e.offsetHeight:0;if(m===e)B=\"start\"===f?k:\"end\"===f?k-H:\"nearest\"===f?o(M,M+H,H,p,T,M+k,M+k+v,v):k-H/2,F=\"start\"===u?D:\"center\"===u?D-b/2:\"end\"===u?D-b:o(y,y+b,b,g,W,y+D,y+D+E,E),B=Math.max(0,B+M),F=Math.max(0,F+y);else{B=\"start\"===f?k-i-p:\"end\"===f?k-d+T+S:\"nearest\"===f?o(i,d,l,p,T+S,k,k+v,v):k-(i+l/2)+S/2,F=\"start\"===u?D-h-g:\"center\"===u?D-(h+r/2)+V/2:\"end\"===u?D-s+W+V:o(h,s,r,g,W+V,D,D+E,E);const{scrollLeft:t,scrollTop:n}=e;B=0===Y?0:Math.max(0,Math.min(n+B/Y,e.scrollHeight-l/Y+S)),F=0===X?0:Math.max(0,Math.min(t+F/X,e.scrollWidth-r/X+V)),k+=n-B,D+=t-F}L.push({el:e,top:B,left:F})}return L};//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/compute-scroll-into-view/dist/index.js\n");

/***/ })

};
;