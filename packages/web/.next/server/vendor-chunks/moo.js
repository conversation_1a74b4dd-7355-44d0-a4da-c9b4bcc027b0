/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/moo";
exports.ids = ["vendor-chunks/moo"];
exports.modules = {

/***/ "(rsc)/../../node_modules/moo/moo.js":
/*!*************************************!*\
  !*** ../../node_modules/moo/moo.js ***!
  \*************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function(root, factory) {\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__)) /* global define */\n  } else {}\n}(this, function() {\n  'use strict';\n\n  var hasOwnProperty = Object.prototype.hasOwnProperty\n  var toString = Object.prototype.toString\n  var hasSticky = typeof new RegExp().sticky === 'boolean'\n\n  /***************************************************************************/\n\n  function isRegExp(o) { return o && toString.call(o) === '[object RegExp]' }\n  function isObject(o) { return o && typeof o === 'object' && !isRegExp(o) && !Array.isArray(o) }\n\n  function reEscape(s) {\n    return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n  }\n  function reGroups(s) {\n    var re = new RegExp('|' + s)\n    return re.exec('').length - 1\n  }\n  function reCapture(s) {\n    return '(' + s + ')'\n  }\n  function reUnion(regexps) {\n    if (!regexps.length) return '(?!)'\n    var source =  regexps.map(function(s) {\n      return \"(?:\" + s + \")\"\n    }).join('|')\n    return \"(?:\" + source + \")\"\n  }\n\n  function regexpOrLiteral(obj) {\n    if (typeof obj === 'string') {\n      return '(?:' + reEscape(obj) + ')'\n\n    } else if (isRegExp(obj)) {\n      // TODO: consider /u support\n      if (obj.ignoreCase) throw new Error('RegExp /i flag not allowed')\n      if (obj.global) throw new Error('RegExp /g flag is implied')\n      if (obj.sticky) throw new Error('RegExp /y flag is implied')\n      if (obj.multiline) throw new Error('RegExp /m flag is implied')\n      return obj.source\n\n    } else {\n      throw new Error('Not a pattern: ' + obj)\n    }\n  }\n\n  function pad(s, length) {\n    if (s.length > length) {\n      return s\n    }\n    return Array(length - s.length + 1).join(\" \") + s\n  }\n\n  function lastNLines(string, numLines) {\n    var position = string.length\n    var lineBreaks = 0;\n    while (true) {\n      var idx = string.lastIndexOf(\"\\n\", position - 1)\n      if (idx === -1) {\n        break;\n      } else {\n        lineBreaks++\n      }\n      position = idx\n      if (lineBreaks === numLines) {\n        break;\n      }\n      if (position === 0) {\n        break;\n      }\n    }\n    var startPosition = \n      lineBreaks < numLines ?\n      0 : \n      position + 1\n    return string.substring(startPosition).split(\"\\n\")\n  }\n\n  function objectToRules(object) {\n    var keys = Object.getOwnPropertyNames(object)\n    var result = []\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var thing = object[key]\n      var rules = [].concat(thing)\n      if (key === 'include') {\n        for (var j = 0; j < rules.length; j++) {\n          result.push({include: rules[j]})\n        }\n        continue\n      }\n      var match = []\n      rules.forEach(function(rule) {\n        if (isObject(rule)) {\n          if (match.length) result.push(ruleOptions(key, match))\n          result.push(ruleOptions(key, rule))\n          match = []\n        } else {\n          match.push(rule)\n        }\n      })\n      if (match.length) result.push(ruleOptions(key, match))\n    }\n    return result\n  }\n\n  function arrayToRules(array) {\n    var result = []\n    for (var i = 0; i < array.length; i++) {\n      var obj = array[i]\n      if (obj.include) {\n        var include = [].concat(obj.include)\n        for (var j = 0; j < include.length; j++) {\n          result.push({include: include[j]})\n        }\n        continue\n      }\n      if (!obj.type) {\n        throw new Error('Rule has no type: ' + JSON.stringify(obj))\n      }\n      result.push(ruleOptions(obj.type, obj))\n    }\n    return result\n  }\n\n  function ruleOptions(type, obj) {\n    if (!isObject(obj)) {\n      obj = { match: obj }\n    }\n    if (obj.include) {\n      throw new Error('Matching rules cannot also include states')\n    }\n\n    // nb. error and fallback imply lineBreaks\n    var options = {\n      defaultType: type,\n      lineBreaks: !!obj.error || !!obj.fallback,\n      pop: false,\n      next: null,\n      push: null,\n      error: false,\n      fallback: false,\n      value: null,\n      type: null,\n      shouldThrow: false,\n    }\n\n    // Avoid Object.assign(), so we support IE9+\n    for (var key in obj) {\n      if (hasOwnProperty.call(obj, key)) {\n        options[key] = obj[key]\n      }\n    }\n\n    // type transform cannot be a string\n    if (typeof options.type === 'string' && type !== options.type) {\n      throw new Error(\"Type transform cannot be a string (type '\" + options.type + \"' for token '\" + type + \"')\")\n    }\n\n    // convert to array\n    var match = options.match\n    options.match = Array.isArray(match) ? match : match ? [match] : []\n    options.match.sort(function(a, b) {\n      return isRegExp(a) && isRegExp(b) ? 0\n           : isRegExp(b) ? -1 : isRegExp(a) ? +1 : b.length - a.length\n    })\n    return options\n  }\n\n  function toRules(spec) {\n    return Array.isArray(spec) ? arrayToRules(spec) : objectToRules(spec)\n  }\n\n  var defaultErrorRule = ruleOptions('error', {lineBreaks: true, shouldThrow: true})\n  function compileRules(rules, hasStates) {\n    var errorRule = null\n    var fast = Object.create(null)\n    var fastAllowed = true\n    var unicodeFlag = null\n    var groups = []\n    var parts = []\n\n    // If there is a fallback rule, then disable fast matching\n    for (var i = 0; i < rules.length; i++) {\n      if (rules[i].fallback) {\n        fastAllowed = false\n      }\n    }\n\n    for (var i = 0; i < rules.length; i++) {\n      var options = rules[i]\n\n      if (options.include) {\n        // all valid inclusions are removed by states() preprocessor\n        throw new Error('Inheritance is not allowed in stateless lexers')\n      }\n\n      if (options.error || options.fallback) {\n        // errorRule can only be set once\n        if (errorRule) {\n          if (!options.fallback === !errorRule.fallback) {\n            throw new Error(\"Multiple \" + (options.fallback ? \"fallback\" : \"error\") + \" rules not allowed (for token '\" + options.defaultType + \"')\")\n          } else {\n            throw new Error(\"fallback and error are mutually exclusive (for token '\" + options.defaultType + \"')\")\n          }\n        }\n        errorRule = options\n      }\n\n      var match = options.match.slice()\n      if (fastAllowed) {\n        while (match.length && typeof match[0] === 'string' && match[0].length === 1) {\n          var word = match.shift()\n          fast[word.charCodeAt(0)] = options\n        }\n      }\n\n      // Warn about inappropriate state-switching options\n      if (options.pop || options.push || options.next) {\n        if (!hasStates) {\n          throw new Error(\"State-switching options are not allowed in stateless lexers (for token '\" + options.defaultType + \"')\")\n        }\n        if (options.fallback) {\n          throw new Error(\"State-switching options are not allowed on fallback tokens (for token '\" + options.defaultType + \"')\")\n        }\n      }\n\n      // Only rules with a .match are included in the RegExp\n      if (match.length === 0) {\n        continue\n      }\n      fastAllowed = false\n\n      groups.push(options)\n\n      // Check unicode flag is used everywhere or nowhere\n      for (var j = 0; j < match.length; j++) {\n        var obj = match[j]\n        if (!isRegExp(obj)) {\n          continue\n        }\n\n        if (unicodeFlag === null) {\n          unicodeFlag = obj.unicode\n        } else if (unicodeFlag !== obj.unicode && options.fallback === false) {\n          throw new Error('If one rule is /u then all must be')\n        }\n      }\n\n      // convert to RegExp\n      var pat = reUnion(match.map(regexpOrLiteral))\n\n      // validate\n      var regexp = new RegExp(pat)\n      if (regexp.test(\"\")) {\n        throw new Error(\"RegExp matches empty string: \" + regexp)\n      }\n      var groupCount = reGroups(pat)\n      if (groupCount > 0) {\n        throw new Error(\"RegExp has capture groups: \" + regexp + \"\\nUse (?: … ) instead\")\n      }\n\n      // try and detect rules matching newlines\n      if (!options.lineBreaks && regexp.test('\\n')) {\n        throw new Error('Rule should declare lineBreaks: ' + regexp)\n      }\n\n      // store regex\n      parts.push(reCapture(pat))\n    }\n\n\n    // If there's no fallback rule, use the sticky flag so we only look for\n    // matches at the current index.\n    //\n    // If we don't support the sticky flag, then fake it using an irrefutable\n    // match (i.e. an empty pattern).\n    var fallbackRule = errorRule && errorRule.fallback\n    var flags = hasSticky && !fallbackRule ? 'ym' : 'gm'\n    var suffix = hasSticky || fallbackRule ? '' : '|'\n\n    if (unicodeFlag === true) flags += \"u\"\n    var combined = new RegExp(reUnion(parts) + suffix, flags)\n    return {regexp: combined, groups: groups, fast: fast, error: errorRule || defaultErrorRule}\n  }\n\n  function compile(rules) {\n    var result = compileRules(toRules(rules))\n    return new Lexer({start: result}, 'start')\n  }\n\n  function checkStateGroup(g, name, map) {\n    var state = g && (g.push || g.next)\n    if (state && !map[state]) {\n      throw new Error(\"Missing state '\" + state + \"' (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n    if (g && g.pop && +g.pop !== 1) {\n      throw new Error(\"pop must be 1 (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n  }\n  function compileStates(states, start) {\n    var all = states.$all ? toRules(states.$all) : []\n    delete states.$all\n\n    var keys = Object.getOwnPropertyNames(states)\n    if (!start) start = keys[0]\n\n    var ruleMap = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      ruleMap[key] = toRules(states[key]).concat(all)\n    }\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var rules = ruleMap[key]\n      var included = Object.create(null)\n      for (var j = 0; j < rules.length; j++) {\n        var rule = rules[j]\n        if (!rule.include) continue\n        var splice = [j, 1]\n        if (rule.include !== key && !included[rule.include]) {\n          included[rule.include] = true\n          var newRules = ruleMap[rule.include]\n          if (!newRules) {\n            throw new Error(\"Cannot include nonexistent state '\" + rule.include + \"' (in state '\" + key + \"')\")\n          }\n          for (var k = 0; k < newRules.length; k++) {\n            var newRule = newRules[k]\n            if (rules.indexOf(newRule) !== -1) continue\n            splice.push(newRule)\n          }\n        }\n        rules.splice.apply(rules, splice)\n        j--\n      }\n    }\n\n    var map = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      map[key] = compileRules(ruleMap[key], true)\n    }\n\n    for (var i = 0; i < keys.length; i++) {\n      var name = keys[i]\n      var state = map[name]\n      var groups = state.groups\n      for (var j = 0; j < groups.length; j++) {\n        checkStateGroup(groups[j], name, map)\n      }\n      var fastKeys = Object.getOwnPropertyNames(state.fast)\n      for (var j = 0; j < fastKeys.length; j++) {\n        checkStateGroup(state.fast[fastKeys[j]], name, map)\n      }\n    }\n\n    return new Lexer(map, start)\n  }\n\n  function keywordTransform(map) {\n\n    // Use a JavaScript Map to map keywords to their corresponding token type\n    // unless Map is unsupported, then fall back to using an Object:\n    var isMap = typeof Map !== 'undefined'\n    var reverseMap = isMap ? new Map : Object.create(null)\n\n    var types = Object.getOwnPropertyNames(map)\n    for (var i = 0; i < types.length; i++) {\n      var tokenType = types[i]\n      var item = map[tokenType]\n      var keywordList = Array.isArray(item) ? item : [item]\n      keywordList.forEach(function(keyword) {\n        if (typeof keyword !== 'string') {\n          throw new Error(\"keyword must be string (in keyword '\" + tokenType + \"')\")\n        }\n        if (isMap) {\n          reverseMap.set(keyword, tokenType)\n        } else {\n          reverseMap[keyword] = tokenType\n        }\n      })\n    }\n    return function(k) {\n      return isMap ? reverseMap.get(k) : reverseMap[k]\n    }\n  }\n\n  /***************************************************************************/\n\n  var Lexer = function(states, state) {\n    this.startState = state\n    this.states = states\n    this.buffer = ''\n    this.stack = []\n    this.reset()\n  }\n\n  Lexer.prototype.reset = function(data, info) {\n    this.buffer = data || ''\n    this.index = 0\n    this.line = info ? info.line : 1\n    this.col = info ? info.col : 1\n    this.queuedToken = info ? info.queuedToken : null\n    this.queuedText = info ? info.queuedText: \"\";\n    this.queuedThrow = info ? info.queuedThrow : null\n    this.setState(info ? info.state : this.startState)\n    this.stack = info && info.stack ? info.stack.slice() : []\n    return this\n  }\n\n  Lexer.prototype.save = function() {\n    return {\n      line: this.line,\n      col: this.col,\n      state: this.state,\n      stack: this.stack.slice(),\n      queuedToken: this.queuedToken,\n      queuedText: this.queuedText,\n      queuedThrow: this.queuedThrow,\n    }\n  }\n\n  Lexer.prototype.setState = function(state) {\n    if (!state || this.state === state) return\n    this.state = state\n    var info = this.states[state]\n    this.groups = info.groups\n    this.error = info.error\n    this.re = info.regexp\n    this.fast = info.fast\n  }\n\n  Lexer.prototype.popState = function() {\n    this.setState(this.stack.pop())\n  }\n\n  Lexer.prototype.pushState = function(state) {\n    this.stack.push(this.state)\n    this.setState(state)\n  }\n\n  var eat = hasSticky ? function(re, buffer) { // assume re is /y\n    return re.exec(buffer)\n  } : function(re, buffer) { // assume re is /g\n    var match = re.exec(buffer)\n    // will always match, since we used the |(?:) trick\n    if (match[0].length === 0) {\n      return null\n    }\n    return match\n  }\n\n  Lexer.prototype._getGroup = function(match) {\n    var groupCount = this.groups.length\n    for (var i = 0; i < groupCount; i++) {\n      if (match[i + 1] !== undefined) {\n        return this.groups[i]\n      }\n    }\n    throw new Error('Cannot find token type for matched text')\n  }\n\n  function tokenToString() {\n    return this.value\n  }\n\n  Lexer.prototype.next = function() {\n    var index = this.index\n\n    // If a fallback token matched, we don't need to re-run the RegExp\n    if (this.queuedGroup) {\n      var token = this._token(this.queuedGroup, this.queuedText, index)\n      this.queuedGroup = null\n      this.queuedText = \"\"\n      return token\n    }\n\n    var buffer = this.buffer\n    if (index === buffer.length) {\n      return // EOF\n    }\n\n    // Fast matching for single characters\n    var group = this.fast[buffer.charCodeAt(index)]\n    if (group) {\n      return this._token(group, buffer.charAt(index), index)\n    }\n\n    // Execute RegExp\n    var re = this.re\n    re.lastIndex = index\n    var match = eat(re, buffer)\n\n    // Error tokens match the remaining buffer\n    var error = this.error\n    if (match == null) {\n      return this._token(error, buffer.slice(index, buffer.length), index)\n    }\n\n    var group = this._getGroup(match)\n    var text = match[0]\n\n    if (error.fallback && match.index !== index) {\n      this.queuedGroup = group\n      this.queuedText = text\n\n      // Fallback tokens contain the unmatched portion of the buffer\n      return this._token(error, buffer.slice(index, match.index), index)\n    }\n\n    return this._token(group, text, index)\n  }\n\n  Lexer.prototype._token = function(group, text, offset) {\n    // count line breaks\n    var lineBreaks = 0\n    if (group.lineBreaks) {\n      var matchNL = /\\n/g\n      var nl = 1\n      if (text === '\\n') {\n        lineBreaks = 1\n      } else {\n        while (matchNL.exec(text)) { lineBreaks++; nl = matchNL.lastIndex }\n      }\n    }\n\n    var token = {\n      type: (typeof group.type === 'function' && group.type(text)) || group.defaultType,\n      value: typeof group.value === 'function' ? group.value(text) : text,\n      text: text,\n      toString: tokenToString,\n      offset: offset,\n      lineBreaks: lineBreaks,\n      line: this.line,\n      col: this.col,\n    }\n    // nb. adding more props to token object will make V8 sad!\n\n    var size = text.length\n    this.index += size\n    this.line += lineBreaks\n    if (lineBreaks !== 0) {\n      this.col = size - nl + 1\n    } else {\n      this.col += size\n    }\n\n    // throw, if no rule with {error: true}\n    if (group.shouldThrow) {\n      var err = new Error(this.formatError(token, \"invalid syntax\"))\n      throw err;\n    }\n\n    if (group.pop) this.popState()\n    else if (group.push) this.pushState(group.push)\n    else if (group.next) this.setState(group.next)\n\n    return token\n  }\n\n  if (typeof Symbol !== 'undefined' && Symbol.iterator) {\n    var LexerIterator = function(lexer) {\n      this.lexer = lexer\n    }\n\n    LexerIterator.prototype.next = function() {\n      var token = this.lexer.next()\n      return {value: token, done: !token}\n    }\n\n    LexerIterator.prototype[Symbol.iterator] = function() {\n      return this\n    }\n\n    Lexer.prototype[Symbol.iterator] = function() {\n      return new LexerIterator(this)\n    }\n  }\n\n  Lexer.prototype.formatError = function(token, message) {\n    if (token == null) {\n      // An undefined token indicates EOF\n      var text = this.buffer.slice(this.index)\n      var token = {\n        text: text,\n        offset: this.index,\n        lineBreaks: text.indexOf('\\n') === -1 ? 0 : 1,\n        line: this.line,\n        col: this.col,\n      }\n    }\n    \n    var numLinesAround = 2\n    var firstDisplayedLine = Math.max(token.line - numLinesAround, 1)\n    var lastDisplayedLine = token.line + numLinesAround\n    var lastLineDigits = String(lastDisplayedLine).length\n    var displayedLines = lastNLines(\n        this.buffer, \n        (this.line - token.line) + numLinesAround + 1\n      )\n      .slice(0, 5)\n    var errorLines = []\n    errorLines.push(message + \" at line \" + token.line + \" col \" + token.col + \":\")\n    errorLines.push(\"\")\n    for (var i = 0; i < displayedLines.length; i++) {\n      var line = displayedLines[i]\n      var lineNo = firstDisplayedLine + i\n      errorLines.push(pad(String(lineNo), lastLineDigits) + \"  \" + line);\n      if (lineNo === token.line) {\n        errorLines.push(pad(\"\", lastLineDigits + token.col + 1) + \"^\")\n      }\n    }\n    return errorLines.join(\"\\n\")\n  }\n\n  Lexer.prototype.clone = function() {\n    return new Lexer(this.states, this.state)\n  }\n\n  Lexer.prototype.has = function(tokenType) {\n    return true\n  }\n\n\n  return {\n    compile: compile,\n    states: compileStates,\n    error: Object.freeze({error: true}),\n    fallback: Object.freeze({fallback: true}),\n    keywords: keywordTransform,\n  }\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/moo/moo.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/moo/moo.js":
/*!*************************************!*\
  !*** ../../node_modules/moo/moo.js ***!
  \*************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function(root, factory) {\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__)) /* global define */\n  } else {}\n}(this, function() {\n  'use strict';\n\n  var hasOwnProperty = Object.prototype.hasOwnProperty\n  var toString = Object.prototype.toString\n  var hasSticky = typeof new RegExp().sticky === 'boolean'\n\n  /***************************************************************************/\n\n  function isRegExp(o) { return o && toString.call(o) === '[object RegExp]' }\n  function isObject(o) { return o && typeof o === 'object' && !isRegExp(o) && !Array.isArray(o) }\n\n  function reEscape(s) {\n    return s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n  }\n  function reGroups(s) {\n    var re = new RegExp('|' + s)\n    return re.exec('').length - 1\n  }\n  function reCapture(s) {\n    return '(' + s + ')'\n  }\n  function reUnion(regexps) {\n    if (!regexps.length) return '(?!)'\n    var source =  regexps.map(function(s) {\n      return \"(?:\" + s + \")\"\n    }).join('|')\n    return \"(?:\" + source + \")\"\n  }\n\n  function regexpOrLiteral(obj) {\n    if (typeof obj === 'string') {\n      return '(?:' + reEscape(obj) + ')'\n\n    } else if (isRegExp(obj)) {\n      // TODO: consider /u support\n      if (obj.ignoreCase) throw new Error('RegExp /i flag not allowed')\n      if (obj.global) throw new Error('RegExp /g flag is implied')\n      if (obj.sticky) throw new Error('RegExp /y flag is implied')\n      if (obj.multiline) throw new Error('RegExp /m flag is implied')\n      return obj.source\n\n    } else {\n      throw new Error('Not a pattern: ' + obj)\n    }\n  }\n\n  function pad(s, length) {\n    if (s.length > length) {\n      return s\n    }\n    return Array(length - s.length + 1).join(\" \") + s\n  }\n\n  function lastNLines(string, numLines) {\n    var position = string.length\n    var lineBreaks = 0;\n    while (true) {\n      var idx = string.lastIndexOf(\"\\n\", position - 1)\n      if (idx === -1) {\n        break;\n      } else {\n        lineBreaks++\n      }\n      position = idx\n      if (lineBreaks === numLines) {\n        break;\n      }\n      if (position === 0) {\n        break;\n      }\n    }\n    var startPosition = \n      lineBreaks < numLines ?\n      0 : \n      position + 1\n    return string.substring(startPosition).split(\"\\n\")\n  }\n\n  function objectToRules(object) {\n    var keys = Object.getOwnPropertyNames(object)\n    var result = []\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var thing = object[key]\n      var rules = [].concat(thing)\n      if (key === 'include') {\n        for (var j = 0; j < rules.length; j++) {\n          result.push({include: rules[j]})\n        }\n        continue\n      }\n      var match = []\n      rules.forEach(function(rule) {\n        if (isObject(rule)) {\n          if (match.length) result.push(ruleOptions(key, match))\n          result.push(ruleOptions(key, rule))\n          match = []\n        } else {\n          match.push(rule)\n        }\n      })\n      if (match.length) result.push(ruleOptions(key, match))\n    }\n    return result\n  }\n\n  function arrayToRules(array) {\n    var result = []\n    for (var i = 0; i < array.length; i++) {\n      var obj = array[i]\n      if (obj.include) {\n        var include = [].concat(obj.include)\n        for (var j = 0; j < include.length; j++) {\n          result.push({include: include[j]})\n        }\n        continue\n      }\n      if (!obj.type) {\n        throw new Error('Rule has no type: ' + JSON.stringify(obj))\n      }\n      result.push(ruleOptions(obj.type, obj))\n    }\n    return result\n  }\n\n  function ruleOptions(type, obj) {\n    if (!isObject(obj)) {\n      obj = { match: obj }\n    }\n    if (obj.include) {\n      throw new Error('Matching rules cannot also include states')\n    }\n\n    // nb. error and fallback imply lineBreaks\n    var options = {\n      defaultType: type,\n      lineBreaks: !!obj.error || !!obj.fallback,\n      pop: false,\n      next: null,\n      push: null,\n      error: false,\n      fallback: false,\n      value: null,\n      type: null,\n      shouldThrow: false,\n    }\n\n    // Avoid Object.assign(), so we support IE9+\n    for (var key in obj) {\n      if (hasOwnProperty.call(obj, key)) {\n        options[key] = obj[key]\n      }\n    }\n\n    // type transform cannot be a string\n    if (typeof options.type === 'string' && type !== options.type) {\n      throw new Error(\"Type transform cannot be a string (type '\" + options.type + \"' for token '\" + type + \"')\")\n    }\n\n    // convert to array\n    var match = options.match\n    options.match = Array.isArray(match) ? match : match ? [match] : []\n    options.match.sort(function(a, b) {\n      return isRegExp(a) && isRegExp(b) ? 0\n           : isRegExp(b) ? -1 : isRegExp(a) ? +1 : b.length - a.length\n    })\n    return options\n  }\n\n  function toRules(spec) {\n    return Array.isArray(spec) ? arrayToRules(spec) : objectToRules(spec)\n  }\n\n  var defaultErrorRule = ruleOptions('error', {lineBreaks: true, shouldThrow: true})\n  function compileRules(rules, hasStates) {\n    var errorRule = null\n    var fast = Object.create(null)\n    var fastAllowed = true\n    var unicodeFlag = null\n    var groups = []\n    var parts = []\n\n    // If there is a fallback rule, then disable fast matching\n    for (var i = 0; i < rules.length; i++) {\n      if (rules[i].fallback) {\n        fastAllowed = false\n      }\n    }\n\n    for (var i = 0; i < rules.length; i++) {\n      var options = rules[i]\n\n      if (options.include) {\n        // all valid inclusions are removed by states() preprocessor\n        throw new Error('Inheritance is not allowed in stateless lexers')\n      }\n\n      if (options.error || options.fallback) {\n        // errorRule can only be set once\n        if (errorRule) {\n          if (!options.fallback === !errorRule.fallback) {\n            throw new Error(\"Multiple \" + (options.fallback ? \"fallback\" : \"error\") + \" rules not allowed (for token '\" + options.defaultType + \"')\")\n          } else {\n            throw new Error(\"fallback and error are mutually exclusive (for token '\" + options.defaultType + \"')\")\n          }\n        }\n        errorRule = options\n      }\n\n      var match = options.match.slice()\n      if (fastAllowed) {\n        while (match.length && typeof match[0] === 'string' && match[0].length === 1) {\n          var word = match.shift()\n          fast[word.charCodeAt(0)] = options\n        }\n      }\n\n      // Warn about inappropriate state-switching options\n      if (options.pop || options.push || options.next) {\n        if (!hasStates) {\n          throw new Error(\"State-switching options are not allowed in stateless lexers (for token '\" + options.defaultType + \"')\")\n        }\n        if (options.fallback) {\n          throw new Error(\"State-switching options are not allowed on fallback tokens (for token '\" + options.defaultType + \"')\")\n        }\n      }\n\n      // Only rules with a .match are included in the RegExp\n      if (match.length === 0) {\n        continue\n      }\n      fastAllowed = false\n\n      groups.push(options)\n\n      // Check unicode flag is used everywhere or nowhere\n      for (var j = 0; j < match.length; j++) {\n        var obj = match[j]\n        if (!isRegExp(obj)) {\n          continue\n        }\n\n        if (unicodeFlag === null) {\n          unicodeFlag = obj.unicode\n        } else if (unicodeFlag !== obj.unicode && options.fallback === false) {\n          throw new Error('If one rule is /u then all must be')\n        }\n      }\n\n      // convert to RegExp\n      var pat = reUnion(match.map(regexpOrLiteral))\n\n      // validate\n      var regexp = new RegExp(pat)\n      if (regexp.test(\"\")) {\n        throw new Error(\"RegExp matches empty string: \" + regexp)\n      }\n      var groupCount = reGroups(pat)\n      if (groupCount > 0) {\n        throw new Error(\"RegExp has capture groups: \" + regexp + \"\\nUse (?: … ) instead\")\n      }\n\n      // try and detect rules matching newlines\n      if (!options.lineBreaks && regexp.test('\\n')) {\n        throw new Error('Rule should declare lineBreaks: ' + regexp)\n      }\n\n      // store regex\n      parts.push(reCapture(pat))\n    }\n\n\n    // If there's no fallback rule, use the sticky flag so we only look for\n    // matches at the current index.\n    //\n    // If we don't support the sticky flag, then fake it using an irrefutable\n    // match (i.e. an empty pattern).\n    var fallbackRule = errorRule && errorRule.fallback\n    var flags = hasSticky && !fallbackRule ? 'ym' : 'gm'\n    var suffix = hasSticky || fallbackRule ? '' : '|'\n\n    if (unicodeFlag === true) flags += \"u\"\n    var combined = new RegExp(reUnion(parts) + suffix, flags)\n    return {regexp: combined, groups: groups, fast: fast, error: errorRule || defaultErrorRule}\n  }\n\n  function compile(rules) {\n    var result = compileRules(toRules(rules))\n    return new Lexer({start: result}, 'start')\n  }\n\n  function checkStateGroup(g, name, map) {\n    var state = g && (g.push || g.next)\n    if (state && !map[state]) {\n      throw new Error(\"Missing state '\" + state + \"' (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n    if (g && g.pop && +g.pop !== 1) {\n      throw new Error(\"pop must be 1 (in token '\" + g.defaultType + \"' of state '\" + name + \"')\")\n    }\n  }\n  function compileStates(states, start) {\n    var all = states.$all ? toRules(states.$all) : []\n    delete states.$all\n\n    var keys = Object.getOwnPropertyNames(states)\n    if (!start) start = keys[0]\n\n    var ruleMap = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      ruleMap[key] = toRules(states[key]).concat(all)\n    }\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      var rules = ruleMap[key]\n      var included = Object.create(null)\n      for (var j = 0; j < rules.length; j++) {\n        var rule = rules[j]\n        if (!rule.include) continue\n        var splice = [j, 1]\n        if (rule.include !== key && !included[rule.include]) {\n          included[rule.include] = true\n          var newRules = ruleMap[rule.include]\n          if (!newRules) {\n            throw new Error(\"Cannot include nonexistent state '\" + rule.include + \"' (in state '\" + key + \"')\")\n          }\n          for (var k = 0; k < newRules.length; k++) {\n            var newRule = newRules[k]\n            if (rules.indexOf(newRule) !== -1) continue\n            splice.push(newRule)\n          }\n        }\n        rules.splice.apply(rules, splice)\n        j--\n      }\n    }\n\n    var map = Object.create(null)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      map[key] = compileRules(ruleMap[key], true)\n    }\n\n    for (var i = 0; i < keys.length; i++) {\n      var name = keys[i]\n      var state = map[name]\n      var groups = state.groups\n      for (var j = 0; j < groups.length; j++) {\n        checkStateGroup(groups[j], name, map)\n      }\n      var fastKeys = Object.getOwnPropertyNames(state.fast)\n      for (var j = 0; j < fastKeys.length; j++) {\n        checkStateGroup(state.fast[fastKeys[j]], name, map)\n      }\n    }\n\n    return new Lexer(map, start)\n  }\n\n  function keywordTransform(map) {\n\n    // Use a JavaScript Map to map keywords to their corresponding token type\n    // unless Map is unsupported, then fall back to using an Object:\n    var isMap = typeof Map !== 'undefined'\n    var reverseMap = isMap ? new Map : Object.create(null)\n\n    var types = Object.getOwnPropertyNames(map)\n    for (var i = 0; i < types.length; i++) {\n      var tokenType = types[i]\n      var item = map[tokenType]\n      var keywordList = Array.isArray(item) ? item : [item]\n      keywordList.forEach(function(keyword) {\n        if (typeof keyword !== 'string') {\n          throw new Error(\"keyword must be string (in keyword '\" + tokenType + \"')\")\n        }\n        if (isMap) {\n          reverseMap.set(keyword, tokenType)\n        } else {\n          reverseMap[keyword] = tokenType\n        }\n      })\n    }\n    return function(k) {\n      return isMap ? reverseMap.get(k) : reverseMap[k]\n    }\n  }\n\n  /***************************************************************************/\n\n  var Lexer = function(states, state) {\n    this.startState = state\n    this.states = states\n    this.buffer = ''\n    this.stack = []\n    this.reset()\n  }\n\n  Lexer.prototype.reset = function(data, info) {\n    this.buffer = data || ''\n    this.index = 0\n    this.line = info ? info.line : 1\n    this.col = info ? info.col : 1\n    this.queuedToken = info ? info.queuedToken : null\n    this.queuedText = info ? info.queuedText: \"\";\n    this.queuedThrow = info ? info.queuedThrow : null\n    this.setState(info ? info.state : this.startState)\n    this.stack = info && info.stack ? info.stack.slice() : []\n    return this\n  }\n\n  Lexer.prototype.save = function() {\n    return {\n      line: this.line,\n      col: this.col,\n      state: this.state,\n      stack: this.stack.slice(),\n      queuedToken: this.queuedToken,\n      queuedText: this.queuedText,\n      queuedThrow: this.queuedThrow,\n    }\n  }\n\n  Lexer.prototype.setState = function(state) {\n    if (!state || this.state === state) return\n    this.state = state\n    var info = this.states[state]\n    this.groups = info.groups\n    this.error = info.error\n    this.re = info.regexp\n    this.fast = info.fast\n  }\n\n  Lexer.prototype.popState = function() {\n    this.setState(this.stack.pop())\n  }\n\n  Lexer.prototype.pushState = function(state) {\n    this.stack.push(this.state)\n    this.setState(state)\n  }\n\n  var eat = hasSticky ? function(re, buffer) { // assume re is /y\n    return re.exec(buffer)\n  } : function(re, buffer) { // assume re is /g\n    var match = re.exec(buffer)\n    // will always match, since we used the |(?:) trick\n    if (match[0].length === 0) {\n      return null\n    }\n    return match\n  }\n\n  Lexer.prototype._getGroup = function(match) {\n    var groupCount = this.groups.length\n    for (var i = 0; i < groupCount; i++) {\n      if (match[i + 1] !== undefined) {\n        return this.groups[i]\n      }\n    }\n    throw new Error('Cannot find token type for matched text')\n  }\n\n  function tokenToString() {\n    return this.value\n  }\n\n  Lexer.prototype.next = function() {\n    var index = this.index\n\n    // If a fallback token matched, we don't need to re-run the RegExp\n    if (this.queuedGroup) {\n      var token = this._token(this.queuedGroup, this.queuedText, index)\n      this.queuedGroup = null\n      this.queuedText = \"\"\n      return token\n    }\n\n    var buffer = this.buffer\n    if (index === buffer.length) {\n      return // EOF\n    }\n\n    // Fast matching for single characters\n    var group = this.fast[buffer.charCodeAt(index)]\n    if (group) {\n      return this._token(group, buffer.charAt(index), index)\n    }\n\n    // Execute RegExp\n    var re = this.re\n    re.lastIndex = index\n    var match = eat(re, buffer)\n\n    // Error tokens match the remaining buffer\n    var error = this.error\n    if (match == null) {\n      return this._token(error, buffer.slice(index, buffer.length), index)\n    }\n\n    var group = this._getGroup(match)\n    var text = match[0]\n\n    if (error.fallback && match.index !== index) {\n      this.queuedGroup = group\n      this.queuedText = text\n\n      // Fallback tokens contain the unmatched portion of the buffer\n      return this._token(error, buffer.slice(index, match.index), index)\n    }\n\n    return this._token(group, text, index)\n  }\n\n  Lexer.prototype._token = function(group, text, offset) {\n    // count line breaks\n    var lineBreaks = 0\n    if (group.lineBreaks) {\n      var matchNL = /\\n/g\n      var nl = 1\n      if (text === '\\n') {\n        lineBreaks = 1\n      } else {\n        while (matchNL.exec(text)) { lineBreaks++; nl = matchNL.lastIndex }\n      }\n    }\n\n    var token = {\n      type: (typeof group.type === 'function' && group.type(text)) || group.defaultType,\n      value: typeof group.value === 'function' ? group.value(text) : text,\n      text: text,\n      toString: tokenToString,\n      offset: offset,\n      lineBreaks: lineBreaks,\n      line: this.line,\n      col: this.col,\n    }\n    // nb. adding more props to token object will make V8 sad!\n\n    var size = text.length\n    this.index += size\n    this.line += lineBreaks\n    if (lineBreaks !== 0) {\n      this.col = size - nl + 1\n    } else {\n      this.col += size\n    }\n\n    // throw, if no rule with {error: true}\n    if (group.shouldThrow) {\n      var err = new Error(this.formatError(token, \"invalid syntax\"))\n      throw err;\n    }\n\n    if (group.pop) this.popState()\n    else if (group.push) this.pushState(group.push)\n    else if (group.next) this.setState(group.next)\n\n    return token\n  }\n\n  if (typeof Symbol !== 'undefined' && Symbol.iterator) {\n    var LexerIterator = function(lexer) {\n      this.lexer = lexer\n    }\n\n    LexerIterator.prototype.next = function() {\n      var token = this.lexer.next()\n      return {value: token, done: !token}\n    }\n\n    LexerIterator.prototype[Symbol.iterator] = function() {\n      return this\n    }\n\n    Lexer.prototype[Symbol.iterator] = function() {\n      return new LexerIterator(this)\n    }\n  }\n\n  Lexer.prototype.formatError = function(token, message) {\n    if (token == null) {\n      // An undefined token indicates EOF\n      var text = this.buffer.slice(this.index)\n      var token = {\n        text: text,\n        offset: this.index,\n        lineBreaks: text.indexOf('\\n') === -1 ? 0 : 1,\n        line: this.line,\n        col: this.col,\n      }\n    }\n    \n    var numLinesAround = 2\n    var firstDisplayedLine = Math.max(token.line - numLinesAround, 1)\n    var lastDisplayedLine = token.line + numLinesAround\n    var lastLineDigits = String(lastDisplayedLine).length\n    var displayedLines = lastNLines(\n        this.buffer, \n        (this.line - token.line) + numLinesAround + 1\n      )\n      .slice(0, 5)\n    var errorLines = []\n    errorLines.push(message + \" at line \" + token.line + \" col \" + token.col + \":\")\n    errorLines.push(\"\")\n    for (var i = 0; i < displayedLines.length; i++) {\n      var line = displayedLines[i]\n      var lineNo = firstDisplayedLine + i\n      errorLines.push(pad(String(lineNo), lastLineDigits) + \"  \" + line);\n      if (lineNo === token.line) {\n        errorLines.push(pad(\"\", lastLineDigits + token.col + 1) + \"^\")\n      }\n    }\n    return errorLines.join(\"\\n\")\n  }\n\n  Lexer.prototype.clone = function() {\n    return new Lexer(this.states, this.state)\n  }\n\n  Lexer.prototype.has = function(tokenType) {\n    return true\n  }\n\n\n  return {\n    compile: compile,\n    states: compileStates,\n    error: Object.freeze({error: true}),\n    fallback: Object.freeze({fallback: true}),\n    keywords: keywordTransform,\n  }\n\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/moo/moo.js\n");

/***/ })

};
;