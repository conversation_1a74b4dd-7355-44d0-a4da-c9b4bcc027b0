"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-core";
exports.ids = ["vendor-chunks/fumadocs-core"];
exports.modules = {

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __commonJS: () => (/* binding */ __commonJS),\n/* harmony export */   __toESM: () => (/* binding */ __toESM)\n/* harmony export */ });\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenNode: () => (/* binding */ flattenNode),\n/* harmony export */   remarkHeading: () => (/* binding */ remarkHeading)\n/* harmony export */ });\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! github-slugger */ \"(rsc)/../../node_modules/github-slugger/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/../../node_modules/unist-util-visit/lib/index.js\");\n// src/mdx-plugins/remark-heading.ts\n\n\n\n// src/mdx-plugins/remark-utils.ts\nfunction flattenNode(node) {\n  if (\"children\" in node)\n    return node.children.map((child) => flattenNode(child)).join(\"\");\n  if (\"value\" in node) return node.value;\n  return \"\";\n}\n\n// src/mdx-plugins/remark-heading.ts\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\nvar regex = /\\s*\\[#(?<slug>[^]+?)]\\s*$/;\nfunction remarkHeading({\n  slug: defaultSlug,\n  customId = true,\n  generateToc = true\n} = {}) {\n  return (root, file) => {\n    const toc = [];\n    slugger.reset();\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(root, \"heading\", (heading) => {\n      heading.data ||= {};\n      heading.data.hProperties ||= {};\n      let id = heading.data.hProperties.id;\n      const lastNode = heading.children.at(-1);\n      if (!id && lastNode?.type === \"text\" && customId) {\n        const match = regex.exec(lastNode.value);\n        if (match?.[1]) {\n          id = match[1];\n          lastNode.value = lastNode.value.slice(0, match.index);\n        }\n      }\n      let flattened = null;\n      if (!id) {\n        flattened ??= flattenNode(heading);\n        id = defaultSlug ? defaultSlug(root, heading, flattened) : slugger.slug(flattened);\n      }\n      heading.data.hProperties.id = id;\n      if (generateToc) {\n        toc.push({\n          title: flattened ?? flattenNode(heading),\n          url: `#${id}`,\n          depth: heading.depth\n        });\n      }\n      return \"skip\";\n    });\n    if (generateToc) file.data.toc = toc;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty),
/* harmony export */   HideIfEmptyProvider: () => (/* binding */ HideIfEmptyProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");

const HideIfEmpty = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HideIfEmpty() from the server but HideIfEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/hide-if-empty.js",
"HideIfEmpty",
);const HideIfEmptyProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HideIfEmptyProvider() from the server but HideIfEmptyProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/hide-if-empty.js",
"HideIfEmptyProvider",
);

/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/link.js":
/*!*****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/link.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js",
"default",
));


/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/server/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/server/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageTree: () => (/* binding */ page_tree_exports),\n/* harmony export */   createMetadataImage: () => (/* binding */ createMetadataImage),\n/* harmony export */   findNeighbour: () => (/* binding */ findNeighbour),\n/* harmony export */   flattenTree: () => (/* binding */ flattenTree),\n/* harmony export */   getGithubLastEdit: () => (/* binding */ getGithubLastEdit),\n/* harmony export */   getPageTreePeers: () => (/* binding */ getPageTreePeers),\n/* harmony export */   getPageTreeRoots: () => (/* binding */ getPageTreeRoots),\n/* harmony export */   getTableOfContents: () => (/* binding */ getTableOfContents),\n/* harmony export */   separatePageTree: () => (/* binding */ separatePageTree)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-Y2774T3B.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var remark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark */ \"(rsc)/../../node_modules/remark/index.js\");\n\n\n\n// src/server/get-toc.ts\n\nfunction getTableOfContents(content, remarkPlugins) {\n  if (remarkPlugins) {\n    return (0,remark__WEBPACK_IMPORTED_MODULE_2__.remark)().use(remarkPlugins).use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).process(content).then((result2) => {\n      if (\"toc\" in result2.data) return result2.data.toc;\n      return [];\n    });\n  }\n  const result = (0,remark__WEBPACK_IMPORTED_MODULE_2__.remark)().use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).processSync(content);\n  if (\"toc\" in result.data) return result.data.toc;\n  return [];\n}\n\n// src/utils/page-tree.tsx\nfunction flattenTree(tree) {\n  return tree.flatMap((node) => {\n    if (node.type === \"separator\") return [];\n    if (node.type === \"folder\") {\n      const child = flattenTree(node.children);\n      if (node.index) return [node.index, ...child];\n      return child;\n    }\n    return [node];\n  });\n}\nfunction findNeighbour(tree, url, options) {\n  const { separateRoot = true } = options ?? {};\n  const roots = separateRoot ? getPageTreeRoots(tree) : [tree];\n  for (const root of roots) {\n    const list = flattenTree(root.children);\n    for (let i = 0; i < list.length; i++) {\n      if (list[i].url === url) {\n        return {\n          next: list[i + 1],\n          previous: list[i - 1]\n        };\n      }\n    }\n  }\n  return {};\n}\nfunction getPageTreeRoots(pageTree) {\n  const result = pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    const roots = getPageTreeRoots(child);\n    if (child.root) {\n      roots.push(child);\n    }\n    return roots;\n  });\n  if (!(\"type\" in pageTree)) result.push(pageTree);\n  return result;\n}\nfunction separatePageTree(pageTree) {\n  return pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    return {\n      name: child.name,\n      url: child.index?.url,\n      children: child.children\n    };\n  });\n}\nfunction getPageTreePeers(tree, url) {\n  const parent = findParentFromTree(tree, url);\n  if (!parent) return [];\n  return parent.children.filter(\n    (item) => item.type === \"page\" && item.url !== url\n  );\n}\nfunction findParentFromTree(node, url) {\n  if (\"index\" in node && node.index?.url === url) {\n    return node;\n  }\n  for (const child of node.children) {\n    if (child.type === \"folder\") {\n      const parent = findParentFromTree(child, url);\n      if (parent) return parent;\n    }\n    if (child.type === \"page\" && child.url === url) {\n      return node;\n    }\n  }\n}\n\n// src/server/page-tree.ts\nvar page_tree_exports = {};\n\n// src/server/git-api.ts\nasync function getGithubLastEdit({\n  repo,\n  token,\n  owner,\n  path,\n  sha,\n  options = {},\n  params: customParams = {}\n}) {\n  const headers = new Headers(options.headers);\n  const params = new URLSearchParams();\n  params.set(\"path\", path);\n  params.set(\"page\", \"1\");\n  params.set(\"per_page\", \"1\");\n  if (sha) params.set(\"sha\", sha);\n  for (const [key, value] of Object.entries(customParams)) {\n    params.set(key, value);\n  }\n  if (token) {\n    headers.append(\"authorization\", token);\n  }\n  const res = await fetch(\n    `https://api.github.com/repos/${owner}/${repo}/commits?${params.toString()}`,\n    {\n      cache: \"force-cache\",\n      ...options,\n      headers\n    }\n  );\n  if (!res.ok)\n    throw new Error(\n      `Failed to fetch last edit time from Git ${await res.text()}`\n    );\n  const data = await res.json();\n  if (data.length === 0) return null;\n  return new Date(data[0].commit.committer.date);\n}\n\n// src/server/metadata.ts\nfunction createMetadataImage(options) {\n  const { filename = \"image.png\", imageRoute = \"/docs-og\" } = options;\n  function getImageMeta(slugs) {\n    return {\n      alt: \"Banner\",\n      url: `/${[...imageRoute.split(\"/\"), ...slugs, filename].filter((v) => v.length > 0).join(\"/\")}`,\n      width: 1200,\n      height: 630\n    };\n  }\n  return {\n    getImageMeta,\n    withImage(slugs, data) {\n      const imageData = getImageMeta(slugs);\n      return {\n        ...data,\n        openGraph: {\n          images: imageData,\n          ...data?.openGraph\n        },\n        twitter: {\n          images: imageData,\n          card: \"summary_large_image\",\n          ...data?.twitter\n        }\n      };\n    },\n    generateParams() {\n      return options.source.generateParams().map((params) => ({\n        ...params,\n        slug: [...params.slug, filename]\n      }));\n    },\n    createAPI(handler) {\n      return async (req, args) => {\n        const params = await args.params;\n        if (!params || !(\"slug\" in params) || params.slug === void 0)\n          throw new Error(`Invalid params: ${JSON.stringify(params)}`);\n        const lang = \"lang\" in params && typeof params.lang === \"string\" ? params.lang : void 0;\n        const input = {\n          slug: Array.isArray(params.slug) ? params.slug : [params.slug],\n          lang\n        };\n        const page = options.source.getPage(\n          input.slug.slice(0, -1),\n          //remove filename\n          lang\n        );\n        if (!page)\n          return new Response(null, {\n            status: 404\n          });\n        return handler(page, req, { params: input });\n      };\n    }\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/server/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/algolia-UCGCELZZ.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/algolia-UCGCELZZ.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.length > 0) {\n    const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n      requests: [\n        {\n          type: \"default\",\n          indexName,\n          query,\n          distinct: 5,\n          hitsPerPage: 10,\n          filters: tag ? `tag:${tag}` : void 0\n        }\n      ]\n    });\n    return groupResults(result.results[0].hits).filter(\n      (hit) => hit.type === \"page\"\n    );\n  }\n  return [];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/algolia-UCGCELZZ.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/breadcrumb.js":
/*!***********************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/breadcrumb.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBreadcrumbItems: () => (/* binding */ getBreadcrumbItems),\n/* harmony export */   getBreadcrumbItemsFromPath: () => (/* binding */ getBreadcrumbItemsFromPath),\n/* harmony export */   searchPath: () => (/* binding */ searchPath),\n/* harmony export */   useBreadcrumb: () => (/* binding */ useBreadcrumb)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/breadcrumb.tsx\n\nfunction useBreadcrumb(url, tree, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => getBreadcrumbItems(url, tree, options),\n    [tree, url, options]\n  );\n}\nfunction getBreadcrumbItems(url, tree, options = {}) {\n  return getBreadcrumbItemsFromPath(\n    tree,\n    searchPath(tree.children, url) ?? [],\n    options\n  );\n}\nfunction getBreadcrumbItemsFromPath(tree, path, options) {\n  const { includePage = true, includeSeparator = false, includeRoot } = options;\n  let items = [];\n  path.forEach((item, i) => {\n    if (item.type === \"separator\" && item.name && includeSeparator) {\n      items.push({\n        name: item.name\n      });\n    }\n    if (item.type === \"folder\") {\n      const next = path.at(i + 1);\n      if (next && item.index === next) return;\n      if (item.root) {\n        items = [];\n        return;\n      }\n      items.push({\n        name: item.name,\n        url: item.index?.url\n      });\n    }\n    if (item.type === \"page\" && includePage) {\n      items.push({\n        name: item.name,\n        url: item.url\n      });\n    }\n  });\n  if (includeRoot) {\n    items.unshift({\n      name: tree.name,\n      url: typeof includeRoot === \"object\" ? includeRoot.url : void 0\n    });\n  }\n  return items;\n}\nfunction searchPath(nodes, url) {\n  if (url.endsWith(\"/\")) url = url.slice(0, -1);\n  let separator;\n  for (const node of nodes) {\n    if (node.type === \"separator\") separator = node;\n    if (node.type === \"folder\") {\n      if (node.index?.url === url) {\n        const items2 = [];\n        if (separator) items2.push(separator);\n        items2.push(node, node.index);\n        return items2;\n      }\n      const items = searchPath(node.children, url);\n      if (items) {\n        items.unshift(node);\n        if (separator) items.unshift(separator);\n        return items;\n      }\n    }\n    if (node.type === \"page\" && node.url === url) {\n      const items = [];\n      if (separator) items.push(separator);\n      items.push(node);\n      return items;\n    }\n  }\n  return null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/breadcrumb.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link2)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/link.tsx\n\n\nvar Link2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n  ({\n    href = \"#\",\n    external = !(href.startsWith(\"/\") || href.startsWith(\"#\") || href.startsWith(\".\")),\n    prefetch,\n    ...props\n  }, ref) => {\n    if (external) {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"a\",\n        {\n          ref,\n          href,\n          rel: \"noreferrer noopener\",\n          target: \"_blank\",\n          ...props,\n          children: props.children\n        }\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link, { ref, href, prefetch, ...props });\n  }\n);\nLink2.displayName = \"Link\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay01U1UyTzVBUy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTZCOztBQUU3QjtBQUNtQztBQUNLO0FBQ3hDLFlBQVksaURBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDZCQUE2QixzREFBRztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLENBQUMsb0RBQUksSUFBSSwrQkFBK0I7QUFDdEU7QUFDQTtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTVTVTJPNUFTLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIExpbmtcbn0gZnJvbSBcIi4vY2h1bmstQkJQN01JTzQuanNcIjtcblxuLy8gc3JjL2xpbmsudHN4XG5pbXBvcnQgeyBmb3J3YXJkUmVmIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBMaW5rMiA9IGZvcndhcmRSZWYoXG4gICh7XG4gICAgaHJlZiA9IFwiI1wiLFxuICAgIGV4dGVybmFsID0gIShocmVmLnN0YXJ0c1dpdGgoXCIvXCIpIHx8IGhyZWYuc3RhcnRzV2l0aChcIiNcIikgfHwgaHJlZi5zdGFydHNXaXRoKFwiLlwiKSksXG4gICAgcHJlZmV0Y2gsXG4gICAgLi4ucHJvcHNcbiAgfSwgcmVmKSA9PiB7XG4gICAgaWYgKGV4dGVybmFsKSB7XG4gICAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgXCJhXCIsXG4gICAgICAgIHtcbiAgICAgICAgICByZWYsXG4gICAgICAgICAgaHJlZixcbiAgICAgICAgICByZWw6IFwibm9yZWZlcnJlciBub29wZW5lclwiLFxuICAgICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgICBjaGlsZHJlbjogcHJvcHMuY2hpbGRyZW5cbiAgICAgICAgfVxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goTGluaywgeyByZWYsIGhyZWYsIHByZWZldGNoLCAuLi5wcm9wcyB9KTtcbiAgfVxuKTtcbkxpbmsyLmRpc3BsYXlOYW1lID0gXCJMaW5rXCI7XG5cbmV4cG9ydCB7XG4gIExpbmsyIGFzIExpbmtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-62HKBTBF.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-62HKBTBF.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchAdvanced: () => (/* binding */ searchAdvanced),\n/* harmony export */   searchSimple: () => (/* binding */ searchSimple)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/../../node_modules/@orama/orama/dist/esm/index.js\");\n\n\n// src/search/orama/search/simple.ts\n\nasync function searchSimple(db, query, params = {}) {\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, {\n    term: query,\n    tolerance: 1,\n    ...params,\n    boost: {\n      title: 2,\n      ...\"boost\" in params ? params.boost : void 0\n    }\n  });\n  return result.hits.map((hit) => ({\n    type: \"page\",\n    content: hit.document.title,\n    id: hit.document.url,\n    url: hit.document.url\n  }));\n}\n\n// src/search/orama/search/advanced.ts\n\nasync function searchAdvanced(db, query, tag = [], extraParams = {}) {\n  if (typeof tag === \"string\") tag = [tag];\n  let params = {\n    ...extraParams,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tags: tag.length > 0 ? {\n        containsAll: tag\n      } : void 0,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 8,\n      ...extraParams.groupBy\n    }\n  };\n  if (query.length > 0) {\n    params = {\n      ...params,\n      term: query,\n      properties: [\"content\"]\n    };\n  }\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, params);\n  const list = [];\n  for (const item of result.groups ?? []) {\n    const pageId = item.values[0];\n    const page = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.getByID)(db, pageId);\n    if (!page) continue;\n    list.push({\n      id: pageId,\n      type: \"page\",\n      content: page.content,\n      url: page.url\n    });\n    for (const hit of item.result) {\n      if (hit.document.type === \"page\") continue;\n      list.push({\n        id: hit.document.id.toString(),\n        content: hit.document.content,\n        type: hit.document.type,\n        url: hit.document.url\n      });\n    }\n  }\n  return list;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* binding */ FrameworkProvider),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useParams: () => (/* binding */ useParams),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/framework/index.tsx\n\n\nvar notImplemented = () => {\n  throw new Error(\n    \"You need to wrap your application inside `FrameworkProvider`.\"\n  );\n};\nvar FrameworkContext = createContext(\"FrameworkContext\", {\n  useParams: notImplemented,\n  useRouter: notImplemented,\n  usePathname: notImplemented\n});\nfunction FrameworkProvider({\n  Link: Link2,\n  useRouter: useRouter2,\n  useParams: useParams2,\n  usePathname: usePathname2,\n  Image: Image2,\n  children\n}) {\n  const framework = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n    () => ({\n      usePathname: usePathname2,\n      useRouter: useRouter2,\n      Link: Link2,\n      Image: Image2,\n      useParams: useParams2\n    }),\n    [Link2, usePathname2, useRouter2, useParams2, Image2]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FrameworkContext.Provider, { value: framework, children });\n}\nfunction usePathname() {\n  return FrameworkContext.use().usePathname();\n}\nfunction useRouter() {\n  return FrameworkContext.use().useRouter();\n}\nfunction useParams() {\n  return FrameworkContext.use().useParams();\n}\nfunction Image(props) {\n  const { Image: Image2 } = FrameworkContext.use();\n  if (!Image2) {\n    const { src, alt, priority, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"img\",\n      {\n        alt,\n        src,\n        fetchPriority: priority ? \"high\" : \"auto\",\n        ...rest\n      }\n    );\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Image2, { ...props });\n}\nfunction Link(props) {\n  const { Link: Link2 } = FrameworkContext.use();\n  if (!Link2) {\n    const { href, prefetch: _, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"a\", { href, ...rest });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Link2, { ...props });\n}\nfunction createContext(name, v) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(v);\n  return {\n    Provider: (props) => {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value: props.value, children: props.children });\n    },\n    use: (errorMessage) => {\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (!value)\n        throw new Error(\n          errorMessage ?? `Provider of ${name} is required but missing.`\n        );\n      return value;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1CQlA3TUlPNC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTtBQUMwQjtBQUNjO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxvQkFBb0IsMENBQWE7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSx5QkFBeUIsc0RBQUcsOEJBQThCLDRCQUE0QjtBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxnQkFBZ0I7QUFDMUI7QUFDQSxZQUFZLDhCQUE4QjtBQUMxQywyQkFBMkIsc0RBQUc7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHNEQUFHLFdBQVcsVUFBVTtBQUNqRDtBQUNBO0FBQ0EsVUFBVSxjQUFjO0FBQ3hCO0FBQ0EsWUFBWSw2QkFBNkI7QUFDekMsMkJBQTJCLHNEQUFHLFFBQVEsZUFBZTtBQUNyRDtBQUNBLHlCQUF5QixzREFBRyxVQUFVLFVBQVU7QUFDaEQ7QUFDQTtBQUNBLGtCQUFrQixnREFBbUI7QUFDckM7QUFDQTtBQUNBLDZCQUE2QixzREFBRyxxQkFBcUIsOENBQThDO0FBQ25HLEtBQUs7QUFDTDtBQUNBLG9CQUFvQiw2Q0FBZ0I7QUFDcEM7QUFDQTtBQUNBLHlDQUF5QyxNQUFNO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBVUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstQkJQN01JTzQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2ZyYW1ld29yay9pbmRleC50c3hcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIG5vdEltcGxlbWVudGVkID0gKCkgPT4ge1xuICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgXCJZb3UgbmVlZCB0byB3cmFwIHlvdXIgYXBwbGljYXRpb24gaW5zaWRlIGBGcmFtZXdvcmtQcm92aWRlcmAuXCJcbiAgKTtcbn07XG52YXIgRnJhbWV3b3JrQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoXCJGcmFtZXdvcmtDb250ZXh0XCIsIHtcbiAgdXNlUGFyYW1zOiBub3RJbXBsZW1lbnRlZCxcbiAgdXNlUm91dGVyOiBub3RJbXBsZW1lbnRlZCxcbiAgdXNlUGF0aG5hbWU6IG5vdEltcGxlbWVudGVkXG59KTtcbmZ1bmN0aW9uIEZyYW1ld29ya1Byb3ZpZGVyKHtcbiAgTGluazogTGluazIsXG4gIHVzZVJvdXRlcjogdXNlUm91dGVyMixcbiAgdXNlUGFyYW1zOiB1c2VQYXJhbXMyLFxuICB1c2VQYXRobmFtZTogdXNlUGF0aG5hbWUyLFxuICBJbWFnZTogSW1hZ2UyLFxuICBjaGlsZHJlblxufSkge1xuICBjb25zdCBmcmFtZXdvcmsgPSBSZWFjdC51c2VNZW1vKFxuICAgICgpID0+ICh7XG4gICAgICB1c2VQYXRobmFtZTogdXNlUGF0aG5hbWUyLFxuICAgICAgdXNlUm91dGVyOiB1c2VSb3V0ZXIyLFxuICAgICAgTGluazogTGluazIsXG4gICAgICBJbWFnZTogSW1hZ2UyLFxuICAgICAgdXNlUGFyYW1zOiB1c2VQYXJhbXMyXG4gICAgfSksXG4gICAgW0xpbmsyLCB1c2VQYXRobmFtZTIsIHVzZVJvdXRlcjIsIHVzZVBhcmFtczIsIEltYWdlMl1cbiAgKTtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRnJhbWV3b3JrQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZnJhbWV3b3JrLCBjaGlsZHJlbiB9KTtcbn1cbmZ1bmN0aW9uIHVzZVBhdGhuYW1lKCkge1xuICByZXR1cm4gRnJhbWV3b3JrQ29udGV4dC51c2UoKS51c2VQYXRobmFtZSgpO1xufVxuZnVuY3Rpb24gdXNlUm91dGVyKCkge1xuICByZXR1cm4gRnJhbWV3b3JrQ29udGV4dC51c2UoKS51c2VSb3V0ZXIoKTtcbn1cbmZ1bmN0aW9uIHVzZVBhcmFtcygpIHtcbiAgcmV0dXJuIEZyYW1ld29ya0NvbnRleHQudXNlKCkudXNlUGFyYW1zKCk7XG59XG5mdW5jdGlvbiBJbWFnZShwcm9wcykge1xuICBjb25zdCB7IEltYWdlOiBJbWFnZTIgfSA9IEZyYW1ld29ya0NvbnRleHQudXNlKCk7XG4gIGlmICghSW1hZ2UyKSB7XG4gICAgY29uc3QgeyBzcmMsIGFsdCwgcHJpb3JpdHksIC4uLnJlc3QgfSA9IHByb3BzO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgXCJpbWdcIixcbiAgICAgIHtcbiAgICAgICAgYWx0LFxuICAgICAgICBzcmMsXG4gICAgICAgIGZldGNoUHJpb3JpdHk6IHByaW9yaXR5ID8gXCJoaWdoXCIgOiBcImF1dG9cIixcbiAgICAgICAgLi4ucmVzdFxuICAgICAgfVxuICAgICk7XG4gIH1cbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goSW1hZ2UyLCB7IC4uLnByb3BzIH0pO1xufVxuZnVuY3Rpb24gTGluayhwcm9wcykge1xuICBjb25zdCB7IExpbms6IExpbmsyIH0gPSBGcmFtZXdvcmtDb250ZXh0LnVzZSgpO1xuICBpZiAoIUxpbmsyKSB7XG4gICAgY29uc3QgeyBocmVmLCBwcmVmZXRjaDogXywgLi4ucmVzdCB9ID0gcHJvcHM7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXCJhXCIsIHsgaHJlZiwgLi4ucmVzdCB9KTtcbiAgfVxuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChMaW5rMiwgeyAuLi5wcm9wcyB9KTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUNvbnRleHQobmFtZSwgdikge1xuICBjb25zdCBDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2KTtcbiAgcmV0dXJuIHtcbiAgICBQcm92aWRlcjogKHByb3BzKSA9PiB7XG4gICAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBwcm9wcy52YWx1ZSwgY2hpbGRyZW46IHByb3BzLmNoaWxkcmVuIH0pO1xuICAgIH0sXG4gICAgdXNlOiAoZXJyb3JNZXNzYWdlKSA9PiB7XG4gICAgICBjb25zdCB2YWx1ZSA9IFJlYWN0LnVzZUNvbnRleHQoQ29udGV4dCk7XG4gICAgICBpZiAoIXZhbHVlKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgZXJyb3JNZXNzYWdlID8/IGBQcm92aWRlciBvZiAke25hbWV9IGlzIHJlcXVpcmVkIGJ1dCBtaXNzaW5nLmBcbiAgICAgICAgKTtcbiAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gIH07XG59XG5cbmV4cG9ydCB7XG4gIEZyYW1ld29ya1Byb3ZpZGVyLFxuICB1c2VQYXRobmFtZSxcbiAgdXNlUm91dGVyLFxuICB1c2VQYXJhbXMsXG4gIEltYWdlLFxuICBMaW5rLFxuICBjcmVhdGVDb250ZXh0XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* binding */ useOnChange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-on-change.ts\n\nfunction isDifferent(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferent(v, b[i]));\n  }\n  return a !== b;\n}\nfunction useOnChange(value, onChange, isUpdated = isDifferent) {\n  const [prev, setPrev] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n  if (isUpdated(prev, value)) {\n    onChange(value, prev);\n    setPrev(value);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1FTVdHVFhTVy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ2lDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLCtDQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstRU1XR1RYU1cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL3VzZS1vbi1jaGFuZ2UudHNcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBpc0RpZmZlcmVudChhLCBiKSB7XG4gIGlmIChBcnJheS5pc0FycmF5KGEpICYmIEFycmF5LmlzQXJyYXkoYikpIHtcbiAgICByZXR1cm4gYi5sZW5ndGggIT09IGEubGVuZ3RoIHx8IGEuc29tZSgodiwgaSkgPT4gaXNEaWZmZXJlbnQodiwgYltpXSkpO1xuICB9XG4gIHJldHVybiBhICE9PSBiO1xufVxuZnVuY3Rpb24gdXNlT25DaGFuZ2UodmFsdWUsIG9uQ2hhbmdlLCBpc1VwZGF0ZWQgPSBpc0RpZmZlcmVudCkge1xuICBjb25zdCBbcHJldiwgc2V0UHJldl0gPSB1c2VTdGF0ZSh2YWx1ZSk7XG4gIGlmIChpc1VwZGF0ZWQocHJldiwgdmFsdWUpKSB7XG4gICAgb25DaGFuZ2UodmFsdWUsIHByZXYpO1xuICAgIHNldFByZXYodmFsdWUpO1xuICB9XG59XG5cbmV4cG9ydCB7XG4gIHVzZU9uQ2hhbmdlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-media-query.ts\n\nfunction useMediaQuery(query, disabled = false) {\n  const [isMatch, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) return;\n    const mediaQueryList = window.matchMedia(query);\n    const handleChange = () => {\n      setMatch(mediaQueryList.matches);\n    };\n    handleChange();\n    mediaQueryList.addEventListener(\"change\", handleChange);\n    return () => {\n      mediaQueryList.removeEventListener(\"change\", handleChange);\n    };\n  }, [disabled, query]);\n  return isMatch;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1FUDVMSEdEWi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQzRDO0FBQzVDO0FBQ0EsOEJBQThCLCtDQUFRO0FBQ3RDLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUVQNUxIR0RaLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy91c2UtbWVkaWEtcXVlcnkudHNcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZU1lZGlhUXVlcnkocXVlcnksIGRpc2FibGVkID0gZmFsc2UpIHtcbiAgY29uc3QgW2lzTWF0Y2gsIHNldE1hdGNoXSA9IHVzZVN0YXRlKG51bGwpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChkaXNhYmxlZCkgcmV0dXJuO1xuICAgIGNvbnN0IG1lZGlhUXVlcnlMaXN0ID0gd2luZG93Lm1hdGNoTWVkaWEocXVlcnkpO1xuICAgIGNvbnN0IGhhbmRsZUNoYW5nZSA9ICgpID0+IHtcbiAgICAgIHNldE1hdGNoKG1lZGlhUXVlcnlMaXN0Lm1hdGNoZXMpO1xuICAgIH07XG4gICAgaGFuZGxlQ2hhbmdlKCk7XG4gICAgbWVkaWFRdWVyeUxpc3QuYWRkRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBoYW5kbGVDaGFuZ2UpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBtZWRpYVF1ZXJ5TGlzdC5yZW1vdmVFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIGhhbmRsZUNoYW5nZSk7XG4gICAgfTtcbiAgfSwgW2Rpc2FibGVkLCBxdWVyeV0pO1xuICByZXR1cm4gaXNNYXRjaDtcbn1cblxuZXhwb3J0IHtcbiAgdXNlTWVkaWFRdWVyeVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __commonJS: () => (/* binding */ __commonJS),\n/* harmony export */   __toESM: () => (/* binding */ __toESM)\n/* harmony export */ });\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1LQU9FTUNUSS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1LQU9FTUNUSS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvcmVtb3ZlLXVuZGVmaW5lZC50c1xuZnVuY3Rpb24gcmVtb3ZlVW5kZWZpbmVkKHZhbHVlLCBkZWVwID0gZmFsc2UpIHtcbiAgY29uc3Qgb2JqID0gdmFsdWU7XG4gIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKG9iaikpIHtcbiAgICBpZiAob2JqW2tleV0gPT09IHZvaWQgMCkgZGVsZXRlIG9ialtrZXldO1xuICAgIGlmIChkZWVwICYmIHR5cGVvZiBvYmpba2V5XSA9PT0gXCJvYmplY3RcIiAmJiBvYmpba2V5XSAhPT0gbnVsbCkge1xuICAgICAgcmVtb3ZlVW5kZWZpbmVkKG9ialtrZXldLCBkZWVwKTtcbiAgICB9IGVsc2UgaWYgKGRlZXAgJiYgQXJyYXkuaXNBcnJheShvYmpba2V5XSkpIHtcbiAgICAgIG9ialtrZXldLmZvckVhY2goKHYpID0+IHJlbW92ZVVuZGVmaW5lZCh2LCBkZWVwKSk7XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHtcbiAgcmVtb3ZlVW5kZWZpbmVkXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchDocs: () => (/* binding */ fetchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, { api = \"/api/search\", locale, tag }) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", Array.isArray(tag) ? tag.join(\",\") : tag);\n  const key = `${api}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9mZXRjaC1JVFBIQlBCRS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2Qjs7QUFFN0I7QUFDQTtBQUNBLGtDQUFrQyxrQ0FBa0M7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsSUFBSSxHQUFHLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZldGNoLUlUUEhCUEJFLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4vY2h1bmstSlNCUkRKQkUuanNcIjtcblxuLy8gc3JjL3NlYXJjaC9jbGllbnQvZmV0Y2gudHNcbnZhciBjYWNoZSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG5hc3luYyBmdW5jdGlvbiBmZXRjaERvY3MocXVlcnksIHsgYXBpID0gXCIvYXBpL3NlYXJjaFwiLCBsb2NhbGUsIHRhZyB9KSB7XG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgcGFyYW1zLnNldChcInF1ZXJ5XCIsIHF1ZXJ5KTtcbiAgaWYgKGxvY2FsZSkgcGFyYW1zLnNldChcImxvY2FsZVwiLCBsb2NhbGUpO1xuICBpZiAodGFnKSBwYXJhbXMuc2V0KFwidGFnXCIsIEFycmF5LmlzQXJyYXkodGFnKSA/IHRhZy5qb2luKFwiLFwiKSA6IHRhZyk7XG4gIGNvbnN0IGtleSA9IGAke2FwaX0/JHtwYXJhbXN9YDtcbiAgY29uc3QgY2FjaGVkID0gY2FjaGUuZ2V0KGtleSk7XG4gIGlmIChjYWNoZWQpIHJldHVybiBjYWNoZWQ7XG4gIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGtleSk7XG4gIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoYXdhaXQgcmVzLnRleHQoKSk7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlcy5qc29uKCk7XG4gIGNhY2hlLnNldChrZXksIHJlc3VsdCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5leHBvcnQge1xuICBmZXRjaERvY3Ncbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/framework/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/framework/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider),\n/* harmony export */   Image: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Image),\n/* harmony export */   Link: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link),\n/* harmony export */   createContext: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.createContext),\n/* harmony export */   useParams: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useParams),\n/* harmony export */   usePathname: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.usePathname),\n/* harmony export */   useRouter: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* __next_internal_client_entry_do_not_use__ FrameworkProvider,Image,Link,createContext,useParams,usePathname,useRouter auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9mcmFtZXdvcmsvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O2dJQVM4QjtBQUNBO0FBUzVCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7XG4gIEZyYW1ld29ya1Byb3ZpZGVyLFxuICBJbWFnZSxcbiAgTGluayxcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlUGFyYW1zLFxuICB1c2VQYXRobmFtZSxcbiAgdXNlUm91dGVyXG59IGZyb20gXCIuLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuaW1wb3J0IFwiLi4vY2h1bmstSlNCUkRKQkUuanNcIjtcbmV4cG9ydCB7XG4gIEZyYW1ld29ya1Byb3ZpZGVyLFxuICBJbWFnZSxcbiAgTGluayxcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlUGFyYW1zLFxuICB1c2VQYXRobmFtZSxcbiAgdXNlUm91dGVyXG59O1xuIl0sIm5hbWVzIjpbIkZyYW1ld29ya1Byb3ZpZGVyIiwiSW1hZ2UiLCJMaW5rIiwiY3JlYXRlQ29udGV4dCIsInVzZVBhcmFtcyIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/framework/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/framework/next.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/framework/next.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextProvider: () => (/* binding */ NextProvider)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ NextProvider auto */ \n\n// src/framework/next.tsx\n\n\n\n\nfunction NextProvider({ children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider, {\n        usePathname: next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        useRouter: next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        useParams: next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        Link: next_link__WEBPACK_IMPORTED_MODULE_3__,\n        Image: next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        children\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9mcmFtZXdvcmsvbmV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O2tFQUc4QjtBQUNBO0FBRTlCLHlCQUF5QjtBQUMyQztBQUN2QztBQUNFO0FBQ1M7QUFDeEMsU0FBU08sYUFBYSxFQUFFQyxRQUFRLEVBQUU7SUFDaEMsT0FBTyxhQUFhLEdBQUdGLHNEQUFHQSxDQUN4Qk4saUVBQWlCQSxFQUNqQjtRQUNFRSxXQUFXQSwwREFBQUE7UUFDWEMsU0FBU0Esd0RBQUFBO1FBQ1RGLFNBQVNBLHdEQUFBQTtRQUNURyxJQUFJQSx3Q0FBQUE7UUFDSkMsS0FBS0Esb0RBQUFBO1FBQ0xHO0lBQ0Y7QUFFSjtBQUdFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9uZXh0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHtcbiAgRnJhbWV3b3JrUHJvdmlkZXJcbn0gZnJvbSBcIi4uL2NodW5rLUJCUDdNSU80LmpzXCI7XG5pbXBvcnQgXCIuLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuXG4vLyBzcmMvZnJhbWV3b3JrL25leHQudHN4XG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVBhdGhuYW1lLCB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZnVuY3Rpb24gTmV4dFByb3ZpZGVyKHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICBGcmFtZXdvcmtQcm92aWRlcixcbiAgICB7XG4gICAgICB1c2VQYXRobmFtZSxcbiAgICAgIHVzZVJvdXRlcixcbiAgICAgIHVzZVBhcmFtcyxcbiAgICAgIExpbmssXG4gICAgICBJbWFnZSxcbiAgICAgIGNoaWxkcmVuXG4gICAgfVxuICApO1xufVxuZXhwb3J0IHtcbiAgTmV4dFByb3ZpZGVyXG59O1xuIl0sIm5hbWVzIjpbIkZyYW1ld29ya1Byb3ZpZGVyIiwidXNlUGFyYW1zIiwidXNlUGF0aG5hbWUiLCJ1c2VSb3V0ZXIiLCJMaW5rIiwiSW1hZ2UiLCJqc3giLCJOZXh0UHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/framework/next.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty),\n/* harmony export */   HideIfEmptyProvider: () => (/* binding */ HideIfEmptyProvider)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ HideIfEmpty,HideIfEmptyProvider auto */ \n// src/hide-if-empty.tsx\n\n\nvar Context = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    nonce: void 0\n});\nfunction HideIfEmptyProvider({ nonce, children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Context.Provider, {\n        value: (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n            \"HideIfEmptyProvider.useMemo\": ()=>({\n                    nonce\n                })\n        }[\"HideIfEmptyProvider.useMemo\"], [\n            nonce\n        ]),\n        children\n    });\n}\nfunction getElement(id) {\n    return document.querySelector(`[data-fd-if-empty=\"${id}\"]`);\n}\nfunction isEmpty(node) {\n    for(let i = 0; i < node.childNodes.length; i++){\n        const child = node.childNodes.item(i);\n        if (child.nodeType === Node.TEXT_NODE || child.nodeType === Node.ELEMENT_NODE && window.getComputedStyle(child).display !== \"none\") {\n            return false;\n        }\n    }\n    return true;\n}\nfunction HideIfEmpty({ as: Comp, ...props }) {\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const { nonce } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(Context);\n    const [empty, setEmpty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"HideIfEmpty.useState\": ()=>{\n            const element =  false ? 0 : null;\n            if (element) return isEmpty(element);\n        }\n    }[\"HideIfEmpty.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HideIfEmpty.useEffect\": ()=>{\n            const handleResize = {\n                \"HideIfEmpty.useEffect.handleResize\": ()=>{\n                    const element = getElement(id);\n                    if (element) setEmpty(isEmpty(element));\n                }\n            }[\"HideIfEmpty.useEffect.handleResize\"];\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"HideIfEmpty.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n            })[\"HideIfEmpty.useEffect\"];\n        }\n    }[\"HideIfEmpty.useEffect\"], [\n        id\n    ]);\n    const init = (id2)=>{\n        const element = getElement(id2);\n        if (element) element.hidden = isEmpty(element);\n        const script = document.currentScript;\n        if (script) script.parentNode?.removeChild(script);\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n                ...props,\n                \"data-fd-if-empty\": id,\n                hidden: empty ?? false\n            }),\n            empty === void 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"script\", {\n                nonce,\n                dangerouslySetInnerHTML: {\n                    __html: `{${getElement};${isEmpty};(${init})(\"${id}\")}`\n                }\n            })\n        ]\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/hide-if-empty.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/link.js":
/*!*****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/link.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__.Link)\n/* harmony export */ });\n/* harmony import */ var _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5SU2O5AS.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\");\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9saW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7NkRBRzZCO0FBQ0E7QUFDQTtBQUczQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9saW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHtcbiAgTGlua1xufSBmcm9tIFwiLi9jaHVuay01U1UyTzVBUy5qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuZXhwb3J0IHtcbiAgTGluayBhcyBkZWZhdWx0XG59O1xuIl0sIm5hbWVzIjpbIkxpbmsiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/link.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/mixedbread-2MQ3PSN7.js":
/*!********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/mixedbread-2MQ3PSN7.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   search: () => (/* binding */ search)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! github-slugger */ \"(ssr)/../../node_modules/github-slugger/index.js\");\n\n\n// ../../node_modules/.pnpm/remove-markdown@0.6.2/node_modules/remove-markdown/index.js\nvar require_remove_markdown = (0,_chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__.__commonJS)({\n  \"../../node_modules/.pnpm/remove-markdown@0.6.2/node_modules/remove-markdown/index.js\"(exports, module) {\n    \"use strict\";\n    module.exports = function(md, options) {\n      options = options || {};\n      options.listUnicodeChar = options.hasOwnProperty(\"listUnicodeChar\") ? options.listUnicodeChar : false;\n      options.stripListLeaders = options.hasOwnProperty(\"stripListLeaders\") ? options.stripListLeaders : true;\n      options.gfm = options.hasOwnProperty(\"gfm\") ? options.gfm : true;\n      options.useImgAltText = options.hasOwnProperty(\"useImgAltText\") ? options.useImgAltText : true;\n      options.abbr = options.hasOwnProperty(\"abbr\") ? options.abbr : false;\n      options.replaceLinksWithURL = options.hasOwnProperty(\"replaceLinksWithURL\") ? options.replaceLinksWithURL : false;\n      options.htmlTagsToSkip = options.hasOwnProperty(\"htmlTagsToSkip\") ? options.htmlTagsToSkip : [];\n      options.throwError = options.hasOwnProperty(\"throwError\") ? options.throwError : false;\n      var output = md || \"\";\n      output = output.replace(/^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/gm, \"\");\n      try {\n        if (options.stripListLeaders) {\n          if (options.listUnicodeChar)\n            output = output.replace(/^([\\s\\t]*)([\\*\\-\\+]|\\d+\\.)\\s+/gm, options.listUnicodeChar + \" $1\");\n          else\n            output = output.replace(/^([\\s\\t]*)([\\*\\-\\+]|\\d+\\.)\\s+/gm, \"$1\");\n        }\n        if (options.gfm) {\n          output = output.replace(/\\n={2,}/g, \"\\n\").replace(/~{3}.*\\n/g, \"\").replace(/~~/g, \"\").replace(/```(?:.*)\\n([\\s\\S]*?)```/g, (_, code) => code.trim());\n        }\n        if (options.abbr) {\n          output = output.replace(/\\*\\[.*\\]:.*\\n/, \"\");\n        }\n        let htmlReplaceRegex = /<[^>]*>/g;\n        if (options.htmlTagsToSkip && options.htmlTagsToSkip.length > 0) {\n          const joinedHtmlTagsToSkip = options.htmlTagsToSkip.join(\"|\");\n          htmlReplaceRegex = new RegExp(\n            `<(?!/?(${joinedHtmlTagsToSkip})(?=>|s[^>]*>))[^>]*>`,\n            \"g\"\n          );\n        }\n        output = output.replace(htmlReplaceRegex, \"\").replace(/^[=\\-]{2,}\\s*$/g, \"\").replace(/\\[\\^.+?\\](\\: .*?$)?/g, \"\").replace(/\\s{0,2}\\[.*?\\]: .*?$/g, \"\").replace(/\\!\\[(.*?)\\][\\[\\(].*?[\\]\\)]/g, options.useImgAltText ? \"$1\" : \"\").replace(/\\[([\\s\\S]*?)\\]\\s*[\\(\\[].*?[\\)\\]]/g, options.replaceLinksWithURL ? \"$2\" : \"$1\").replace(/^(\\n)?\\s{0,3}>\\s?/gm, \"$1\").replace(/^\\s{1,2}\\[(.*?)\\]: (\\S+)( \".*?\")?\\s*$/g, \"\").replace(/^(\\n)?\\s{0,}#{1,6}\\s*( (.+))? +#+$|^(\\n)?\\s{0,}#{1,6}\\s*( (.+))?$/gm, \"$1$3$4$6\").replace(/([\\*]+)(\\S)(.*?\\S)??\\1/g, \"$2$3\").replace(/(^|\\W)([_]+)(\\S)(.*?\\S)??\\2($|\\W)/g, \"$1$3$4$5\").replace(/(`{3,})(.*?)\\1/gm, \"$2\").replace(/`(.+?)`/g, \"$1\").replace(/~(.*?)~/g, \"$1\");\n      } catch (e) {\n        if (options.throwError) throw e;\n        console.error(\"remove-markdown encountered error: %s\", e);\n        return md;\n      }\n      return output;\n    };\n  }\n});\n\n// src/search/client/mixedbread.ts\nvar import_remove_markdown = (0,_chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__.__toESM)(require_remove_markdown(), 1);\n\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\nfunction extractHeadingTitle(text) {\n  const trimmedText = text.trim();\n  if (!trimmedText.startsWith(\"#\")) {\n    return \"\";\n  }\n  const lines = trimmedText.split(\"\\n\");\n  const firstLine = lines[0]?.trim();\n  if (firstLine) {\n    const plainText = (0, import_remove_markdown.default)(firstLine, {\n      useImgAltText: false\n    });\n    return plainText;\n  }\n  return \"\";\n}\nasync function search(query, options) {\n  const { client, vectorStoreId, tag } = options;\n  if (!query.trim()) {\n    return [];\n  }\n  const res = await client.vectorStores.search({\n    query,\n    vector_store_identifiers: [vectorStoreId],\n    top_k: 10,\n    filters: {\n      key: \"generated_metadata.tag\",\n      operator: \"eq\",\n      value: tag\n    },\n    search_options: {\n      return_metadata: true\n    }\n  });\n  const results = res.data.flatMap((item) => {\n    const metadata = item.generated_metadata;\n    const url = metadata.url || \"#\";\n    const title = metadata.title || \"Untitled\";\n    const chunkResults = [\n      {\n        id: `${item.file_id}-${item.chunk_index}-page`,\n        type: \"page\",\n        content: title,\n        url\n      }\n    ];\n    const headingTitle = item.type === \"text\" ? extractHeadingTitle(item.text) : \"\";\n    if (headingTitle) {\n      slugger.reset();\n      chunkResults.push({\n        id: `${item.file_id}-${item.chunk_index}-heading`,\n        type: \"heading\",\n        content: headingTitle,\n        url: `${url}#${slugger.slug(headingTitle)}`\n      });\n    }\n    return chunkResults;\n  });\n  return results;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9taXhlZGJyZWFkLTJNUTNQU043LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUc2Qjs7QUFFN0I7QUFDQSw4QkFBOEIsOERBQVU7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsSUFBSSxhQUFhLEdBQUcsYUFBYSxHQUFHLGNBQWMsR0FBRztBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLEdBQUcscUJBQXFCLEVBQUU7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixxQkFBcUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFLEdBQUcsNERBQTRELElBQUkseU1BQXlNLElBQUksNEJBQTRCLElBQUksdURBQXVELEdBQUcsRUFBRSxJQUFJLDBCQUEwQixHQUFHLEVBQUUsSUFBSSw4SUFBOEksR0FBRztBQUMxbUIsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBLDZCQUE2QiwyREFBTztBQUNDO0FBQ3JDLGtCQUFrQixzREFBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSw2QkFBNkI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxhQUFhLEdBQUcsaUJBQWlCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsYUFBYSxHQUFHLGlCQUFpQjtBQUNoRDtBQUNBO0FBQ0EsZ0JBQWdCLElBQUksR0FBRywyQkFBMkI7QUFDbEQsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUdFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L21peGVkYnJlYWQtMk1RM1BTTjcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgX19jb21tb25KUyxcbiAgX190b0VTTVxufSBmcm9tIFwiLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuXG4vLyAuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVtb3ZlLW1hcmtkb3duQDAuNi4yL25vZGVfbW9kdWxlcy9yZW1vdmUtbWFya2Rvd24vaW5kZXguanNcbnZhciByZXF1aXJlX3JlbW92ZV9tYXJrZG93biA9IF9fY29tbW9uSlMoe1xuICBcIi4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZW1vdmUtbWFya2Rvd25AMC42LjIvbm9kZV9tb2R1bGVzL3JlbW92ZS1tYXJrZG93bi9pbmRleC5qc1wiKGV4cG9ydHMsIG1vZHVsZSkge1xuICAgIFwidXNlIHN0cmljdFwiO1xuICAgIG1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24obWQsIG9wdGlvbnMpIHtcbiAgICAgIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICAgICAgb3B0aW9ucy5saXN0VW5pY29kZUNoYXIgPSBvcHRpb25zLmhhc093blByb3BlcnR5KFwibGlzdFVuaWNvZGVDaGFyXCIpID8gb3B0aW9ucy5saXN0VW5pY29kZUNoYXIgOiBmYWxzZTtcbiAgICAgIG9wdGlvbnMuc3RyaXBMaXN0TGVhZGVycyA9IG9wdGlvbnMuaGFzT3duUHJvcGVydHkoXCJzdHJpcExpc3RMZWFkZXJzXCIpID8gb3B0aW9ucy5zdHJpcExpc3RMZWFkZXJzIDogdHJ1ZTtcbiAgICAgIG9wdGlvbnMuZ2ZtID0gb3B0aW9ucy5oYXNPd25Qcm9wZXJ0eShcImdmbVwiKSA/IG9wdGlvbnMuZ2ZtIDogdHJ1ZTtcbiAgICAgIG9wdGlvbnMudXNlSW1nQWx0VGV4dCA9IG9wdGlvbnMuaGFzT3duUHJvcGVydHkoXCJ1c2VJbWdBbHRUZXh0XCIpID8gb3B0aW9ucy51c2VJbWdBbHRUZXh0IDogdHJ1ZTtcbiAgICAgIG9wdGlvbnMuYWJiciA9IG9wdGlvbnMuaGFzT3duUHJvcGVydHkoXCJhYmJyXCIpID8gb3B0aW9ucy5hYmJyIDogZmFsc2U7XG4gICAgICBvcHRpb25zLnJlcGxhY2VMaW5rc1dpdGhVUkwgPSBvcHRpb25zLmhhc093blByb3BlcnR5KFwicmVwbGFjZUxpbmtzV2l0aFVSTFwiKSA/IG9wdGlvbnMucmVwbGFjZUxpbmtzV2l0aFVSTCA6IGZhbHNlO1xuICAgICAgb3B0aW9ucy5odG1sVGFnc1RvU2tpcCA9IG9wdGlvbnMuaGFzT3duUHJvcGVydHkoXCJodG1sVGFnc1RvU2tpcFwiKSA/IG9wdGlvbnMuaHRtbFRhZ3NUb1NraXAgOiBbXTtcbiAgICAgIG9wdGlvbnMudGhyb3dFcnJvciA9IG9wdGlvbnMuaGFzT3duUHJvcGVydHkoXCJ0aHJvd0Vycm9yXCIpID8gb3B0aW9ucy50aHJvd0Vycm9yIDogZmFsc2U7XG4gICAgICB2YXIgb3V0cHV0ID0gbWQgfHwgXCJcIjtcbiAgICAgIG91dHB1dCA9IG91dHB1dC5yZXBsYWNlKC9eIHswLDN9KCg/Oi1bXFx0IF0qKXszLH18KD86X1sgXFx0XSopezMsfXwoPzpcXCpbIFxcdF0qKXszLH0pKD86XFxuK3wkKS9nbSwgXCJcIik7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAob3B0aW9ucy5zdHJpcExpc3RMZWFkZXJzKSB7XG4gICAgICAgICAgaWYgKG9wdGlvbnMubGlzdFVuaWNvZGVDaGFyKVxuICAgICAgICAgICAgb3V0cHV0ID0gb3V0cHV0LnJlcGxhY2UoL14oW1xcc1xcdF0qKShbXFwqXFwtXFwrXXxcXGQrXFwuKVxccysvZ20sIG9wdGlvbnMubGlzdFVuaWNvZGVDaGFyICsgXCIgJDFcIik7XG4gICAgICAgICAgZWxzZVxuICAgICAgICAgICAgb3V0cHV0ID0gb3V0cHV0LnJlcGxhY2UoL14oW1xcc1xcdF0qKShbXFwqXFwtXFwrXXxcXGQrXFwuKVxccysvZ20sIFwiJDFcIik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMuZ2ZtKSB7XG4gICAgICAgICAgb3V0cHV0ID0gb3V0cHV0LnJlcGxhY2UoL1xcbj17Mix9L2csIFwiXFxuXCIpLnJlcGxhY2UoL357M30uKlxcbi9nLCBcIlwiKS5yZXBsYWNlKC9+fi9nLCBcIlwiKS5yZXBsYWNlKC9gYGAoPzouKilcXG4oW1xcc1xcU10qPylgYGAvZywgKF8sIGNvZGUpID0+IGNvZGUudHJpbSgpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5hYmJyKSB7XG4gICAgICAgICAgb3V0cHV0ID0gb3V0cHV0LnJlcGxhY2UoL1xcKlxcWy4qXFxdOi4qXFxuLywgXCJcIik7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGh0bWxSZXBsYWNlUmVnZXggPSAvPFtePl0qPi9nO1xuICAgICAgICBpZiAob3B0aW9ucy5odG1sVGFnc1RvU2tpcCAmJiBvcHRpb25zLmh0bWxUYWdzVG9Ta2lwLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCBqb2luZWRIdG1sVGFnc1RvU2tpcCA9IG9wdGlvbnMuaHRtbFRhZ3NUb1NraXAuam9pbihcInxcIik7XG4gICAgICAgICAgaHRtbFJlcGxhY2VSZWdleCA9IG5ldyBSZWdFeHAoXG4gICAgICAgICAgICBgPCg/IS8/KCR7am9pbmVkSHRtbFRhZ3NUb1NraXB9KSg/PT58c1tePl0qPikpW14+XSo+YCxcbiAgICAgICAgICAgIFwiZ1wiXG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgICBvdXRwdXQgPSBvdXRwdXQucmVwbGFjZShodG1sUmVwbGFjZVJlZ2V4LCBcIlwiKS5yZXBsYWNlKC9eWz1cXC1dezIsfVxccyokL2csIFwiXCIpLnJlcGxhY2UoL1xcW1xcXi4rP1xcXShcXDogLio/JCk/L2csIFwiXCIpLnJlcGxhY2UoL1xcc3swLDJ9XFxbLio/XFxdOiAuKj8kL2csIFwiXCIpLnJlcGxhY2UoL1xcIVxcWyguKj8pXFxdW1xcW1xcKF0uKj9bXFxdXFwpXS9nLCBvcHRpb25zLnVzZUltZ0FsdFRleHQgPyBcIiQxXCIgOiBcIlwiKS5yZXBsYWNlKC9cXFsoW1xcc1xcU10qPylcXF1cXHMqW1xcKFxcW10uKj9bXFwpXFxdXS9nLCBvcHRpb25zLnJlcGxhY2VMaW5rc1dpdGhVUkwgPyBcIiQyXCIgOiBcIiQxXCIpLnJlcGxhY2UoL14oXFxuKT9cXHN7MCwzfT5cXHM/L2dtLCBcIiQxXCIpLnJlcGxhY2UoL15cXHN7MSwyfVxcWyguKj8pXFxdOiAoXFxTKykoIFwiLio/XCIpP1xccyokL2csIFwiXCIpLnJlcGxhY2UoL14oXFxuKT9cXHN7MCx9I3sxLDZ9XFxzKiggKC4rKSk/ICsjKyR8XihcXG4pP1xcc3swLH0jezEsNn1cXHMqKCAoLispKT8kL2dtLCBcIiQxJDMkNCQ2XCIpLnJlcGxhY2UoLyhbXFwqXSspKFxcUykoLio/XFxTKT8/XFwxL2csIFwiJDIkM1wiKS5yZXBsYWNlKC8oXnxcXFcpKFtfXSspKFxcUykoLio/XFxTKT8/XFwyKCR8XFxXKS9nLCBcIiQxJDMkNCQ1XCIpLnJlcGxhY2UoLyhgezMsfSkoLio/KVxcMS9nbSwgXCIkMlwiKS5yZXBsYWNlKC9gKC4rPylgL2csIFwiJDFcIikucmVwbGFjZSgvfiguKj8pfi9nLCBcIiQxXCIpO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBpZiAob3B0aW9ucy50aHJvd0Vycm9yKSB0aHJvdyBlO1xuICAgICAgICBjb25zb2xlLmVycm9yKFwicmVtb3ZlLW1hcmtkb3duIGVuY291bnRlcmVkIGVycm9yOiAlc1wiLCBlKTtcbiAgICAgICAgcmV0dXJuIG1kO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG91dHB1dDtcbiAgICB9O1xuICB9XG59KTtcblxuLy8gc3JjL3NlYXJjaC9jbGllbnQvbWl4ZWRicmVhZC50c1xudmFyIGltcG9ydF9yZW1vdmVfbWFya2Rvd24gPSBfX3RvRVNNKHJlcXVpcmVfcmVtb3ZlX21hcmtkb3duKCksIDEpO1xuaW1wb3J0IFNsdWdnZXIgZnJvbSBcImdpdGh1Yi1zbHVnZ2VyXCI7XG52YXIgc2x1Z2dlciA9IG5ldyBTbHVnZ2VyKCk7XG5mdW5jdGlvbiBleHRyYWN0SGVhZGluZ1RpdGxlKHRleHQpIHtcbiAgY29uc3QgdHJpbW1lZFRleHQgPSB0ZXh0LnRyaW0oKTtcbiAgaWYgKCF0cmltbWVkVGV4dC5zdGFydHNXaXRoKFwiI1wiKSkge1xuICAgIHJldHVybiBcIlwiO1xuICB9XG4gIGNvbnN0IGxpbmVzID0gdHJpbW1lZFRleHQuc3BsaXQoXCJcXG5cIik7XG4gIGNvbnN0IGZpcnN0TGluZSA9IGxpbmVzWzBdPy50cmltKCk7XG4gIGlmIChmaXJzdExpbmUpIHtcbiAgICBjb25zdCBwbGFpblRleHQgPSAoMCwgaW1wb3J0X3JlbW92ZV9tYXJrZG93bi5kZWZhdWx0KShmaXJzdExpbmUsIHtcbiAgICAgIHVzZUltZ0FsdFRleHQ6IGZhbHNlXG4gICAgfSk7XG4gICAgcmV0dXJuIHBsYWluVGV4dDtcbiAgfVxuICByZXR1cm4gXCJcIjtcbn1cbmFzeW5jIGZ1bmN0aW9uIHNlYXJjaChxdWVyeSwgb3B0aW9ucykge1xuICBjb25zdCB7IGNsaWVudCwgdmVjdG9yU3RvcmVJZCwgdGFnIH0gPSBvcHRpb25zO1xuICBpZiAoIXF1ZXJ5LnRyaW0oKSkge1xuICAgIHJldHVybiBbXTtcbiAgfVxuICBjb25zdCByZXMgPSBhd2FpdCBjbGllbnQudmVjdG9yU3RvcmVzLnNlYXJjaCh7XG4gICAgcXVlcnksXG4gICAgdmVjdG9yX3N0b3JlX2lkZW50aWZpZXJzOiBbdmVjdG9yU3RvcmVJZF0sXG4gICAgdG9wX2s6IDEwLFxuICAgIGZpbHRlcnM6IHtcbiAgICAgIGtleTogXCJnZW5lcmF0ZWRfbWV0YWRhdGEudGFnXCIsXG4gICAgICBvcGVyYXRvcjogXCJlcVwiLFxuICAgICAgdmFsdWU6IHRhZ1xuICAgIH0sXG4gICAgc2VhcmNoX29wdGlvbnM6IHtcbiAgICAgIHJldHVybl9tZXRhZGF0YTogdHJ1ZVxuICAgIH1cbiAgfSk7XG4gIGNvbnN0IHJlc3VsdHMgPSByZXMuZGF0YS5mbGF0TWFwKChpdGVtKSA9PiB7XG4gICAgY29uc3QgbWV0YWRhdGEgPSBpdGVtLmdlbmVyYXRlZF9tZXRhZGF0YTtcbiAgICBjb25zdCB1cmwgPSBtZXRhZGF0YS51cmwgfHwgXCIjXCI7XG4gICAgY29uc3QgdGl0bGUgPSBtZXRhZGF0YS50aXRsZSB8fCBcIlVudGl0bGVkXCI7XG4gICAgY29uc3QgY2h1bmtSZXN1bHRzID0gW1xuICAgICAge1xuICAgICAgICBpZDogYCR7aXRlbS5maWxlX2lkfS0ke2l0ZW0uY2h1bmtfaW5kZXh9LXBhZ2VgLFxuICAgICAgICB0eXBlOiBcInBhZ2VcIixcbiAgICAgICAgY29udGVudDogdGl0bGUsXG4gICAgICAgIHVybFxuICAgICAgfVxuICAgIF07XG4gICAgY29uc3QgaGVhZGluZ1RpdGxlID0gaXRlbS50eXBlID09PSBcInRleHRcIiA/IGV4dHJhY3RIZWFkaW5nVGl0bGUoaXRlbS50ZXh0KSA6IFwiXCI7XG4gICAgaWYgKGhlYWRpbmdUaXRsZSkge1xuICAgICAgc2x1Z2dlci5yZXNldCgpO1xuICAgICAgY2h1bmtSZXN1bHRzLnB1c2goe1xuICAgICAgICBpZDogYCR7aXRlbS5maWxlX2lkfS0ke2l0ZW0uY2h1bmtfaW5kZXh9LWhlYWRpbmdgLFxuICAgICAgICB0eXBlOiBcImhlYWRpbmdcIixcbiAgICAgICAgY29udGVudDogaGVhZGluZ1RpdGxlLFxuICAgICAgICB1cmw6IGAke3VybH0jJHtzbHVnZ2VyLnNsdWcoaGVhZGluZ1RpdGxlKX1gXG4gICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGNodW5rUmVzdWx0cztcbiAgfSk7XG4gIHJldHVybiByZXN1bHRzO1xufVxuZXhwb3J0IHtcbiAgc2VhcmNoXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/mixedbread-2MQ3PSN7.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/search/client.js":
/*!**************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/search/client.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocsSearch: () => (/* binding */ useDocsSearch)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n// src/search/client.ts\n\n\n// src/utils/use-debounce.ts\n\nfunction useDebounce(value, delayMs = 1e3) {\n  const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(value);\n  const timer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(void 0);\n  if (delayMs === 0) return value;\n  if (value !== debouncedValue && timer.current?.value !== value) {\n    if (timer.current) clearTimeout(timer.current.handler);\n    const handler = window.setTimeout(() => {\n      setDebouncedValue(value);\n    }, delayMs);\n    timer.current = { value, handler };\n  }\n  return debouncedValue;\n}\n\n// src/search/client.ts\nfunction isDifferentDeep(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferentDeep(v, b[i]));\n  }\n  if (typeof a === \"object\" && a && typeof b === \"object\" && b) {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    return aKeys.length !== bKeys.length || aKeys.some(\n      (key) => isDifferentDeep(a[key], b[key])\n    );\n  }\n  return a !== b;\n}\nfunction useDocsSearch(clientOptions, _locale, _tag, _delayMs = 100, _allowEmpty = false, _key) {\n  const {\n    delayMs = _delayMs ?? 100,\n    allowEmpty = _allowEmpty ?? false,\n    ...client\n  } = clientOptions;\n  client.tag ??= _tag;\n  client.locale ??= _locale;\n  const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n  const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"empty\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n  const debouncedValue = useDebounce(search, delayMs);\n  const onStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(void 0);\n  (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(\n    [client, debouncedValue],\n    () => {\n      if (onStart.current) {\n        onStart.current();\n        onStart.current = void 0;\n      }\n      setIsLoading(true);\n      let interrupt = false;\n      onStart.current = () => {\n        interrupt = true;\n      };\n      async function run() {\n        if (debouncedValue.length === 0 && !allowEmpty) return \"empty\";\n        if (client.type === \"fetch\") {\n          const { fetchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../fetch-ITPHBPBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/fetch-ITPHBPBE.js\"));\n          return fetchDocs(debouncedValue, client);\n        }\n        if (client.type === \"algolia\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../algolia-UCGCELZZ.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/algolia-UCGCELZZ.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"orama-cloud\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../orama-cloud-6T5Z4MZY.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/orama-cloud-6T5Z4MZY.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"static\") {\n          const { search: search2 } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/fumadocs-core\"), __webpack_require__.e(\"vendor-chunks/@orama\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../static-7YX4RCT6.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/static-7YX4RCT6.js\"));\n          return search2(debouncedValue, client);\n        }\n        if (client.type === \"mixedbread\") {\n          const { search: search2 } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/fumadocs-core\"), __webpack_require__.e(\"vendor-chunks/github-slugger\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../mixedbread-2MQ3PSN7.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/mixedbread-2MQ3PSN7.js\"));\n          return search2(debouncedValue, client);\n        }\n        throw new Error(\"unknown search client\");\n      }\n      void run().then((res) => {\n        if (interrupt) return;\n        setError(void 0);\n        setResults(res);\n      }).catch((err) => {\n        setError(err);\n      }).finally(() => {\n        setIsLoading(false);\n      });\n    },\n    isDifferentDeep\n  );\n  return { search, setSearch, query: { isLoading, data: results, error } };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/search/client.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/static-7YX4RCT6.js":
/*!****************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/static-7YX4RCT6.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   search: () => (/* binding */ search)\n/* harmony export */ });\n/* harmony import */ var _chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-62HKBTBF.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\");\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/../../node_modules/@orama/orama/dist/esm/index.js\");\n\n\n\n\n// src/search/client/static.ts\n\nvar cache = /* @__PURE__ */ new Map();\nasync function loadDB({\n  from = \"/api/search\",\n  initOrama = (locale) => (0,_orama_orama__WEBPACK_IMPORTED_MODULE_3__.create)({ schema: { _: \"string\" }, language: locale })\n}) {\n  const cacheKey = from;\n  const cached = cache.get(cacheKey);\n  if (cached) return cached;\n  async function init() {\n    const res = await fetch(from);\n    if (!res.ok)\n      throw new Error(\n        `failed to fetch exported search indexes from ${from}, make sure the search database is exported and available for client.`\n      );\n    const data = await res.json();\n    const dbs = /* @__PURE__ */ new Map();\n    if (data.type === \"i18n\") {\n      await Promise.all(\n        Object.entries(data.data).map(async ([k, v]) => {\n          const db2 = await initOrama(k);\n          (0,_orama_orama__WEBPACK_IMPORTED_MODULE_3__.load)(db2, v);\n          dbs.set(k, {\n            type: v.type,\n            db: db2\n          });\n        })\n      );\n      return dbs;\n    }\n    const db = await initOrama();\n    (0,_orama_orama__WEBPACK_IMPORTED_MODULE_3__.load)(db, data);\n    dbs.set(\"\", {\n      type: data.type,\n      db\n    });\n    return dbs;\n  }\n  const result = init();\n  cache.set(cacheKey, result);\n  return result;\n}\nasync function search(query, options) {\n  const { tag, locale } = options;\n  const db = (await loadDB(options)).get(locale ?? \"\");\n  if (!db) return [];\n  if (db.type === \"simple\")\n    return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchSimple)(db, query);\n  return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchAdvanced)(db.db, query, tag);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/static-7YX4RCT6.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/toc.js":
/*!****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/toc.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnchorProvider: () => (/* binding */ AnchorProvider),\n/* harmony export */   ScrollProvider: () => (/* binding */ ScrollProvider),\n/* harmony export */   TOCItem: () => (/* binding */ TOCItem),\n/* harmony export */   useActiveAnchor: () => (/* binding */ useActiveAnchor),\n/* harmony export */   useActiveAnchors: () => (/* binding */ useActiveAnchors)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-EMWGTXSW.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! scroll-into-view-if-needed */ \"(ssr)/../../node_modules/scroll-into-view-if-needed/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ AnchorProvider,ScrollProvider,TOCItem,useActiveAnchor,useActiveAnchors auto */ \n\n// src/toc.tsx\n\n\n// src/utils/merge-refs.ts\nfunction mergeRefs(...refs) {\n    return (value)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === \"function\") {\n                ref(value);\n            } else if (ref !== null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n// src/utils/use-anchor-observer.ts\n\nfunction useAnchorObserver(watch, single) {\n    const [activeAnchor, setActiveAnchor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useAnchorObserver.useEffect\": ()=>{\n            let visible = [];\n            const observer = new IntersectionObserver({\n                \"useAnchorObserver.useEffect\": (entries)=>{\n                    for (const entry of entries){\n                        if (entry.isIntersecting && !visible.includes(entry.target.id)) {\n                            visible = [\n                                ...visible,\n                                entry.target.id\n                            ];\n                        } else if (!entry.isIntersecting && visible.includes(entry.target.id)) {\n                            visible = visible.filter({\n                                \"useAnchorObserver.useEffect\": (v)=>v !== entry.target.id\n                            }[\"useAnchorObserver.useEffect\"]);\n                        }\n                    }\n                    if (visible.length > 0) setActiveAnchor(visible);\n                }\n            }[\"useAnchorObserver.useEffect\"], {\n                rootMargin: single ? \"-80px 0% -70% 0%\" : `-20px 0% -40% 0%`,\n                threshold: 1\n            });\n            function onScroll() {\n                const element = document.scrollingElement;\n                if (!element) return;\n                const top = element.scrollTop;\n                if (top <= 0 && single) setActiveAnchor(watch.slice(0, 1));\n                else if (top + element.clientHeight >= element.scrollHeight - 6) {\n                    setActiveAnchor({\n                        \"useAnchorObserver.useEffect.onScroll\": (active)=>{\n                            return active.length > 0 && !single ? watch.slice(watch.indexOf(active[0])) : watch.slice(-1);\n                        }\n                    }[\"useAnchorObserver.useEffect.onScroll\"]);\n                }\n            }\n            for (const heading of watch){\n                const element = document.getElementById(heading);\n                if (element) observer.observe(element);\n            }\n            onScroll();\n            window.addEventListener(\"scroll\", onScroll);\n            return ({\n                \"useAnchorObserver.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", onScroll);\n                    observer.disconnect();\n                }\n            })[\"useAnchorObserver.useEffect\"];\n        }\n    }[\"useAnchorObserver.useEffect\"], [\n        single,\n        watch\n    ]);\n    return single ? activeAnchor.slice(0, 1) : activeAnchor;\n}\n// src/toc.tsx\n\nvar ActiveAnchorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)([]);\nvar ScrollContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    current: null\n});\nfunction useActiveAnchor() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ActiveAnchorContext).at(-1);\n}\nfunction useActiveAnchors() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ActiveAnchorContext);\n}\nfunction ScrollProvider({ containerRef, children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ScrollContext.Provider, {\n        value: containerRef,\n        children\n    });\n}\nfunction AnchorProvider({ toc, single = true, children }) {\n    const headings = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"AnchorProvider.useMemo[headings]\": ()=>{\n            return toc.map({\n                \"AnchorProvider.useMemo[headings]\": (item)=>item.url.split(\"#\")[1]\n            }[\"AnchorProvider.useMemo[headings]\"]);\n        }\n    }[\"AnchorProvider.useMemo[headings]\"], [\n        toc\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ActiveAnchorContext.Provider, {\n        value: useAnchorObserver(headings, single),\n        children\n    });\n}\nvar TOCItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ onActiveChange, ...props }, ref)=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ScrollContext);\n    const anchors = useActiveAnchors();\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mergedRef = mergeRefs(anchorRef, ref);\n    const isActive = anchors.includes(props.href.slice(1));\n    (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(isActive, {\n        \"TOCItem.useOnChange\": (v)=>{\n            const element = anchorRef.current;\n            if (!element) return;\n            if (v && containerRef.current) {\n                (0,scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(element, {\n                    behavior: \"smooth\",\n                    block: \"center\",\n                    inline: \"center\",\n                    scrollMode: \"always\",\n                    boundary: containerRef.current\n                });\n            }\n            onActiveChange?.(v);\n        }\n    }[\"TOCItem.useOnChange\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"a\", {\n        ref: mergedRef,\n        \"data-active\": isActive,\n        ...props,\n        children: props.children\n    });\n});\nTOCItem.displayName = \"TOCItem\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/toc.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/utils/use-effect-event.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/utils/use-effect-event.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ useEffectEvent auto */ \n// src/utils/use-effect-event.ts\n\nfunction useEffectEvent(callback) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(callback);\n    ref.current = callback;\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useEffectEvent.useCallback\": (...params)=>ref.current(...params)\n    }[\"useEffectEvent.useCallback\"], []);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztvRUFDOEI7QUFFOUIsZ0NBQWdDO0FBQ1k7QUFDNUMsU0FBU0UsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxNQUFNSCw2Q0FBTUEsQ0FBQ0U7SUFDbkJDLElBQUlDLE9BQU8sR0FBR0Y7SUFDZCxPQUFPSCxrREFBV0E7c0NBQUMsQ0FBQyxHQUFHTSxTQUFXRixJQUFJQyxPQUFPLElBQUlDO3FDQUFTLEVBQUU7QUFDOUQ7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IFwiLi4vY2h1bmstSlNCUkRKQkUuanNcIjtcblxuLy8gc3JjL3V0aWxzL3VzZS1lZmZlY3QtZXZlbnQudHNcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZihjYWxsYmFjayk7XG4gIHJlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIHJldHVybiB1c2VDYWxsYmFjaygoLi4ucGFyYW1zKSA9PiByZWYuY3VycmVudCguLi5wYXJhbXMpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VFZmZlY3RFdmVudFxufTtcbiJdLCJuYW1lcyI6WyJ1c2VDYWxsYmFjayIsInVzZVJlZiIsInVzZUVmZmVjdEV2ZW50IiwiY2FsbGJhY2siLCJyZWYiLCJjdXJyZW50IiwicGFyYW1zIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/utils/use-effect-event.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/utils/use-media-query.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/utils/use-media-query.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* reexport safe */ _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__.useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EP5LHGDZ.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtbWVkaWEtcXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRThCO0FBQ0E7QUFHNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvdXRpbHMvdXNlLW1lZGlhLXF1ZXJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIHVzZU1lZGlhUXVlcnlcbn0gZnJvbSBcIi4uL2NodW5rLUVQNUxIR0RaLmpzXCI7XG5pbXBvcnQgXCIuLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuZXhwb3J0IHtcbiAgdXNlTWVkaWFRdWVyeVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/utils/use-media-query.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/utils/use-on-change.js":
/*!********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/utils/use-on-change.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* reexport safe */ _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2Utb24tY2hhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUU4QjtBQUNBO0FBRzVCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1vbi1jaGFuZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn0gZnJvbSBcIi4uL2NodW5rLUVNV0dUWFNXLmpzXCI7XG5pbXBvcnQgXCIuLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/utils/use-on-change.js\n");

/***/ })

};
;