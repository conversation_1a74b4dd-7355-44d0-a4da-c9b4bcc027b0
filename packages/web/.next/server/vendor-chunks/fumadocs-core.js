"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-core";
exports.ids = ["vendor-chunks/fumadocs-core"];
exports.modules = {

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __commonJS: () => (/* binding */ __commonJS),\n/* harmony export */   __toESM: () => (/* binding */ __toESM)\n/* harmony export */ });\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenNode: () => (/* binding */ flattenNode),\n/* harmony export */   remarkHeading: () => (/* binding */ remarkHeading)\n/* harmony export */ });\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! github-slugger */ \"(rsc)/../../node_modules/github-slugger/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/../../node_modules/unist-util-visit/lib/index.js\");\n// src/mdx-plugins/remark-heading.ts\n\n\n\n// src/mdx-plugins/remark-utils.ts\nfunction flattenNode(node) {\n  if (\"children\" in node)\n    return node.children.map((child) => flattenNode(child)).join(\"\");\n  if (\"value\" in node) return node.value;\n  return \"\";\n}\n\n// src/mdx-plugins/remark-heading.ts\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\nvar regex = /\\s*\\[#(?<slug>[^]+?)]\\s*$/;\nfunction remarkHeading({\n  slug: defaultSlug,\n  customId = true,\n  generateToc = true\n} = {}) {\n  return (root, file) => {\n    const toc = [];\n    slugger.reset();\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(root, \"heading\", (heading) => {\n      heading.data ||= {};\n      heading.data.hProperties ||= {};\n      let id = heading.data.hProperties.id;\n      const lastNode = heading.children.at(-1);\n      if (!id && lastNode?.type === \"text\" && customId) {\n        const match = regex.exec(lastNode.value);\n        if (match?.[1]) {\n          id = match[1];\n          lastNode.value = lastNode.value.slice(0, match.index);\n        }\n      }\n      let flattened = null;\n      if (!id) {\n        flattened ??= flattenNode(heading);\n        id = defaultSlug ? defaultSlug(root, heading, flattened) : slugger.slug(flattened);\n      }\n      heading.data.hProperties.id = id;\n      if (generateToc) {\n        toc.push({\n          title: flattened ?? flattenNode(heading),\n          url: `#${id}`,\n          depth: heading.depth\n        });\n      }\n      return \"skip\";\n    });\n    if (generateToc) file.data.toc = toc;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1ZMjc3NFQzQi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDcUM7QUFDSTs7QUFFekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0Isc0RBQU87QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBLElBQUksdURBQUs7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLEdBQUc7QUFDdEI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBS0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstWTI3NzRUM0IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL21keC1wbHVnaW5zL3JlbWFyay1oZWFkaW5nLnRzXG5pbXBvcnQgU2x1Z2dlciBmcm9tIFwiZ2l0aHViLXNsdWdnZXJcIjtcbmltcG9ydCB7IHZpc2l0IH0gZnJvbSBcInVuaXN0LXV0aWwtdmlzaXRcIjtcblxuLy8gc3JjL21keC1wbHVnaW5zL3JlbWFyay11dGlscy50c1xuZnVuY3Rpb24gZmxhdHRlbk5vZGUobm9kZSkge1xuICBpZiAoXCJjaGlsZHJlblwiIGluIG5vZGUpXG4gICAgcmV0dXJuIG5vZGUuY2hpbGRyZW4ubWFwKChjaGlsZCkgPT4gZmxhdHRlbk5vZGUoY2hpbGQpKS5qb2luKFwiXCIpO1xuICBpZiAoXCJ2YWx1ZVwiIGluIG5vZGUpIHJldHVybiBub2RlLnZhbHVlO1xuICByZXR1cm4gXCJcIjtcbn1cblxuLy8gc3JjL21keC1wbHVnaW5zL3JlbWFyay1oZWFkaW5nLnRzXG52YXIgc2x1Z2dlciA9IG5ldyBTbHVnZ2VyKCk7XG52YXIgcmVnZXggPSAvXFxzKlxcWyMoPzxzbHVnPlteXSs/KV1cXHMqJC87XG5mdW5jdGlvbiByZW1hcmtIZWFkaW5nKHtcbiAgc2x1ZzogZGVmYXVsdFNsdWcsXG4gIGN1c3RvbUlkID0gdHJ1ZSxcbiAgZ2VuZXJhdGVUb2MgPSB0cnVlXG59ID0ge30pIHtcbiAgcmV0dXJuIChyb290LCBmaWxlKSA9PiB7XG4gICAgY29uc3QgdG9jID0gW107XG4gICAgc2x1Z2dlci5yZXNldCgpO1xuICAgIHZpc2l0KHJvb3QsIFwiaGVhZGluZ1wiLCAoaGVhZGluZykgPT4ge1xuICAgICAgaGVhZGluZy5kYXRhIHx8PSB7fTtcbiAgICAgIGhlYWRpbmcuZGF0YS5oUHJvcGVydGllcyB8fD0ge307XG4gICAgICBsZXQgaWQgPSBoZWFkaW5nLmRhdGEuaFByb3BlcnRpZXMuaWQ7XG4gICAgICBjb25zdCBsYXN0Tm9kZSA9IGhlYWRpbmcuY2hpbGRyZW4uYXQoLTEpO1xuICAgICAgaWYgKCFpZCAmJiBsYXN0Tm9kZT8udHlwZSA9PT0gXCJ0ZXh0XCIgJiYgY3VzdG9tSWQpIHtcbiAgICAgICAgY29uc3QgbWF0Y2ggPSByZWdleC5leGVjKGxhc3ROb2RlLnZhbHVlKTtcbiAgICAgICAgaWYgKG1hdGNoPy5bMV0pIHtcbiAgICAgICAgICBpZCA9IG1hdGNoWzFdO1xuICAgICAgICAgIGxhc3ROb2RlLnZhbHVlID0gbGFzdE5vZGUudmFsdWUuc2xpY2UoMCwgbWF0Y2guaW5kZXgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBsZXQgZmxhdHRlbmVkID0gbnVsbDtcbiAgICAgIGlmICghaWQpIHtcbiAgICAgICAgZmxhdHRlbmVkID8/PSBmbGF0dGVuTm9kZShoZWFkaW5nKTtcbiAgICAgICAgaWQgPSBkZWZhdWx0U2x1ZyA/IGRlZmF1bHRTbHVnKHJvb3QsIGhlYWRpbmcsIGZsYXR0ZW5lZCkgOiBzbHVnZ2VyLnNsdWcoZmxhdHRlbmVkKTtcbiAgICAgIH1cbiAgICAgIGhlYWRpbmcuZGF0YS5oUHJvcGVydGllcy5pZCA9IGlkO1xuICAgICAgaWYgKGdlbmVyYXRlVG9jKSB7XG4gICAgICAgIHRvYy5wdXNoKHtcbiAgICAgICAgICB0aXRsZTogZmxhdHRlbmVkID8/IGZsYXR0ZW5Ob2RlKGhlYWRpbmcpLFxuICAgICAgICAgIHVybDogYCMke2lkfWAsXG4gICAgICAgICAgZGVwdGg6IGhlYWRpbmcuZGVwdGhcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gXCJza2lwXCI7XG4gICAgfSk7XG4gICAgaWYgKGdlbmVyYXRlVG9jKSBmaWxlLmRhdGEudG9jID0gdG9jO1xuICB9O1xufVxuXG5leHBvcnQge1xuICBmbGF0dGVuTm9kZSxcbiAgcmVtYXJrSGVhZGluZ1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/link.js":
/*!*****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/link.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/node_modules/fumadocs-core/dist/link.js",
"default",
));


/***/ }),

/***/ "(rsc)/../../node_modules/fumadocs-core/dist/server/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/server/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageTree: () => (/* binding */ page_tree_exports),\n/* harmony export */   createMetadataImage: () => (/* binding */ createMetadataImage),\n/* harmony export */   findNeighbour: () => (/* binding */ findNeighbour),\n/* harmony export */   flattenTree: () => (/* binding */ flattenTree),\n/* harmony export */   getGithubLastEdit: () => (/* binding */ getGithubLastEdit),\n/* harmony export */   getPageTreePeers: () => (/* binding */ getPageTreePeers),\n/* harmony export */   getPageTreeRoots: () => (/* binding */ getPageTreeRoots),\n/* harmony export */   getTableOfContents: () => (/* binding */ getTableOfContents),\n/* harmony export */   separatePageTree: () => (/* binding */ separatePageTree)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-Y2774T3B.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var remark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark */ \"(rsc)/../../node_modules/remark/index.js\");\n\n\n\n// src/server/get-toc.ts\n\nfunction getTableOfContents(content, remarkPlugins) {\n  if (remarkPlugins) {\n    return (0,remark__WEBPACK_IMPORTED_MODULE_2__.remark)().use(remarkPlugins).use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).process(content).then((result2) => {\n      if (\"toc\" in result2.data) return result2.data.toc;\n      return [];\n    });\n  }\n  const result = (0,remark__WEBPACK_IMPORTED_MODULE_2__.remark)().use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).processSync(content);\n  if (\"toc\" in result.data) return result.data.toc;\n  return [];\n}\n\n// src/utils/page-tree.tsx\nfunction flattenTree(tree) {\n  return tree.flatMap((node) => {\n    if (node.type === \"separator\") return [];\n    if (node.type === \"folder\") {\n      const child = flattenTree(node.children);\n      if (node.index) return [node.index, ...child];\n      return child;\n    }\n    return [node];\n  });\n}\nfunction findNeighbour(tree, url, options) {\n  const { separateRoot = true } = options ?? {};\n  const roots = separateRoot ? getPageTreeRoots(tree) : [tree];\n  for (const root of roots) {\n    const list = flattenTree(root.children);\n    for (let i = 0; i < list.length; i++) {\n      if (list[i].url === url) {\n        return {\n          next: list[i + 1],\n          previous: list[i - 1]\n        };\n      }\n    }\n  }\n  return {};\n}\nfunction getPageTreeRoots(pageTree) {\n  const result = pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    const roots = getPageTreeRoots(child);\n    if (child.root) {\n      roots.push(child);\n    }\n    return roots;\n  });\n  if (!(\"type\" in pageTree)) result.push(pageTree);\n  return result;\n}\nfunction separatePageTree(pageTree) {\n  return pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    return {\n      name: child.name,\n      url: child.index?.url,\n      children: child.children\n    };\n  });\n}\nfunction getPageTreePeers(tree, url) {\n  const parent = findParentFromTree(tree, url);\n  if (!parent) return [];\n  return parent.children.filter(\n    (item) => item.type === \"page\" && item.url !== url\n  );\n}\nfunction findParentFromTree(node, url) {\n  if (\"index\" in node && node.index?.url === url) {\n    return node;\n  }\n  for (const child of node.children) {\n    if (child.type === \"folder\") {\n      const parent = findParentFromTree(child, url);\n      if (parent) return parent;\n    }\n    if (child.type === \"page\" && child.url === url) {\n      return node;\n    }\n  }\n}\n\n// src/server/page-tree.ts\nvar page_tree_exports = {};\n\n// src/server/git-api.ts\nasync function getGithubLastEdit({\n  repo,\n  token,\n  owner,\n  path,\n  sha,\n  options = {},\n  params: customParams = {}\n}) {\n  const headers = new Headers(options.headers);\n  const params = new URLSearchParams();\n  params.set(\"path\", path);\n  params.set(\"page\", \"1\");\n  params.set(\"per_page\", \"1\");\n  if (sha) params.set(\"sha\", sha);\n  for (const [key, value] of Object.entries(customParams)) {\n    params.set(key, value);\n  }\n  if (token) {\n    headers.append(\"authorization\", token);\n  }\n  const res = await fetch(\n    `https://api.github.com/repos/${owner}/${repo}/commits?${params.toString()}`,\n    {\n      cache: \"force-cache\",\n      ...options,\n      headers\n    }\n  );\n  if (!res.ok)\n    throw new Error(\n      `Failed to fetch last edit time from Git ${await res.text()}`\n    );\n  const data = await res.json();\n  if (data.length === 0) return null;\n  return new Date(data[0].commit.committer.date);\n}\n\n// src/server/metadata.ts\nfunction createMetadataImage(options) {\n  const { filename = \"image.png\", imageRoute = \"/docs-og\" } = options;\n  function getImageMeta(slugs) {\n    return {\n      alt: \"Banner\",\n      url: `/${[...imageRoute.split(\"/\"), ...slugs, filename].filter((v) => v.length > 0).join(\"/\")}`,\n      width: 1200,\n      height: 630\n    };\n  }\n  return {\n    getImageMeta,\n    withImage(slugs, data) {\n      const imageData = getImageMeta(slugs);\n      return {\n        ...data,\n        openGraph: {\n          images: imageData,\n          ...data?.openGraph\n        },\n        twitter: {\n          images: imageData,\n          card: \"summary_large_image\",\n          ...data?.twitter\n        }\n      };\n    },\n    generateParams() {\n      return options.source.generateParams().map((params) => ({\n        ...params,\n        slug: [...params.slug, filename]\n      }));\n    },\n    createAPI(handler) {\n      return async (req, args) => {\n        const params = await args.params;\n        if (!params || !(\"slug\" in params) || params.slug === void 0)\n          throw new Error(`Invalid params: ${JSON.stringify(params)}`);\n        const lang = \"lang\" in params && typeof params.lang === \"string\" ? params.lang : void 0;\n        const input = {\n          slug: Array.isArray(params.slug) ? params.slug : [params.slug],\n          lang\n        };\n        const page = options.source.getPage(\n          input.slug.slice(0, -1),\n          //remove filename\n          lang\n        );\n        if (!page)\n          return new Response(null, {\n            status: 404\n          });\n        return handler(page, req, { params: input });\n      };\n    }\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9zZXJ2ZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRThCO0FBQ0E7O0FBRTlCO0FBQ2dDO0FBQ2hDO0FBQ0E7QUFDQSxXQUFXLDhDQUFNLDBCQUEwQiw2REFBYTtBQUN4RDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLDhDQUFNLE9BQU8sNkRBQWE7QUFDM0M7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxVQUFVLHNCQUFzQjtBQUNoQztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsTUFBTSxHQUFHLEtBQUssV0FBVyxrQkFBa0I7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxpQkFBaUI7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVSxrREFBa0Q7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsZUFBZSxxRkFBcUY7QUFDcEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2Qyx1QkFBdUI7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsb0NBQW9DLGVBQWU7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFXRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9zZXJ2ZXIvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgcmVtYXJrSGVhZGluZ1xufSBmcm9tIFwiLi4vY2h1bmstWTI3NzRUM0IuanNcIjtcbmltcG9ydCBcIi4uL2NodW5rLUpTQlJESkJFLmpzXCI7XG5cbi8vIHNyYy9zZXJ2ZXIvZ2V0LXRvYy50c1xuaW1wb3J0IHsgcmVtYXJrIH0gZnJvbSBcInJlbWFya1wiO1xuZnVuY3Rpb24gZ2V0VGFibGVPZkNvbnRlbnRzKGNvbnRlbnQsIHJlbWFya1BsdWdpbnMpIHtcbiAgaWYgKHJlbWFya1BsdWdpbnMpIHtcbiAgICByZXR1cm4gcmVtYXJrKCkudXNlKHJlbWFya1BsdWdpbnMpLnVzZShyZW1hcmtIZWFkaW5nKS5wcm9jZXNzKGNvbnRlbnQpLnRoZW4oKHJlc3VsdDIpID0+IHtcbiAgICAgIGlmIChcInRvY1wiIGluIHJlc3VsdDIuZGF0YSkgcmV0dXJuIHJlc3VsdDIuZGF0YS50b2M7XG4gICAgICByZXR1cm4gW107XG4gICAgfSk7XG4gIH1cbiAgY29uc3QgcmVzdWx0ID0gcmVtYXJrKCkudXNlKHJlbWFya0hlYWRpbmcpLnByb2Nlc3NTeW5jKGNvbnRlbnQpO1xuICBpZiAoXCJ0b2NcIiBpbiByZXN1bHQuZGF0YSkgcmV0dXJuIHJlc3VsdC5kYXRhLnRvYztcbiAgcmV0dXJuIFtdO1xufVxuXG4vLyBzcmMvdXRpbHMvcGFnZS10cmVlLnRzeFxuZnVuY3Rpb24gZmxhdHRlblRyZWUodHJlZSkge1xuICByZXR1cm4gdHJlZS5mbGF0TWFwKChub2RlKSA9PiB7XG4gICAgaWYgKG5vZGUudHlwZSA9PT0gXCJzZXBhcmF0b3JcIikgcmV0dXJuIFtdO1xuICAgIGlmIChub2RlLnR5cGUgPT09IFwiZm9sZGVyXCIpIHtcbiAgICAgIGNvbnN0IGNoaWxkID0gZmxhdHRlblRyZWUobm9kZS5jaGlsZHJlbik7XG4gICAgICBpZiAobm9kZS5pbmRleCkgcmV0dXJuIFtub2RlLmluZGV4LCAuLi5jaGlsZF07XG4gICAgICByZXR1cm4gY2hpbGQ7XG4gICAgfVxuICAgIHJldHVybiBbbm9kZV07XG4gIH0pO1xufVxuZnVuY3Rpb24gZmluZE5laWdoYm91cih0cmVlLCB1cmwsIG9wdGlvbnMpIHtcbiAgY29uc3QgeyBzZXBhcmF0ZVJvb3QgPSB0cnVlIH0gPSBvcHRpb25zID8/IHt9O1xuICBjb25zdCByb290cyA9IHNlcGFyYXRlUm9vdCA/IGdldFBhZ2VUcmVlUm9vdHModHJlZSkgOiBbdHJlZV07XG4gIGZvciAoY29uc3Qgcm9vdCBvZiByb290cykge1xuICAgIGNvbnN0IGxpc3QgPSBmbGF0dGVuVHJlZShyb290LmNoaWxkcmVuKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpc3QubGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmIChsaXN0W2ldLnVybCA9PT0gdXJsKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgbmV4dDogbGlzdFtpICsgMV0sXG4gICAgICAgICAgcHJldmlvdXM6IGxpc3RbaSAtIDFdXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiB7fTtcbn1cbmZ1bmN0aW9uIGdldFBhZ2VUcmVlUm9vdHMocGFnZVRyZWUpIHtcbiAgY29uc3QgcmVzdWx0ID0gcGFnZVRyZWUuY2hpbGRyZW4uZmxhdE1hcCgoY2hpbGQpID0+IHtcbiAgICBpZiAoY2hpbGQudHlwZSAhPT0gXCJmb2xkZXJcIikgcmV0dXJuIFtdO1xuICAgIGNvbnN0IHJvb3RzID0gZ2V0UGFnZVRyZWVSb290cyhjaGlsZCk7XG4gICAgaWYgKGNoaWxkLnJvb3QpIHtcbiAgICAgIHJvb3RzLnB1c2goY2hpbGQpO1xuICAgIH1cbiAgICByZXR1cm4gcm9vdHM7XG4gIH0pO1xuICBpZiAoIShcInR5cGVcIiBpbiBwYWdlVHJlZSkpIHJlc3VsdC5wdXNoKHBhZ2VUcmVlKTtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIHNlcGFyYXRlUGFnZVRyZWUocGFnZVRyZWUpIHtcbiAgcmV0dXJuIHBhZ2VUcmVlLmNoaWxkcmVuLmZsYXRNYXAoKGNoaWxkKSA9PiB7XG4gICAgaWYgKGNoaWxkLnR5cGUgIT09IFwiZm9sZGVyXCIpIHJldHVybiBbXTtcbiAgICByZXR1cm4ge1xuICAgICAgbmFtZTogY2hpbGQubmFtZSxcbiAgICAgIHVybDogY2hpbGQuaW5kZXg/LnVybCxcbiAgICAgIGNoaWxkcmVuOiBjaGlsZC5jaGlsZHJlblxuICAgIH07XG4gIH0pO1xufVxuZnVuY3Rpb24gZ2V0UGFnZVRyZWVQZWVycyh0cmVlLCB1cmwpIHtcbiAgY29uc3QgcGFyZW50ID0gZmluZFBhcmVudEZyb21UcmVlKHRyZWUsIHVybCk7XG4gIGlmICghcGFyZW50KSByZXR1cm4gW107XG4gIHJldHVybiBwYXJlbnQuY2hpbGRyZW4uZmlsdGVyKFxuICAgIChpdGVtKSA9PiBpdGVtLnR5cGUgPT09IFwicGFnZVwiICYmIGl0ZW0udXJsICE9PSB1cmxcbiAgKTtcbn1cbmZ1bmN0aW9uIGZpbmRQYXJlbnRGcm9tVHJlZShub2RlLCB1cmwpIHtcbiAgaWYgKFwiaW5kZXhcIiBpbiBub2RlICYmIG5vZGUuaW5kZXg/LnVybCA9PT0gdXJsKSB7XG4gICAgcmV0dXJuIG5vZGU7XG4gIH1cbiAgZm9yIChjb25zdCBjaGlsZCBvZiBub2RlLmNoaWxkcmVuKSB7XG4gICAgaWYgKGNoaWxkLnR5cGUgPT09IFwiZm9sZGVyXCIpIHtcbiAgICAgIGNvbnN0IHBhcmVudCA9IGZpbmRQYXJlbnRGcm9tVHJlZShjaGlsZCwgdXJsKTtcbiAgICAgIGlmIChwYXJlbnQpIHJldHVybiBwYXJlbnQ7XG4gICAgfVxuICAgIGlmIChjaGlsZC50eXBlID09PSBcInBhZ2VcIiAmJiBjaGlsZC51cmwgPT09IHVybCkge1xuICAgICAgcmV0dXJuIG5vZGU7XG4gICAgfVxuICB9XG59XG5cbi8vIHNyYy9zZXJ2ZXIvcGFnZS10cmVlLnRzXG52YXIgcGFnZV90cmVlX2V4cG9ydHMgPSB7fTtcblxuLy8gc3JjL3NlcnZlci9naXQtYXBpLnRzXG5hc3luYyBmdW5jdGlvbiBnZXRHaXRodWJMYXN0RWRpdCh7XG4gIHJlcG8sXG4gIHRva2VuLFxuICBvd25lcixcbiAgcGF0aCxcbiAgc2hhLFxuICBvcHRpb25zID0ge30sXG4gIHBhcmFtczogY3VzdG9tUGFyYW1zID0ge31cbn0pIHtcbiAgY29uc3QgaGVhZGVycyA9IG5ldyBIZWFkZXJzKG9wdGlvbnMuaGVhZGVycyk7XG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgcGFyYW1zLnNldChcInBhdGhcIiwgcGF0aCk7XG4gIHBhcmFtcy5zZXQoXCJwYWdlXCIsIFwiMVwiKTtcbiAgcGFyYW1zLnNldChcInBlcl9wYWdlXCIsIFwiMVwiKTtcbiAgaWYgKHNoYSkgcGFyYW1zLnNldChcInNoYVwiLCBzaGEpO1xuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhjdXN0b21QYXJhbXMpKSB7XG4gICAgcGFyYW1zLnNldChrZXksIHZhbHVlKTtcbiAgfVxuICBpZiAodG9rZW4pIHtcbiAgICBoZWFkZXJzLmFwcGVuZChcImF1dGhvcml6YXRpb25cIiwgdG9rZW4pO1xuICB9XG4gIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFxuICAgIGBodHRwczovL2FwaS5naXRodWIuY29tL3JlcG9zLyR7b3duZXJ9LyR7cmVwb30vY29tbWl0cz8ke3BhcmFtcy50b1N0cmluZygpfWAsXG4gICAge1xuICAgICAgY2FjaGU6IFwiZm9yY2UtY2FjaGVcIixcbiAgICAgIC4uLm9wdGlvbnMsXG4gICAgICBoZWFkZXJzXG4gICAgfVxuICApO1xuICBpZiAoIXJlcy5vaylcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBgRmFpbGVkIHRvIGZldGNoIGxhc3QgZWRpdCB0aW1lIGZyb20gR2l0ICR7YXdhaXQgcmVzLnRleHQoKX1gXG4gICAgKTtcbiAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKCk7XG4gIGlmIChkYXRhLmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7XG4gIHJldHVybiBuZXcgRGF0ZShkYXRhWzBdLmNvbW1pdC5jb21taXR0ZXIuZGF0ZSk7XG59XG5cbi8vIHNyYy9zZXJ2ZXIvbWV0YWRhdGEudHNcbmZ1bmN0aW9uIGNyZWF0ZU1ldGFkYXRhSW1hZ2Uob3B0aW9ucykge1xuICBjb25zdCB7IGZpbGVuYW1lID0gXCJpbWFnZS5wbmdcIiwgaW1hZ2VSb3V0ZSA9IFwiL2RvY3Mtb2dcIiB9ID0gb3B0aW9ucztcbiAgZnVuY3Rpb24gZ2V0SW1hZ2VNZXRhKHNsdWdzKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGFsdDogXCJCYW5uZXJcIixcbiAgICAgIHVybDogYC8ke1suLi5pbWFnZVJvdXRlLnNwbGl0KFwiL1wiKSwgLi4uc2x1Z3MsIGZpbGVuYW1lXS5maWx0ZXIoKHYpID0+IHYubGVuZ3RoID4gMCkuam9pbihcIi9cIil9YCxcbiAgICAgIHdpZHRoOiAxMjAwLFxuICAgICAgaGVpZ2h0OiA2MzBcbiAgICB9O1xuICB9XG4gIHJldHVybiB7XG4gICAgZ2V0SW1hZ2VNZXRhLFxuICAgIHdpdGhJbWFnZShzbHVncywgZGF0YSkge1xuICAgICAgY29uc3QgaW1hZ2VEYXRhID0gZ2V0SW1hZ2VNZXRhKHNsdWdzKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLmRhdGEsXG4gICAgICAgIG9wZW5HcmFwaDoge1xuICAgICAgICAgIGltYWdlczogaW1hZ2VEYXRhLFxuICAgICAgICAgIC4uLmRhdGE/Lm9wZW5HcmFwaFxuICAgICAgICB9LFxuICAgICAgICB0d2l0dGVyOiB7XG4gICAgICAgICAgaW1hZ2VzOiBpbWFnZURhdGEsXG4gICAgICAgICAgY2FyZDogXCJzdW1tYXJ5X2xhcmdlX2ltYWdlXCIsXG4gICAgICAgICAgLi4uZGF0YT8udHdpdHRlclxuICAgICAgICB9XG4gICAgICB9O1xuICAgIH0sXG4gICAgZ2VuZXJhdGVQYXJhbXMoKSB7XG4gICAgICByZXR1cm4gb3B0aW9ucy5zb3VyY2UuZ2VuZXJhdGVQYXJhbXMoKS5tYXAoKHBhcmFtcykgPT4gKHtcbiAgICAgICAgLi4ucGFyYW1zLFxuICAgICAgICBzbHVnOiBbLi4ucGFyYW1zLnNsdWcsIGZpbGVuYW1lXVxuICAgICAgfSkpO1xuICAgIH0sXG4gICAgY3JlYXRlQVBJKGhhbmRsZXIpIHtcbiAgICAgIHJldHVybiBhc3luYyAocmVxLCBhcmdzKSA9PiB7XG4gICAgICAgIGNvbnN0IHBhcmFtcyA9IGF3YWl0IGFyZ3MucGFyYW1zO1xuICAgICAgICBpZiAoIXBhcmFtcyB8fCAhKFwic2x1Z1wiIGluIHBhcmFtcykgfHwgcGFyYW1zLnNsdWcgPT09IHZvaWQgMClcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgcGFyYW1zOiAke0pTT04uc3RyaW5naWZ5KHBhcmFtcyl9YCk7XG4gICAgICAgIGNvbnN0IGxhbmcgPSBcImxhbmdcIiBpbiBwYXJhbXMgJiYgdHlwZW9mIHBhcmFtcy5sYW5nID09PSBcInN0cmluZ1wiID8gcGFyYW1zLmxhbmcgOiB2b2lkIDA7XG4gICAgICAgIGNvbnN0IGlucHV0ID0ge1xuICAgICAgICAgIHNsdWc6IEFycmF5LmlzQXJyYXkocGFyYW1zLnNsdWcpID8gcGFyYW1zLnNsdWcgOiBbcGFyYW1zLnNsdWddLFxuICAgICAgICAgIGxhbmdcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgcGFnZSA9IG9wdGlvbnMuc291cmNlLmdldFBhZ2UoXG4gICAgICAgICAgaW5wdXQuc2x1Zy5zbGljZSgwLCAtMSksXG4gICAgICAgICAgLy9yZW1vdmUgZmlsZW5hbWVcbiAgICAgICAgICBsYW5nXG4gICAgICAgICk7XG4gICAgICAgIGlmICghcGFnZSlcbiAgICAgICAgICByZXR1cm4gbmV3IFJlc3BvbnNlKG51bGwsIHtcbiAgICAgICAgICAgIHN0YXR1czogNDA0XG4gICAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBoYW5kbGVyKHBhZ2UsIHJlcSwgeyBwYXJhbXM6IGlucHV0IH0pO1xuICAgICAgfTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBwYWdlX3RyZWVfZXhwb3J0cyBhcyBQYWdlVHJlZSxcbiAgY3JlYXRlTWV0YWRhdGFJbWFnZSxcbiAgZmluZE5laWdoYm91cixcbiAgZmxhdHRlblRyZWUsXG4gIGdldEdpdGh1Ykxhc3RFZGl0LFxuICBnZXRQYWdlVHJlZVBlZXJzLFxuICBnZXRQYWdlVHJlZVJvb3RzLFxuICBnZXRUYWJsZU9mQ29udGVudHMsXG4gIHNlcGFyYXRlUGFnZVRyZWVcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/fumadocs-core/dist/server/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/breadcrumb.js":
/*!***********************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/breadcrumb.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBreadcrumbItems: () => (/* binding */ getBreadcrumbItems),\n/* harmony export */   getBreadcrumbItemsFromPath: () => (/* binding */ getBreadcrumbItemsFromPath),\n/* harmony export */   searchPath: () => (/* binding */ searchPath),\n/* harmony export */   useBreadcrumb: () => (/* binding */ useBreadcrumb)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/breadcrumb.tsx\n\nfunction useBreadcrumb(url, tree, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => getBreadcrumbItems(url, tree, options),\n    [tree, url, options]\n  );\n}\nfunction getBreadcrumbItems(url, tree, options = {}) {\n  return getBreadcrumbItemsFromPath(\n    tree,\n    searchPath(tree.children, url) ?? [],\n    options\n  );\n}\nfunction getBreadcrumbItemsFromPath(tree, path, options) {\n  const { includePage = true, includeSeparator = false, includeRoot } = options;\n  let items = [];\n  path.forEach((item, i) => {\n    if (item.type === \"separator\" && item.name && includeSeparator) {\n      items.push({\n        name: item.name\n      });\n    }\n    if (item.type === \"folder\") {\n      const next = path.at(i + 1);\n      if (next && item.index === next) return;\n      if (item.root) {\n        items = [];\n        return;\n      }\n      items.push({\n        name: item.name,\n        url: item.index?.url\n      });\n    }\n    if (item.type === \"page\" && includePage) {\n      items.push({\n        name: item.name,\n        url: item.url\n      });\n    }\n  });\n  if (includeRoot) {\n    items.unshift({\n      name: tree.name,\n      url: typeof includeRoot === \"object\" ? includeRoot.url : void 0\n    });\n  }\n  return items;\n}\nfunction searchPath(nodes, url) {\n  if (url.endsWith(\"/\")) url = url.slice(0, -1);\n  let separator;\n  for (const node of nodes) {\n    if (node.type === \"separator\") separator = node;\n    if (node.type === \"folder\") {\n      if (node.index?.url === url) {\n        const items2 = [];\n        if (separator) items2.push(separator);\n        items2.push(node, node.index);\n        return items2;\n      }\n      const items = searchPath(node.children, url);\n      if (items) {\n        items.unshift(node);\n        if (separator) items.unshift(separator);\n        return items;\n      }\n    }\n    if (node.type === \"page\" && node.url === url) {\n      const items = [];\n      if (separator) items.push(separator);\n      items.push(node);\n      return items;\n    }\n  }\n  return null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/breadcrumb.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link2)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/link.tsx\n\n\nvar Link2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n  ({\n    href = \"#\",\n    external = !(href.startsWith(\"/\") || href.startsWith(\"#\") || href.startsWith(\".\")),\n    prefetch,\n    ...props\n  }, ref) => {\n    if (external) {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"a\",\n        {\n          ref,\n          href,\n          rel: \"noreferrer noopener\",\n          target: \"_blank\",\n          ...props,\n          children: props.children\n        }\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link, { ref, href, prefetch, ...props });\n  }\n);\nLink2.displayName = \"Link\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay01U1UyTzVBUy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRTZCOztBQUU3QjtBQUNtQztBQUNLO0FBQ3hDLFlBQVksaURBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDZCQUE2QixzREFBRztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLENBQUMsb0RBQUksSUFBSSwrQkFBK0I7QUFDdEU7QUFDQTtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTVTVTJPNUFTLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIExpbmtcbn0gZnJvbSBcIi4vY2h1bmstQkJQN01JTzQuanNcIjtcblxuLy8gc3JjL2xpbmsudHN4XG5pbXBvcnQgeyBmb3J3YXJkUmVmIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBMaW5rMiA9IGZvcndhcmRSZWYoXG4gICh7XG4gICAgaHJlZiA9IFwiI1wiLFxuICAgIGV4dGVybmFsID0gIShocmVmLnN0YXJ0c1dpdGgoXCIvXCIpIHx8IGhyZWYuc3RhcnRzV2l0aChcIiNcIikgfHwgaHJlZi5zdGFydHNXaXRoKFwiLlwiKSksXG4gICAgcHJlZmV0Y2gsXG4gICAgLi4ucHJvcHNcbiAgfSwgcmVmKSA9PiB7XG4gICAgaWYgKGV4dGVybmFsKSB7XG4gICAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgXCJhXCIsXG4gICAgICAgIHtcbiAgICAgICAgICByZWYsXG4gICAgICAgICAgaHJlZixcbiAgICAgICAgICByZWw6IFwibm9yZWZlcnJlciBub29wZW5lclwiLFxuICAgICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgICBjaGlsZHJlbjogcHJvcHMuY2hpbGRyZW5cbiAgICAgICAgfVxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goTGluaywgeyByZWYsIGhyZWYsIHByZWZldGNoLCAuLi5wcm9wcyB9KTtcbiAgfVxuKTtcbkxpbmsyLmRpc3BsYXlOYW1lID0gXCJMaW5rXCI7XG5cbmV4cG9ydCB7XG4gIExpbmsyIGFzIExpbmtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* binding */ FrameworkProvider),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useParams: () => (/* binding */ useParams),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/framework/index.tsx\n\n\nvar notImplemented = () => {\n  throw new Error(\n    \"You need to wrap your application inside `FrameworkProvider`.\"\n  );\n};\nvar FrameworkContext = createContext(\"FrameworkContext\", {\n  useParams: notImplemented,\n  useRouter: notImplemented,\n  usePathname: notImplemented\n});\nfunction FrameworkProvider({\n  Link: Link2,\n  useRouter: useRouter2,\n  useParams: useParams2,\n  usePathname: usePathname2,\n  Image: Image2,\n  children\n}) {\n  const framework = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n    () => ({\n      usePathname: usePathname2,\n      useRouter: useRouter2,\n      Link: Link2,\n      Image: Image2,\n      useParams: useParams2\n    }),\n    [Link2, usePathname2, useRouter2, useParams2, Image2]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FrameworkContext.Provider, { value: framework, children });\n}\nfunction usePathname() {\n  return FrameworkContext.use().usePathname();\n}\nfunction useRouter() {\n  return FrameworkContext.use().useRouter();\n}\nfunction useParams() {\n  return FrameworkContext.use().useParams();\n}\nfunction Image(props) {\n  const { Image: Image2 } = FrameworkContext.use();\n  if (!Image2) {\n    const { src, alt, priority, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"img\",\n      {\n        alt,\n        src,\n        fetchPriority: priority ? \"high\" : \"auto\",\n        ...rest\n      }\n    );\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Image2, { ...props });\n}\nfunction Link(props) {\n  const { Link: Link2 } = FrameworkContext.use();\n  if (!Link2) {\n    const { href, prefetch: _, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"a\", { href, ...rest });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Link2, { ...props });\n}\nfunction createContext(name, v) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(v);\n  return {\n    Provider: (props) => {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value: props.value, children: props.children });\n    },\n    use: (errorMessage) => {\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (!value)\n        throw new Error(\n          errorMessage ?? `Provider of ${name} is required but missing.`\n        );\n      return value;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* binding */ useOnChange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-on-change.ts\n\nfunction isDifferent(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferent(v, b[i]));\n  }\n  return a !== b;\n}\nfunction useOnChange(value, onChange, isUpdated = isDifferent) {\n  const [prev, setPrev] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n  if (isUpdated(prev, value)) {\n    onChange(value, prev);\n    setPrev(value);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1FTVdHVFhTVy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ2lDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLCtDQUFRO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstRU1XR1RYU1cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL3VzZS1vbi1jaGFuZ2UudHNcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBpc0RpZmZlcmVudChhLCBiKSB7XG4gIGlmIChBcnJheS5pc0FycmF5KGEpICYmIEFycmF5LmlzQXJyYXkoYikpIHtcbiAgICByZXR1cm4gYi5sZW5ndGggIT09IGEubGVuZ3RoIHx8IGEuc29tZSgodiwgaSkgPT4gaXNEaWZmZXJlbnQodiwgYltpXSkpO1xuICB9XG4gIHJldHVybiBhICE9PSBiO1xufVxuZnVuY3Rpb24gdXNlT25DaGFuZ2UodmFsdWUsIG9uQ2hhbmdlLCBpc1VwZGF0ZWQgPSBpc0RpZmZlcmVudCkge1xuICBjb25zdCBbcHJldiwgc2V0UHJldl0gPSB1c2VTdGF0ZSh2YWx1ZSk7XG4gIGlmIChpc1VwZGF0ZWQocHJldiwgdmFsdWUpKSB7XG4gICAgb25DaGFuZ2UodmFsdWUsIHByZXYpO1xuICAgIHNldFByZXYodmFsdWUpO1xuICB9XG59XG5cbmV4cG9ydCB7XG4gIHVzZU9uQ2hhbmdlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js":
/*!***************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __commonJS: () => (/* binding */ __commonJS),\n/* harmony export */   __toESM: () => (/* binding */ __toESM)\n/* harmony export */ });\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1KU0JSREpCRS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELGFBQWE7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qiw0RkFBNEY7QUFDekg7QUFDQTtBQUNBO0FBQ0EsbUdBQW1HO0FBQ25HO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUVBQXlFLDhCQUE4QjtBQUN2RztBQUNBOztBQUtFIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUpTQlJESkJFLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX2NyZWF0ZSA9IE9iamVjdC5jcmVhdGU7XG52YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZ2V0T3duUHJvcERlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yO1xudmFyIF9fZ2V0T3duUHJvcE5hbWVzID0gT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXM7XG52YXIgX19nZXRQcm90b09mID0gT2JqZWN0LmdldFByb3RvdHlwZU9mO1xudmFyIF9faGFzT3duUHJvcCA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHk7XG52YXIgX19jb21tb25KUyA9IChjYiwgbW9kKSA9PiBmdW5jdGlvbiBfX3JlcXVpcmUoKSB7XG4gIHJldHVybiBtb2QgfHwgKDAsIGNiW19fZ2V0T3duUHJvcE5hbWVzKGNiKVswXV0pKChtb2QgPSB7IGV4cG9ydHM6IHt9IH0pLmV4cG9ydHMsIG1vZCksIG1vZC5leHBvcnRzO1xufTtcbnZhciBfX2NvcHlQcm9wcyA9ICh0bywgZnJvbSwgZXhjZXB0LCBkZXNjKSA9PiB7XG4gIGlmIChmcm9tICYmIHR5cGVvZiBmcm9tID09PSBcIm9iamVjdFwiIHx8IHR5cGVvZiBmcm9tID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICBmb3IgKGxldCBrZXkgb2YgX19nZXRPd25Qcm9wTmFtZXMoZnJvbSkpXG4gICAgICBpZiAoIV9faGFzT3duUHJvcC5jYWxsKHRvLCBrZXkpICYmIGtleSAhPT0gZXhjZXB0KVxuICAgICAgICBfX2RlZlByb3AodG8sIGtleSwgeyBnZXQ6ICgpID0+IGZyb21ba2V5XSwgZW51bWVyYWJsZTogIShkZXNjID0gX19nZXRPd25Qcm9wRGVzYyhmcm9tLCBrZXkpKSB8fCBkZXNjLmVudW1lcmFibGUgfSk7XG4gIH1cbiAgcmV0dXJuIHRvO1xufTtcbnZhciBfX3RvRVNNID0gKG1vZCwgaXNOb2RlTW9kZSwgdGFyZ2V0KSA9PiAodGFyZ2V0ID0gbW9kICE9IG51bGwgPyBfX2NyZWF0ZShfX2dldFByb3RvT2YobW9kKSkgOiB7fSwgX19jb3B5UHJvcHMoXG4gIC8vIElmIHRoZSBpbXBvcnRlciBpcyBpbiBub2RlIGNvbXBhdGliaWxpdHkgbW9kZSBvciB0aGlzIGlzIG5vdCBhbiBFU01cbiAgLy8gZmlsZSB0aGF0IGhhcyBiZWVuIGNvbnZlcnRlZCB0byBhIENvbW1vbkpTIGZpbGUgdXNpbmcgYSBCYWJlbC1cbiAgLy8gY29tcGF0aWJsZSB0cmFuc2Zvcm0gKGkuZS4gXCJfX2VzTW9kdWxlXCIgaGFzIG5vdCBiZWVuIHNldCksIHRoZW4gc2V0XG4gIC8vIFwiZGVmYXVsdFwiIHRvIHRoZSBDb21tb25KUyBcIm1vZHVsZS5leHBvcnRzXCIgZm9yIG5vZGUgY29tcGF0aWJpbGl0eS5cbiAgaXNOb2RlTW9kZSB8fCAhbW9kIHx8ICFtb2QuX19lc01vZHVsZSA/IF9fZGVmUHJvcCh0YXJnZXQsIFwiZGVmYXVsdFwiLCB7IHZhbHVlOiBtb2QsIGVudW1lcmFibGU6IHRydWUgfSkgOiB0YXJnZXQsXG4gIG1vZFxuKSk7XG5cbmV4cG9ydCB7XG4gIF9fY29tbW9uSlMsXG4gIF9fdG9FU01cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/framework/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/framework/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider),\n/* harmony export */   Image: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Image),\n/* harmony export */   Link: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link),\n/* harmony export */   createContext: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.createContext),\n/* harmony export */   useParams: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useParams),\n/* harmony export */   usePathname: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.usePathname),\n/* harmony export */   useRouter: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* __next_internal_client_entry_do_not_use__ FrameworkProvider,Image,Link,createContext,useParams,usePathname,useRouter auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9mcmFtZXdvcmsvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O2dJQVM4QjtBQUNBO0FBUzVCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7XG4gIEZyYW1ld29ya1Byb3ZpZGVyLFxuICBJbWFnZSxcbiAgTGluayxcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlUGFyYW1zLFxuICB1c2VQYXRobmFtZSxcbiAgdXNlUm91dGVyXG59IGZyb20gXCIuLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuaW1wb3J0IFwiLi4vY2h1bmstSlNCUkRKQkUuanNcIjtcbmV4cG9ydCB7XG4gIEZyYW1ld29ya1Byb3ZpZGVyLFxuICBJbWFnZSxcbiAgTGluayxcbiAgY3JlYXRlQ29udGV4dCxcbiAgdXNlUGFyYW1zLFxuICB1c2VQYXRobmFtZSxcbiAgdXNlUm91dGVyXG59O1xuIl0sIm5hbWVzIjpbIkZyYW1ld29ya1Byb3ZpZGVyIiwiSW1hZ2UiLCJMaW5rIiwiY3JlYXRlQ29udGV4dCIsInVzZVBhcmFtcyIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/framework/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/link.js":
/*!*****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/link.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__.Link)\n/* harmony export */ });\n/* harmony import */ var _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5SU2O5AS.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\");\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9saW5rLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7NkRBRzZCO0FBQ0E7QUFDQTtBQUczQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9saW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHtcbiAgTGlua1xufSBmcm9tIFwiLi9jaHVuay01U1UyTzVBUy5qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuaW1wb3J0IFwiLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuZXhwb3J0IHtcbiAgTGluayBhcyBkZWZhdWx0XG59O1xuIl0sIm5hbWVzIjpbIkxpbmsiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/link.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/toc.js":
/*!****************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/toc.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnchorProvider: () => (/* binding */ AnchorProvider),\n/* harmony export */   ScrollProvider: () => (/* binding */ ScrollProvider),\n/* harmony export */   TOCItem: () => (/* binding */ TOCItem),\n/* harmony export */   useActiveAnchor: () => (/* binding */ useActiveAnchor),\n/* harmony export */   useActiveAnchors: () => (/* binding */ useActiveAnchors)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-EMWGTXSW.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! scroll-into-view-if-needed */ \"(ssr)/../../node_modules/scroll-into-view-if-needed/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ AnchorProvider,ScrollProvider,TOCItem,useActiveAnchor,useActiveAnchors auto */ \n\n// src/toc.tsx\n\n\n// src/utils/merge-refs.ts\nfunction mergeRefs(...refs) {\n    return (value)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === \"function\") {\n                ref(value);\n            } else if (ref !== null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n// src/utils/use-anchor-observer.ts\n\nfunction useAnchorObserver(watch, single) {\n    const [activeAnchor, setActiveAnchor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useAnchorObserver.useEffect\": ()=>{\n            let visible = [];\n            const observer = new IntersectionObserver({\n                \"useAnchorObserver.useEffect\": (entries)=>{\n                    for (const entry of entries){\n                        if (entry.isIntersecting && !visible.includes(entry.target.id)) {\n                            visible = [\n                                ...visible,\n                                entry.target.id\n                            ];\n                        } else if (!entry.isIntersecting && visible.includes(entry.target.id)) {\n                            visible = visible.filter({\n                                \"useAnchorObserver.useEffect\": (v)=>v !== entry.target.id\n                            }[\"useAnchorObserver.useEffect\"]);\n                        }\n                    }\n                    if (visible.length > 0) setActiveAnchor(visible);\n                }\n            }[\"useAnchorObserver.useEffect\"], {\n                rootMargin: single ? \"-80px 0% -70% 0%\" : `-20px 0% -40% 0%`,\n                threshold: 1\n            });\n            function onScroll() {\n                const element = document.scrollingElement;\n                if (!element) return;\n                const top = element.scrollTop;\n                if (top <= 0 && single) setActiveAnchor(watch.slice(0, 1));\n                else if (top + element.clientHeight >= element.scrollHeight - 6) {\n                    setActiveAnchor({\n                        \"useAnchorObserver.useEffect.onScroll\": (active)=>{\n                            return active.length > 0 && !single ? watch.slice(watch.indexOf(active[0])) : watch.slice(-1);\n                        }\n                    }[\"useAnchorObserver.useEffect.onScroll\"]);\n                }\n            }\n            for (const heading of watch){\n                const element = document.getElementById(heading);\n                if (element) observer.observe(element);\n            }\n            onScroll();\n            window.addEventListener(\"scroll\", onScroll);\n            return ({\n                \"useAnchorObserver.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", onScroll);\n                    observer.disconnect();\n                }\n            })[\"useAnchorObserver.useEffect\"];\n        }\n    }[\"useAnchorObserver.useEffect\"], [\n        single,\n        watch\n    ]);\n    return single ? activeAnchor.slice(0, 1) : activeAnchor;\n}\n// src/toc.tsx\n\nvar ActiveAnchorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)([]);\nvar ScrollContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    current: null\n});\nfunction useActiveAnchor() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ActiveAnchorContext).at(-1);\n}\nfunction useActiveAnchors() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ActiveAnchorContext);\n}\nfunction ScrollProvider({ containerRef, children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ScrollContext.Provider, {\n        value: containerRef,\n        children\n    });\n}\nfunction AnchorProvider({ toc, single = true, children }) {\n    const headings = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"AnchorProvider.useMemo[headings]\": ()=>{\n            return toc.map({\n                \"AnchorProvider.useMemo[headings]\": (item)=>item.url.split(\"#\")[1]\n            }[\"AnchorProvider.useMemo[headings]\"]);\n        }\n    }[\"AnchorProvider.useMemo[headings]\"], [\n        toc\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ActiveAnchorContext.Provider, {\n        value: useAnchorObserver(headings, single),\n        children\n    });\n}\nvar TOCItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ onActiveChange, ...props }, ref)=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ScrollContext);\n    const anchors = useActiveAnchors();\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const mergedRef = mergeRefs(anchorRef, ref);\n    const isActive = anchors.includes(props.href.slice(1));\n    (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(isActive, {\n        \"TOCItem.useOnChange\": (v)=>{\n            const element = anchorRef.current;\n            if (!element) return;\n            if (v && containerRef.current) {\n                (0,scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(element, {\n                    behavior: \"smooth\",\n                    block: \"center\",\n                    inline: \"center\",\n                    scrollMode: \"always\",\n                    boundary: containerRef.current\n                });\n            }\n            onActiveChange?.(v);\n        }\n    }[\"TOCItem.useOnChange\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"a\", {\n        ref: mergedRef,\n        \"data-active\": isActive,\n        ...props,\n        children: props.children\n    });\n});\nTOCItem.displayName = \"TOCItem\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/toc.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/utils/use-effect-event.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/utils/use-effect-event.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ useEffectEvent auto */ \n// src/utils/use-effect-event.ts\n\nfunction useEffectEvent(callback) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(callback);\n    ref.current = callback;\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useEffectEvent.useCallback\": (...params)=>ref.current(...params)\n    }[\"useEffectEvent.useCallback\"], []);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztvRUFDOEI7QUFFOUIsZ0NBQWdDO0FBQ1k7QUFDNUMsU0FBU0UsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxNQUFNSCw2Q0FBTUEsQ0FBQ0U7SUFDbkJDLElBQUlDLE9BQU8sR0FBR0Y7SUFDZCxPQUFPSCxrREFBV0E7c0NBQUMsQ0FBQyxHQUFHTSxTQUFXRixJQUFJQyxPQUFPLElBQUlDO3FDQUFTLEVBQUU7QUFDOUQ7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IFwiLi4vY2h1bmstSlNCUkRKQkUuanNcIjtcblxuLy8gc3JjL3V0aWxzL3VzZS1lZmZlY3QtZXZlbnQudHNcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZihjYWxsYmFjayk7XG4gIHJlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIHJldHVybiB1c2VDYWxsYmFjaygoLi4ucGFyYW1zKSA9PiByZWYuY3VycmVudCguLi5wYXJhbXMpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VFZmZlY3RFdmVudFxufTtcbiJdLCJuYW1lcyI6WyJ1c2VDYWxsYmFjayIsInVzZVJlZiIsInVzZUVmZmVjdEV2ZW50IiwiY2FsbGJhY2siLCJyZWYiLCJjdXJyZW50IiwicGFyYW1zIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/utils/use-effect-event.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/fumadocs-core/dist/utils/use-on-change.js":
/*!********************************************************************!*\
  !*** ../../node_modules/fumadocs-core/dist/utils/use-on-change.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* reexport safe */ _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var _chunk_JSBRDJBE_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-JSBRDJBE.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/chunk-JSBRDJBE.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2Utb24tY2hhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUU4QjtBQUNBO0FBRzVCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1vbi1jaGFuZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn0gZnJvbSBcIi4uL2NodW5rLUVNV0dUWFNXLmpzXCI7XG5pbXBvcnQgXCIuLi9jaHVuay1KU0JSREpCRS5qc1wiO1xuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/fumadocs-core/dist/utils/use-on-change.js\n");

/***/ })

};
;