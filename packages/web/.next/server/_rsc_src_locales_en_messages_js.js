"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_locales_en_messages_js";
exports.ids = ["_rsc_src_locales_en_messages_js"];
exports.modules = {

/***/ "(rsc)/./src/locales/en/messages.js":
/*!************************************!*\
  !*** ./src/locales/en/messages.js ***!
  \************************************/
/***/ ((module) => {

eval("/*eslint-disable*/ \nmodule.exports = {\n    messages: JSON.parse(\"{\\\"app.title\\\":[\\\"OnlyRules - AI Prompt Management Platform\\\"],\\\"app.description\\\":[\\\"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.\\\"],\\\"nav.home\\\":[\\\"Home\\\"],\\\"nav.templates\\\":[\\\"Templates\\\"],\\\"nav.dashboard\\\":[\\\"Dashboard\\\"],\\\"nav.tutorials\\\":[\\\"Tutorials\\\"],\\\"hero.title\\\":[\\\"Supercharge Your IDE with AI Prompts\\\"],\\\"hero.subtitle\\\":[\\\"Discover, create, and share powerful AI prompt rules for Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, and more. Join the community of developers optimizing their coding workflow.\\\"],\\\"hero.getStarted\\\":[\\\"Get Started\\\"],\\\"hero.browseTemplates\\\":[\\\"Browse Templates\\\"],\\\"features.title\\\":[\\\"Everything You Need for AI-Powered Coding\\\"],\\\"features.subtitle\\\":[\\\"From prompt templates to version control, we've got you covered\\\"],\\\"auth.signIn\\\":[\\\"Sign In\\\"],\\\"auth.signUp\\\":[\\\"Sign Up\\\"],\\\"auth.signOut\\\":[\\\"Sign Out\\\"]}\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbG9jYWxlcy9lbi9tZXNzYWdlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBRUEsT0FBT0MsT0FBTyxHQUFDO0lBQUNDLFVBQVNDLEtBQUtDLEtBQUssQ0FBQztBQUF1OEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9zcmMvbG9jYWxlcy9lbi9tZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKmVzbGludC1kaXNhYmxlKi9tb2R1bGUuZXhwb3J0cz17bWVzc2FnZXM6SlNPTi5wYXJzZShcIntcXFwiYXBwLnRpdGxlXFxcIjpbXFxcIk9ubHlSdWxlcyAtIEFJIFByb21wdCBNYW5hZ2VtZW50IFBsYXRmb3JtXFxcIl0sXFxcImFwcC5kZXNjcmlwdGlvblxcXCI6W1xcXCJDcmVhdGUsIG9yZ2FuaXplLCBhbmQgc2hhcmUgQUkgcHJvbXB0IHJ1bGVzIGZvciB5b3VyIGZhdm9yaXRlIElERXMuIEJvb3N0IHlvdXIgY29kaW5nIHByb2R1Y3Rpdml0eSB3aXRoIGNvbW11bml0eS1kcml2ZW4gdGVtcGxhdGVzLlxcXCJdLFxcXCJuYXYuaG9tZVxcXCI6W1xcXCJIb21lXFxcIl0sXFxcIm5hdi50ZW1wbGF0ZXNcXFwiOltcXFwiVGVtcGxhdGVzXFxcIl0sXFxcIm5hdi5kYXNoYm9hcmRcXFwiOltcXFwiRGFzaGJvYXJkXFxcIl0sXFxcIm5hdi50dXRvcmlhbHNcXFwiOltcXFwiVHV0b3JpYWxzXFxcIl0sXFxcImhlcm8udGl0bGVcXFwiOltcXFwiU3VwZXJjaGFyZ2UgWW91ciBJREUgd2l0aCBBSSBQcm9tcHRzXFxcIl0sXFxcImhlcm8uc3VidGl0bGVcXFwiOltcXFwiRGlzY292ZXIsIGNyZWF0ZSwgYW5kIHNoYXJlIHBvd2VyZnVsIEFJIHByb21wdCBydWxlcyBmb3IgQ3Vyc29yLCBBdWdtZW50IENvZGUsIFdpbmRzdXJmLCBDbGF1ZGUsIEdpdEh1YiBDb3BpbG90LCBHZW1pbmksIGFuZCBtb3JlLiBKb2luIHRoZSBjb21tdW5pdHkgb2YgZGV2ZWxvcGVycyBvcHRpbWl6aW5nIHRoZWlyIGNvZGluZyB3b3JrZmxvdy5cXFwiXSxcXFwiaGVyby5nZXRTdGFydGVkXFxcIjpbXFxcIkdldCBTdGFydGVkXFxcIl0sXFxcImhlcm8uYnJvd3NlVGVtcGxhdGVzXFxcIjpbXFxcIkJyb3dzZSBUZW1wbGF0ZXNcXFwiXSxcXFwiZmVhdHVyZXMudGl0bGVcXFwiOltcXFwiRXZlcnl0aGluZyBZb3UgTmVlZCBmb3IgQUktUG93ZXJlZCBDb2RpbmdcXFwiXSxcXFwiZmVhdHVyZXMuc3VidGl0bGVcXFwiOltcXFwiRnJvbSBwcm9tcHQgdGVtcGxhdGVzIHRvIHZlcnNpb24gY29udHJvbCwgd2UndmUgZ290IHlvdSBjb3ZlcmVkXFxcIl0sXFxcImF1dGguc2lnbkluXFxcIjpbXFxcIlNpZ24gSW5cXFwiXSxcXFwiYXV0aC5zaWduVXBcXFwiOltcXFwiU2lnbiBVcFxcXCJdLFxcXCJhdXRoLnNpZ25PdXRcXFwiOltcXFwiU2lnbiBPdXRcXFwiXX1cIil9OyJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwibWVzc2FnZXMiLCJKU09OIiwicGFyc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/locales/en/messages.js\n");

/***/ })

};
;