"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_zh-CN_messages_js";
exports.ids = ["_ssr_src_locales_zh-CN_messages_js"];
exports.modules = {

/***/ "(ssr)/./src/locales/zh-CN/messages.js":
/*!***************************************!*\
  !*** ./src/locales/zh-CN/messages.js ***!
  \***************************************/
/***/ ((module) => {

eval("/*eslint-disable*/ \nmodule.exports = {\n    messages: JSON.parse(\"{\\\"app.title\\\":[\\\"OnlyRules - AI 提示词管理平台\\\"],\\\"app.description\\\":[\\\"为您喜爱的 IDE 创建、整理和分享 AI 提示词规则。通过社区驱动的模板提升您的编码效率。\\\"],\\\"nav.home\\\":[\\\"首页\\\"],\\\"nav.templates\\\":[\\\"模板\\\"],\\\"nav.dashboard\\\":[\\\"控制台\\\"],\\\"nav.tutorials\\\":[\\\"教程\\\"],\\\"hero.title\\\":[\\\"用 AI 提示词为您的 IDE 赋能\\\"],\\\"hero.subtitle\\\":[\\\"发现、创建和分享强大的 AI 提示词规则，适用于 Cursor、Augment Code、Windsurf、Claude、GitHub Copilot、Gemini 等。加入优化编码工作流程的开发者社区。\\\"],\\\"hero.getStarted\\\":[\\\"开始使用\\\"],\\\"hero.browseTemplates\\\":[\\\"浏览模板\\\"],\\\"features.title\\\":[\\\"AI 驱动编码所需的一切\\\"],\\\"features.subtitle\\\":[\\\"从提示词模板到版本控制，我们为您提供全方位支持\\\"],\\\"auth.signIn\\\":[\\\"登录\\\"],\\\"auth.signUp\\\":[\\\"注册\\\"],\\\"auth.signOut\\\":[\\\"退出登录\\\"]}\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbG9jYWxlcy96aC1DTi9tZXNzYWdlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBRUEsT0FBT0MsT0FBTyxHQUFDO0lBQUNDLFVBQVNDLEtBQUtDLEtBQUssQ0FBQztBQUFrbkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9zcmMvbG9jYWxlcy96aC1DTi9tZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKmVzbGludC1kaXNhYmxlKi9tb2R1bGUuZXhwb3J0cz17bWVzc2FnZXM6SlNPTi5wYXJzZShcIntcXFwiYXBwLnRpdGxlXFxcIjpbXFxcIk9ubHlSdWxlcyAtIEFJIOaPkOekuuivjeeuoeeQhuW5s+WPsFxcXCJdLFxcXCJhcHAuZGVzY3JpcHRpb25cXFwiOltcXFwi5Li65oKo5Zac54ix55qEIElERSDliJvlu7rjgIHmlbTnkIblkozliIbkuqsgQUkg5o+Q56S66K+N6KeE5YiZ44CC6YCa6L+H56S+5Yy66amx5Yqo55qE5qih5p2/5o+Q5Y2H5oKo55qE57yW56CB5pWI546H44CCXFxcIl0sXFxcIm5hdi5ob21lXFxcIjpbXFxcIummlumhtVxcXCJdLFxcXCJuYXYudGVtcGxhdGVzXFxcIjpbXFxcIuaooeadv1xcXCJdLFxcXCJuYXYuZGFzaGJvYXJkXFxcIjpbXFxcIuaOp+WItuWPsFxcXCJdLFxcXCJuYXYudHV0b3JpYWxzXFxcIjpbXFxcIuaVmeeoi1xcXCJdLFxcXCJoZXJvLnRpdGxlXFxcIjpbXFxcIueUqCBBSSDmj5DnpLror43kuLrmgqjnmoQgSURFIOi1i+iDvVxcXCJdLFxcXCJoZXJvLnN1YnRpdGxlXFxcIjpbXFxcIuWPkeeOsOOAgeWIm+W7uuWSjOWIhuS6q+W8uuWkp+eahCBBSSDmj5DnpLror43op4TliJnvvIzpgILnlKjkuo4gQ3Vyc29y44CBQXVnbWVudCBDb2Rl44CBV2luZHN1cmbjgIFDbGF1ZGXjgIFHaXRIdWIgQ29waWxvdOOAgUdlbWluaSDnrYnjgILliqDlhaXkvJjljJbnvJbnoIHlt6XkvZzmtYHnqIvnmoTlvIDlj5HogIXnpL7ljLrjgIJcXFwiXSxcXFwiaGVyby5nZXRTdGFydGVkXFxcIjpbXFxcIuW8gOWni+S9v+eUqFxcXCJdLFxcXCJoZXJvLmJyb3dzZVRlbXBsYXRlc1xcXCI6W1xcXCLmtY/op4jmqKHmnb9cXFwiXSxcXFwiZmVhdHVyZXMudGl0bGVcXFwiOltcXFwiQUkg6amx5Yqo57yW56CB5omA6ZyA55qE5LiA5YiHXFxcIl0sXFxcImZlYXR1cmVzLnN1YnRpdGxlXFxcIjpbXFxcIuS7juaPkOekuuivjeaooeadv+WIsOeJiOacrOaOp+WItu+8jOaIkeS7rOS4uuaCqOaPkOS+m+WFqOaWueS9jeaUr+aMgVxcXCJdLFxcXCJhdXRoLnNpZ25JblxcXCI6W1xcXCLnmbvlvZVcXFwiXSxcXFwiYXV0aC5zaWduVXBcXFwiOltcXFwi5rOo5YaMXFxcIl0sXFxcImF1dGguc2lnbk91dFxcXCI6W1xcXCLpgIDlh7rnmbvlvZVcXFwiXX1cIil9OyJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwibWVzc2FnZXMiLCJKU09OIiwicGFyc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/zh-CN/messages.js\n");

/***/ })

};
;