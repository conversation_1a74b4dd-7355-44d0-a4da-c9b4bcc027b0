"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_zh-HK_messages_js";
exports.ids = ["_ssr_src_locales_zh-HK_messages_js"];
exports.modules = {

/***/ "(ssr)/./src/locales/zh-HK/messages.js":
/*!***************************************!*\
  !*** ./src/locales/zh-HK/messages.js ***!
  \***************************************/
/***/ ((module) => {

eval("/*eslint-disable*/ \nmodule.exports = {\n    messages: JSON.parse(\"{\\\"app.title\\\":[\\\"OnlyRules - AI 提示詞管理平台\\\"],\\\"app.description\\\":[\\\"為您喜愛的 IDE 創建、整理和分享 AI 提示詞規則。通過社區驅動的模板提升您的編碼效率。\\\"],\\\"nav.home\\\":[\\\"首頁\\\"],\\\"nav.templates\\\":[\\\"模板\\\"],\\\"nav.dashboard\\\":[\\\"控制台\\\"],\\\"nav.tutorials\\\":[\\\"教程\\\"],\\\"hero.title\\\":[\\\"用 AI 提示詞為您的 IDE 賦能\\\"],\\\"hero.subtitle\\\":[\\\"發現、創建和分享強大的 AI 提示詞規則，適用於 Cursor、Augment Code、Windsurf、Claude、GitHub Copilot、Gemini 等。加入優化編碼工作流程的開發者社區。\\\"],\\\"hero.getStarted\\\":[\\\"開始使用\\\"],\\\"hero.browseTemplates\\\":[\\\"瀏覽模板\\\"],\\\"features.title\\\":[\\\"AI 驅動編碼所需的一切\\\"],\\\"features.subtitle\\\":[\\\"從提示詞模板到版本控制，我們為您提供全方位支持\\\"],\\\"auth.signIn\\\":[\\\"登入\\\"],\\\"auth.signUp\\\":[\\\"註冊\\\"],\\\"auth.signOut\\\":[\\\"登出\\\"]}\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbG9jYWxlcy96aC1ISy9tZXNzYWdlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBRUEsT0FBT0MsT0FBTyxHQUFDO0lBQUNDLFVBQVNDLEtBQUtDLEtBQUssQ0FBQztBQUFnbkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9zcmMvbG9jYWxlcy96aC1ISy9tZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKmVzbGludC1kaXNhYmxlKi9tb2R1bGUuZXhwb3J0cz17bWVzc2FnZXM6SlNPTi5wYXJzZShcIntcXFwiYXBwLnRpdGxlXFxcIjpbXFxcIk9ubHlSdWxlcyAtIEFJIOaPkOekuuipnueuoeeQhuW5s+WPsFxcXCJdLFxcXCJhcHAuZGVzY3JpcHRpb25cXFwiOltcXFwi54K65oKo5Zac5oSb55qEIElERSDlibXlu7rjgIHmlbTnkIblkozliIbkuqsgQUkg5o+Q56S66Kme6KaP5YmH44CC6YCa6YGO56S+5Y2A6amF5YuV55qE5qih5p2/5o+Q5Y2H5oKo55qE57eo56K85pWI546H44CCXFxcIl0sXFxcIm5hdi5ob21lXFxcIjpbXFxcIummlumggVxcXCJdLFxcXCJuYXYudGVtcGxhdGVzXFxcIjpbXFxcIuaooeadv1xcXCJdLFxcXCJuYXYuZGFzaGJvYXJkXFxcIjpbXFxcIuaOp+WItuWPsFxcXCJdLFxcXCJuYXYudHV0b3JpYWxzXFxcIjpbXFxcIuaVmeeoi1xcXCJdLFxcXCJoZXJvLnRpdGxlXFxcIjpbXFxcIueUqCBBSSDmj5DnpLroqZ7ngrrmgqjnmoQgSURFIOizpuiDvVxcXCJdLFxcXCJoZXJvLnN1YnRpdGxlXFxcIjpbXFxcIueZvOePvuOAgeWJteW7uuWSjOWIhuS6q+W8t+Wkp+eahCBBSSDmj5DnpLroqZ7opo/liYfvvIzpgannlKjmlrwgQ3Vyc29y44CBQXVnbWVudCBDb2Rl44CBV2luZHN1cmbjgIFDbGF1ZGXjgIFHaXRIdWIgQ29waWxvdOOAgUdlbWluaSDnrYnjgILliqDlhaXlhKrljJbnt6jnorzlt6XkvZzmtYHnqIvnmoTplovnmbzogIXnpL7ljYDjgIJcXFwiXSxcXFwiaGVyby5nZXRTdGFydGVkXFxcIjpbXFxcIumWi+Wni+S9v+eUqFxcXCJdLFxcXCJoZXJvLmJyb3dzZVRlbXBsYXRlc1xcXCI6W1xcXCLngI/opr3mqKHmnb9cXFwiXSxcXFwiZmVhdHVyZXMudGl0bGVcXFwiOltcXFwiQUkg6amF5YuV57eo56K85omA6ZyA55qE5LiA5YiHXFxcIl0sXFxcImZlYXR1cmVzLnN1YnRpdGxlXFxcIjpbXFxcIuW+nuaPkOekuuipnuaooeadv+WIsOeJiOacrOaOp+WItu+8jOaIkeWAkeeCuuaCqOaPkOS+m+WFqOaWueS9jeaUr+aMgVxcXCJdLFxcXCJhdXRoLnNpZ25JblxcXCI6W1xcXCLnmbvlhaVcXFwiXSxcXFwiYXV0aC5zaWduVXBcXFwiOltcXFwi6Ki75YaKXFxcIl0sXFxcImF1dGguc2lnbk91dFxcXCI6W1xcXCLnmbvlh7pcXFwiXX1cIil9OyJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwibWVzc2FnZXMiLCJKU09OIiwicGFyc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/zh-HK/messages.js\n");

/***/ })

};
;