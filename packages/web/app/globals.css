@tailwind base;
@tailwind components;
@tailwind utilities;
/* @import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css'; */

@import "@radix-ui/themes/styles.css";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Font family configuration */
.radix-themes {
  --default-font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  font-family: var(--default-font-family);
}

/* Custom utility classes for mobile-first responsive design */
@layer utilities {
  /* Mobile container with responsive padding */
  .mobile-container {
    @apply w-full mx-auto px-4;
    @apply xs:px-6 sm:px-8 md:px-12 lg:px-16 xl:px-20;
    max-width: 1400px;
  }

  /* Touch-friendly target size for mobile interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
    @apply flex items-center justify-center;
    @apply touch-manipulation;
  }
}

