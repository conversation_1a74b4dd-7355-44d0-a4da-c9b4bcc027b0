"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { Theme } from "@radix-ui/themes";
import { useTheme } from "next-themes";

// Custom hook to handle hydration-safe theme detection
function useHydrationSafeTheme() {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR and initial hydration, always return dark theme
  // This ensures server and client render the same content initially
  if (!mounted) {
    return "dark";
  }

  // After hydration, return the actual theme
  const resolvedTheme = theme === "system" ? (systemTheme || "dark") : theme;
  return (resolvedTheme as "light" | "dark") || "dark";
}

function RadixThemeWrapper({ children }: { children: React.ReactNode }) {
  const appearance = useHydrationSafeTheme();

  return (
    <Theme
      accentColor="blue"
      grayColor="slate"
      radius="medium"
      scaling="100%"
      appearance={appearance}
      panelBackground="translucent"
      hasBackground={false}
    >
      {children}
    </Theme>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      defaultTheme="dark"
      enableSystem={true}
      attribute="class"
      disableTransitionOnChange={false}
      {...props}
    >
      <RadixThemeWrapper>{children}</RadixThemeWrapper>
    </NextThemesProvider>
  );
}